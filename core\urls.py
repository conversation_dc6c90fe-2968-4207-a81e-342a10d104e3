# D:\school_fees_saas_v2\core\urls.py

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

# --- Platform Administration URLs (Accessed on the main domain, e.g., myapp.test) ---
# These are for superusers managing the entire platform.
platform_admin_patterns = [
    path('dashboard/', include(('apps.platform_management.urls', 'platform_management_dashboard_ns'), namespace='platform_dashboard')), # Example if dashboard is separate
    path('', include(('apps.platform_management.urls', 'platform_management'), namespace='platform_management')), # General platform settings, etc.
    path('announcements/', include(('apps.announcements.urls', 'platform_announcements'), namespace='platform_announcements_admin')), # Platform-wide announcements CRUD
    # Add other platform-level admin URLs here (e.g., manage tenants, subscriptions plans)
]

# --- Publicly Accessible URLs (Accessed on the main domain, e.g., myapp.test) ---
# For landing pages, public info, user registration for the platform, tenant creation.
public_patterns = [
    path('platform-accounts/', include('apps.users.urls', namespace='users')), # Platform admin login, school owner login
    path('platform-accounts/', include('django.contrib.auth.urls')), # Password reset, etc. for platform users
    path('tenant-lifecycle/', include('apps.tenants.urls', namespace='tenants')), # School registration
    path('subscriptions/', include('apps.subscriptions.urls', namespace='subscriptions')), # Public view of plans, etc.
    path('', include('apps.public_site.urls', namespace='public_site')), # Homepage, about, contact
]

# --- Tenant Portal Prefixed URLs (Accessed on tenant subdomains, e.g., alpha.myapp.test/portal/) ---
# These are apps whose URLs will all start with /portal/ on a tenant domain.
tenant_portal_prefixed_patterns = [
    # path('', RedirectView.as_view(pattern_name='schools:dashboard', permanent=False), name='tenant_portal_home'), # Redirect /portal/ to schools:dashboard
    path('', include('apps.schools.urls', namespace='schools')), # Mount schools directly under /portal/
                                                                # So schools:dashboard -> /portal/dashboard/
                                                                # schools:staff_list -> /portal/staff/
    path('auth/', include('apps.portal_auth.urls', namespace='portal_auth')), # /portal/auth/
    path('announcements/', include(('apps.announcements.urls', 'tenant_announcements_ns'), namespace='tenant_announcements')), # /portal/announcements/ (tenant-specific display)
    # If other apps are consistently under /portal/, add them here:
    # path('hr/', include('apps.hr.urls', namespace='hr_portal')), # e.g. /portal/hr/
]

# --- Tenant Specific App URLs (Accessed on tenant subdomains, with their own distinct prefixes) ---
# These apps have their own top-level prefix on the tenant domain, NOT under /portal/.
tenant_specific_app_patterns = [
    path('parents/', include('apps.parent_portal.urls', namespace='parent_portal')),         # e.g., alpha.myapp.test/parents/
    path('tenant-students/', include('apps.students.urls', namespace='students')),           # e.g., alpha.myapp.test/tenant-students/
    path('tenant-fees/', include('apps.fees.urls', namespace='fees')),                       # e.g., alpha.myapp.test/tenant-fees/
    path('tenant-hr/', include('apps.hr.urls', namespace='hr')),                             # e.g., alpha.myapp.test/tenant-hr/
    path('tenant-finance/', include('apps.finance.urls', namespace='finance')),               # e.g., alpha.myapp.test/tenant-finance/
    path('tenant-accounting/', include('apps.accounting.urls', namespace='accounting')),       # e.g., alpha.myapp.test/tenant-accounting/
    path('tenant-payments/', include('apps.payments.urls', namespace='payments')),             # e.g., alpha.myapp.test/tenant-payments/
    path('tenant-portal-admin/', include('apps.portal_admin.urls', namespace='portal_admin')),# e.g., alpha.myapp.test/tenant-portal-admin/
    path('tenant-communication/', include('apps.communication.urls', namespace='communication')), # e.g., alpha.myapp.test/tenant-communication/
    path('tenant-reporting/', include('apps.reporting.urls', namespace='reporting')),         # e.g., alpha.myapp.test/tenant-reporting/
    path('common-filters/', include('apps.common.urls', namespace='common_filters')),       # e.g., alpha.myapp.test/common-filters/ (actions, not usually browsed)
]


urlpatterns = [
    # Django Admin (usually for superuser on public schema)
    path('admin/', admin.site.urls), 

    # Platform Administration (for superusers, on main domain)
    path('platform-admin/', include(platform_admin_patterns)),

    # Public facing site and platform user accounts (on main domain)
    path('', include(public_patterns)),
    
    # Tenant Portal prefixed apps (these paths will be active on tenant subdomains)
    path('portal/', include(tenant_portal_prefixed_patterns)),

    # Other Tenant Specific apps (these paths will also be active on tenant subdomains)
    path('', include(tenant_specific_app_patterns)), # Mount them at the root of the tenant domain
]


if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    if 'debug_toolbar' in settings.INSTALLED_APPS:
        import debug_toolbar
        urlpatterns = [
            path('__debug__/', include(debug_toolbar.urls)),
        ] + urlpatterns
        
        




# # D:\school_fees_saas_v2\core\urls.py

# from django.contrib import admin
# from django.urls import path, include # Make sure 'include' is imported
# from django.conf import settings      # Import settings
# from django.conf.urls.static import static

# urlpatterns = [
#     # --- Your existing URL patterns ---
#     # Public specific URLs
#     path('admin/', admin.site.urls), 
    
#     path('platform/', include('apps.platform_management.urls')),
    
#     path('platform-accounts/', include('apps.users.urls', namespace='users')), 
#     path('tenant-lifecycle/', include('apps.tenants.urls', namespace='tenants')),
    
#     path('platform-accounts/', include('django.contrib.auth.urls')), # <<< ADD THIS LINE

#     path('', include('apps.public_site.urls', namespace='public_site')),

#     # Tenant-specific URL prefixes
#     path('portal/', include('apps.schools.urls', namespace='schools')), 
    
#     path('portal/auth/', include('apps.portal_auth.urls', namespace='portal_auth')),
        
#     path('parents/', include('apps.parent_portal.urls', namespace='parent_portal')), 
    
    
    
#     # --- ADD THIS LINE FOR PARENT PORTAL ---
#     # path('parent-portal/', include('apps.parent_portal.urls', namespace='parent_portal')), 
#     # --- END ADDITION ---
    
#     path('tenant-fees/', include('apps.fees.urls', namespace='fees')),
#     path('tenant-students/', include('apps.students.urls', namespace='students')),
#     path('tenant-hr/', include('apps.hr.urls', namespace='hr')),
#     path('tenant-finance/', include('apps.finance.urls', namespace='finance')),
#     path('tenant-accounting/', include('apps.accounting.urls', namespace='accounting')),
#     path('tenant-payments/', include('apps.payments.urls', namespace='payments')),
#     path('tenant-portal-admin/', include('apps.portal_admin.urls', namespace='portal_admin')),
    
#     # path('tenant-announcements/', include('apps.announcements.urls', namespace='announcements')),
#     path('portal/announcements/', include('apps.announcements.urls', namespace='announcements_tenant')),
    
#     # path('platform-administration/announcements/', include('apps.announcements.urls', namespace='announcements')),
#     # path('platform/', include('apps.platform_management.urls', namespace='platform_management')),
    
#     path('platform-admin/dashboard/', include('apps.platform_management.urls', namespace='platform_management')), 
#     path('platform-admin/announcements/', include('apps.announcements.urls', namespace='announcements')),
    
#     path('tenant-communication/', include('apps.communication.urls', namespace='communication')),
#     path('tenant-reporting/', include('apps.reporting.urls', namespace='reporting')),
#     path('common-filters/', include('apps.common.urls', namespace='common_filters')), # Example
#     # ... any other app includes ...
    
#     path('subscriptions/', include('apps.subscriptions.urls', namespace='subscriptions')),
# ]


# if settings.DEBUG:
#     urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
#     if 'debug_toolbar' in settings.INSTALLED_APPS:
#         import debug_toolbar
#         urlpatterns = [
#             path('__debug__/', include(debug_toolbar.urls)),
#         ] + urlpatterns

    
    
