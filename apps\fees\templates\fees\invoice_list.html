{# D:\school_fees_saas_v2\templates\fees\invoice_list.html #}
{% extends "tenant_base.html" %}

{% load static humanize fees_tags widget_tweaks %} {# Load all necessary tags #}

{% block title %}{{ view_title|default:"Manage Invoices" }} - {{ request.tenant.name }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        .status-badge { font-size: 0.8em; padding: 0.4em 0.7em; }
        .actions-column .btn { margin-right: 0.25rem; margin-bottom: 0.25rem; }
        .table th, .table td { vertical-align: middle; }
        /* Additional styles from _report_filter_export_card can be merged or kept separate */
    </style>
{% endblock %}

{% block tenant_specific_content %} {# Or your main content block name #}
<div class="container-fluid mt-4">
    {# Breadcrumbs if you have them #}
    {# {% include "partials/_breadcrumb.html" %} #}

    <div class="d-flex flex-wrap justify-content-between align-items-center mb-3">
        <h1 class="h3 mb-0 text-gray-800 me-3">{{ view_title|default:"Manage Invoices" }}</h1>
        <div class="d-flex align-items-center gap-2 mt-2 mt-md-0">
            {% if perms.fees.add_invoice %}
                <a href="{% url 'fees:invoice_create_manual' %}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle-fill me-1"></i> Create Manual Invoice
                </a>
                <a href="{% url 'fees:generate_structure_invoices' %}" class="btn btn-success btn-sm">
                    <i class="bi bi-receipt-cutoff me-1"></i> Generate by Structure
                </a>
            {% endif %}
        </div>
    </div>

    {% include "partials/_messages.html" %}

    {# --- Include Filter Form and Export Card --- #}
    {# This partial expects 'filter_form', 'export_csv_url', etc. in context #}
    {# The InvoiceListView and BaseReportViewMixin should provide these #}
    {% include "reporting/_report_filter_export_card.html" %}

    <div class="card shadow-sm">
        <div class="card-header bg-light py-3">
            <h6 class="m-0 font-weight-bold text-primary">Invoice Records</h6>
        </div>
        <div class="card-body">
            {% if invoices %} {# 'invoices' is context_object_name from ListView #}
                <div class="table-responsive">
                    <table class="table table-hover table-striped table-bordered align-middle table-sm">
                        <thead class="table-light">
                            <tr>
                                <th scope="col" style="width: 10%;">Invoice #</th>
                                <th scope="col" style="width: 20%;">Student</th>
                                <th scope="col" style="width: 10%;">Issue Date</th>
                                <th scope="col" style="width: 10%;">Due Date</th>
                                <th scope="col" class="text-end" style="width: 10%;">Total</th>
                                <th scope="col" class="text-end" style="width: 8%;">Discount</th>
                                <th scope="col" class="text-end" style="width: 8%;">Paid</th>
                                <th scope="col" class="text-end" style="width: 10%;">Balance Due</th>
                                <th scope="col" class="text-center" style="width: 9%;">Status</th>
                                <th scope="col" class="text-center" style="width: 15%;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in invoices %}
                            <tr>
                                <td>
                                    <a href="{{ invoice.get_absolute_url }}"><strong>{{ invoice.invoice_number_display|default:invoice.pk }}</strong></a>
                                </td>
                                <td>
                                    {% if invoice.student %}
                                        <a href="{% url 'students:student_detail' invoice.student.pk %}">{{ invoice.student.full_name }}</a>
                                        <small class="d-block text-muted">{{ invoice.student.admission_number }} | {{ invoice.student.current_class.name|default:"" }}</small>
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </td>
                                <td>{{ invoice.issue_date|date:"d M Y" }}</td>
                                <td>{{ invoice.due_date|date:"d M Y" }}</td>
                                <td class="text-end fw-bold">{{ invoice.total_amount|intcomma }}</td>
                                <td class="text-end">{{ invoice.discount_applied|intcomma }}</td>
                                <td class="text-end text-success">{{ invoice.amount_paid|intcomma }}</td>
                                <td class="text-end {% if invoice.balance_due > 0 %}text-danger fw-bold{% else %}text-muted{% endif %}">
                                    {{ invoice.balance_due|intcomma }}
                                </td>
                                <td class="text-center">
                                    {% invoice_status_badge invoice.status %}
                                </td>
                                <td class="text-center actions-column">
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="actionsMenu{{invoice.pk}}" data-bs-toggle="dropdown" aria-expanded="false">
                                            Actions
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="actionsMenu{{invoice.pk}}">
                                            <li><a class="dropdown-item" href="{{ invoice.get_absolute_url }}"><i class="bi bi-eye me-2"></i>View Details</a></li>
                                            <li><a class="dropdown-item" href="{% url 'fees:invoice_pdf' invoice.pk %}" target="_blank"><i class="bi bi-file-earmark-pdf me-2"></i>View/Print PDF</a></li>
                                            
                                            {# Manual Invoice Editing - Check if it's a manually created one or if editable #}
                                            {% if invoice.fee_structure is None and perms.fees.change_invoice %} {# Example: Only edit if no fee_structure (manual) #}
                                                <li><a class="dropdown-item" href="{% url 'fees:invoice_update' pk=invoice.pk %}"><i class="bi bi-pencil-square me-2"></i>Edit Invoice</a></li>
                                            {% elif invoice.is_editable and perms.fees.change_invoice %}
                                                <li><a class="dropdown-item" href="{% url 'fees:invoice_update' pk=invoice.pk %}"><i class="bi bi-pencil-square me-2"></i>Edit Draft</a></li>
                                            {% endif %}

                                            {% if invoice.is_payable %}
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item" href="{% url 'payments:record_payment' %}?invoice={{ invoice.pk }}"><i class="bi bi-cash-coin me-2"></i>Record Payment</a></li>
                                            {% endif %}
                                            
                                            {# Delete only if it's a draft and user has permission #}
                                            {% if invoice.status == invoice.STATUS_DRAFT and perms.fees.delete_invoice %}
                                                <li><hr class="dropdown-divider"></li>
                                                {# Use a form for delete to make it a POST request #}
                                                <li>
                                                    <form action="{% url 'fees:invoice_delete_manual' invoice.pk %}" method="post" class="d-inline">
                                                        {% csrf_token %}
                                                        <button type="submit" class="dropdown-item text-danger" onclick="return confirm('Are you sure you want to delete this draft invoice?');">
                                                            <i class="bi bi-trash3 me-2"></i>Delete Draft
                                                        </button>
                                                    </form>
                                                </li>
                                            {% endif %}
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% include "partials/_pagination.html" %}
            {% else %}
                <div class="alert alert-info text-center" role="alert">
                    <i class="bi bi-info-circle-fill me-2"></i>
                    No invoices found matching your criteria.
                    {% if perms.fees.add_invoice and not request.GET.urlencode %}
                        <a href="{% url 'fees:invoice_create_manual' %}" class="alert-link">Create one manually?</a>
                    {% endif %}
                </div>
            {% endif %}
        </div> {# End card-body #}
    </div> {# End card #}

    <div class="footer-actions mt-3">
        <a href="{% url 'schools:dashboard' %}" class="btn btn-secondary"><i class="bi bi-arrow-left-circle me-1"></i> Back to Dashboard</a>
    </div>

</div> {# End container-fluid #}
{% endblock tenant_specific_content %}


{% block page_specific_js %}
    {{ block.super }}
    {# Initialize Select2 if you choose to use it for better dropdowns #}
    {# You would need to include Select2 CSS in base.html or tenant_base.html #}
    {# and Select2 JS here or in base.html #}
    {% comment %} {#
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
    $(document).ready(function() {
        // Target specific select elements if they are rendered by django-filter
        $('#{{ filter.form.student.id_for_label|default_if_none:filter_form.student.id_for_label }}').select2({
            theme: "bootstrap-5",
            placeholder: "Select Student",
            allowClear: true
        });
        $('#{{ filter.form.academic_year.id_for_label|default_if_none:filter_form.academic_year.id_for_label }}').select2({
            theme: "bootstrap-5",
            placeholder: "Select Year",
            allowClear: true
        });
        $('#{{ filter.form.status.id_for_label|default_if_none:filter_form.status.id_for_label }}').select2({
            theme: "bootstrap-5",
            placeholder: "Select Status",
            allowClear: true
        });
    });
    </script>
    #} {% endcomment %}
{% endblock %}













{% comment %} {# D:\school_fees_saas_v2\templates\fees\invoice_list.html #}
{% extends "tenant_base.html" %}
{% load static humanize fees_tags %} {# fees_tags for invoice_status_badge #}

{% block title %}{{ view_title|default:"Manage Invoices" }} - {{ request.tenant.name }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }} {# Inherit from tenant_base #}
    <style>
        .status-badge { font-size: 0.8em; padding: 0.4em 0.7em; }
        .actions-column .btn-sm { margin-bottom: 0.25rem; } /* Spacing for action buttons if they wrap */
        .filter-form .form-label-sm { margin-bottom: 0.1rem; font-size: 0.8rem; } /* Smaller filter labels */
        .table th, .table td { vertical-align: middle; }
    </style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-light d-flex flex-wrap justify-content-between align-items-center py-2">
            <h4 class="mb-0 me-3">{{ view_title|default:"Manage Invoices" }}</h4>
            <div class="d-flex align-items-center gap-2"> {# Use gap for spacing #}
                {% if perms.fees.add_invoice %}
                    <a href="{% url 'fees:invoice_create' %}" class="btn btn-primary btn-sm">
                        <i class="bi bi-plus-circle-fill me-1"></i> Create Manual Invoice
                    </a>
                    <a href="{% url 'fees:generate_structure_invoices' %}" class="btn btn-success btn-sm">
                        <i class="bi bi-receipt-cutoff me-1"></i> Generate by Structure
                    </a>
                {% endif %}
                {# Add general export buttons for the list if needed here, or handle via filter form #}
            </div>
        </div>
        <div class="card-body">
            {% include "partials/_messages.html" %}

            {# --- Filter Form Section --- #}
            <form method="get" class="mb-4 p-3 border rounded bg-light-subtle filter-form-row">
                <div class="row g-2 align-items-end">
                    {# Check if 'filter' (from FilterView) or 'filter_form' (if passed manually) is in context #}
                    {% with form_to_render=filter.form|default:filter_form %}
                        {% if form_to_render %}
                            <div class="col-md-3 col-lg-2"> {# Student #}
                                <label for="{{ form_to_render.student.id_for_label }}" class="form-label form-label-sm">{{ form_to_render.student.label }}</label>
                                {{ form_to_render.student }}
                            </div>
                            <div class="col-md-2 col-lg-2"> {# Academic Year #}
                                <label for="{{ form_to_render.academic_year.id_for_label }}" class="form-label form-label-sm">{{ form_to_render.academic_year.label }}</label>
                                {{ form_to_render.academic_year }}
                            </div>
                            <div class="col-md-2 col-lg-2"> {# Status #}
                                <label for="{{ form_to_render.status.id_for_label }}" class="form-label form-label-sm">{{ form_to_render.status.label }}</label>
                                {{ form_to_render.status }}
                            </div>
                            <div class="col-md-3 col-lg-3"> {# Issue Date Range #}
                                <label class="form-label form-label-sm">{{ form_to_render.issue_date.label }}</label>
                                <div class="input-group input-group-sm">
                                    {{ form_to_render.issue_date }} {# Renders both date inputs for the range #}
                                </div>
                            </div>
                            <div class="col-md-auto col-lg-1 pt-3"> {# Buttons aligned with other fields due to label #}
                                <button type="submit" class="btn btn-primary btn-sm w-100"><i class="bi bi-funnel-fill"></i> Filter</button>
                            </div>
                            <div class="col-md-auto col-lg-1 pt-3">
                                <a href="{% url 'fees:invoice_list' %}" class="btn btn-outline-secondary btn-sm w-100" title="Clear Filters"><i class="bi bi-x-lg"></i> Clear</a>
                            </div>
                        {% else %}
                            <div class="col-12"><p class="text-danger">Filter form is not available.</p></div>
                        {% endif %}
                    {% endwith %}
                </div>
            </form>
            {# --- End Filter Form Section --- #}

            {% if invoices %} {# 'invoices' is context_object_name from ListView #}
                <div class="table-responsive">
                    <table class="table table-hover table-striped table-bordered align-middle table-sm"> {# Added table-sm for denser table #}
                        <thead class="table-light">
                            <tr>
                                <th scope="col" style="width: 10%;">Invoice #</th>
                                <th scope="col" style="width: 20%;">Student</th>
                                <th scope="col" style="width: 10%;">Issue Date</th>
                                <th scope="col" style="width: 10%;">Due Date</th>
                                <th scope="col" class="text-end" style="width: 10%;">Total</th>
                                <th scope="col" class="text-end" style="width: 10%;">Discount</th>
                                <th scope="col" class="text-end" style="width: 10%;">Paid</th>
                                <th scope="col" class="text-end" style="width: 10%;">Balance Due</th>
                                <th scope="col" class="text-center" style="width: 10%;">Status</th>
                                <th scope="col" class="text-center" style="width: 10%;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in invoices %}
                            <tr>
                                <td>
                                    <a href="{{ invoice.get_absolute_url }}"><strong>{{ invoice.invoice_number_display }}</strong></a>
                                </td>
                                <td>
                                    {% if invoice.student %}
                                        {{ invoice.student.full_name }}
                                        <small class="d-block text-muted">{{ invoice.student.admission_number }} | {{ invoice.student.current_class.name|default:"" }}</small>
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </td>
                                <td>{{ invoice.issue_date|date:"d M Y" }}</td>
                                <td>{{ invoice.due_date|date:"d M Y" }}</td>
                                <td class="text-end fw-bold">{{ invoice.total_amount|intcomma }}</td>
                                <td class="text-end">{{ invoice.discount_applied|intcomma }}</td>
                                <td class="text-end text-success">{{ invoice.amount_paid|intcomma }}</td>
                                <td class="text-end {% if invoice.balance_due > 0 %}text-danger fw-bold{% else %}text-muted{% endif %}">
                                    {{ invoice.balance_due|intcomma }}
                                </td>
                                <td class="text-center">
                                    {% invoice_status_badge invoice.status %}
                                </td>
                                <td class="text-center actions-column">
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="actionsMenu{{invoice.pk}}" data-bs-toggle="dropdown" aria-expanded="false">
                                            Actions
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="actionsMenu{{invoice.pk}}">
                                            <li><a class="dropdown-item" href="{{ invoice.get_absolute_url }}"><i class="bi bi-eye me-2"></i>View Details</a></li>
                                            <li><a class="dropdown-item" href="{% url 'fees:invoice_pdf' invoice.pk %}" target="_blank"><i class="bi bi-file-earmark-pdf me-2"></i>View/Print PDF</a></li>
                                            {% if invoice.is_editable and perms.fees.change_invoice %}
                                                <li><a class="dropdown-item" href="{% url 'fees:invoice_update' invoice.pk %}"><i class="bi bi-pencil-square me-2"></i>Edit Draft</a></li>
                                            {% endif %}
                                            {% if invoice.is_payable %}
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item" href="{% url 'payments:record_payment' %}?invoice={{ invoice.pk }}"><i class="bi bi-cash-coin me-2"></i>Record Payment</a></li>
                                            {% endif %}
                                            {% if invoice.status == invoice.STATUS_DRAFT and perms.fees.delete_invoice %}
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="{% url 'fees:invoice_delete' invoice.pk %}"><i class="bi bi-trash3 me-2"></i>Delete Draft</a></li>
                                            {% endif %}
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% include "partials/_pagination.html" %} {# Pass page_obj implicitly #}
            {% else %}
                <div class="alert alert-info text-center" role="alert">
                    <i class="bi bi-info-circle-fill me-2"></i>
                    No invoices found matching your criteria.
                    {% if perms.fees.add_invoice and not request.GET.urlencode %} {# Show only if no filters applied #}
                        <a href="{% url 'fees:invoice_create' %}" class="alert-link">Create one manually?</a>
                    {% endif %}
                </div>
            {% endif %}
        </div> {# End card-body #}
    </div> {# End card #}

    <div class="footer-actions mt-3">
        <a href="{% url 'schools:dashboard' %}" class="btn btn-secondary"><i class="bi bi-arrow-left-circle me-1"></i> Back to Dashboard</a>
    </div>

</div> {# End container-fluid #}
{% endblock content %}

{% block page_specific_js %}
    {{ block.super }}
    {# Initialize Select2 if you choose to use it for better dropdowns #}
    {# You would need to include Select2 CSS in base.html or tenant_base.html #}
    {# and Select2 JS here or in base.html #}
    {#
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
    $(document).ready(function() {
        // Target specific select elements if they are rendered by django-filter
        $('#{{ filter.form.student.id_for_label|default_if_none:filter_form.student.id_for_label }}').select2({
            theme: "bootstrap-5",
            placeholder: "Select Student",
            allowClear: true
        });
        $('#{{ filter.form.academic_year.id_for_label|default_if_none:filter_form.academic_year.id_for_label }}').select2({
            theme: "bootstrap-5",
            placeholder: "Select Year",
            allowClear: true
        });
        $('#{{ filter.form.status.id_for_label|default_if_none:filter_form.status.id_for_label }}').select2({
            theme: "bootstrap-5",
            placeholder: "Select Status",
            allowClear: true
        });
    });
    </script>
    #}
{% endblock %}


 {% endcomment %}
