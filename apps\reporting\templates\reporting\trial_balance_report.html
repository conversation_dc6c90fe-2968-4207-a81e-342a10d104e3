{# templates/reporting/trial_balance_report.html #}
{% extends "tenant_base.html" %}
{% load humanize core_tags %}

{% block title %}{{ view_title|default:"Trial Balance" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-collection-fill" %} {# Define icon for this specific report #}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    <p>As at: {{ report_date|date:"Y-m-d" }}</p>

    {# Filter Form (if you have one) #}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Expense Filters" %}
    {% include "partials/_messages.html" %}

    {% comment %} <form method="get" class="mb-3">
        {{ filter_form.as_p }}
        <button type="submit" class="btn btn-primary btn-sm">Apply Filter</button>
        <a href="{% url 'reporting:trial_balance_report' %}" class="btn btn-secondary btn-sm">Clear</a>
    </form> {% endcomment %}

    <table class="table table-bordered table-sm">
        <thead class="table-light">
            <tr>
                <th>Account Code</th>
                <th>Account Name</th>
                <th style="text-align: right;">Debit Balance</th>
                <th style="text-align: right;">Credit Balance</th>
            </tr>
        </thead>
        <tbody>
            {% for account_data in trial_balance_data %}
            <tr>
                <td>{{ account_data.account.code }}</td>
                <td>{{ account_data.account.name }}</td>
                <td style="text-align: right;">
                    {% if account_data.balance > 0 %}{{ account_data.balance|floatformat:2|intcomma }}{% endif %}
                </td>
                <td style="text-align: right;">
                    {% if account_data.balance < 0 %}{{ account_data.balance|floatformat:2|intcomma }}{% endif %}
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="4" class="text-center">No data for trial balance.</td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot class="table-group-divider">
            <tr class="table-light fw-bold">
                <td colspan="2" style="text-align: right;">Totals:</td>
                <td style="text-align: right;">{{ total_debits|floatformat:2|intcomma }}</td>
                <td style.text-align: right;">{{ total_credits|floatformat:2|intcomma }}</td>
            </tr>
            <tr class="{% if totals_match %}table-success{% else %}table-danger{% endif %} fw-bold">
                <td colspan="2" style="text-align: right;">Difference:</td>
                <td colspan="2" style="text-align: {% if totals_match %}center{% else %}right{% endif %};">
                    {% if totals_match %}Balanced{% else %}{{ difference|floatformat:2|intcomma }}{% endif %}
            </tr>
        </tfoot>
    </table>
    {# Export buttons later #}
</div>
{% endblock %}



{% comment %} {% extends "tenant_base.html" %}
{% load humanize static %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="h3 mb-0 text-gray-800">{{ view_title }}</h1>
    </div>

    {% include "partials/_messages.html" %}
    {% include "./_report_filter_export_card.html" %}

    <div class="card shadow-sm">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Trial Balance as at {{ report_date|date:"d F Y" }}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-sm" width="100%" cellspacing="0">
                    <thead class="table-light">
                        <tr>
                            <th>Account Code</th>
                            <th>Account Name</th>
                            <th class="text-end">Debit</th>
                            <th class="text-end">Credit</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in trial_balance_items %}
                        <tr>
                            <td>{{ item.account_code }}</td>
                            <td>{{ item.account_name }}</td>
                            <td class="text-end">{% if item.debit != 0 %}{{ item.debit|floatformat:2|intcomma }}{% endif %}</td>
                            <td class="text-end">{% if item.credit != 0 %}{{ item.credit|floatformat:2|intcomma }}{% endif %}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="4" class="text-center py-4">No account balances to display for the selected date.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    {% if trial_balance_items %}
                    <tfoot>
                        <tr class="table-light fw-bold fs-5">
                            <td colspan="2" class="text-end">TOTALS</td>
                            <td class="text-end">{{ total_debits|floatformat:2|intcomma }}</td>
                            <td class="text-end">{{ total_credits|floatformat:2|intcomma }}</td>
                        </tr>
                        {% if not totals_match %}
                        <tr class="table-danger fw-bold">
                            <td colspan="2" class="text-end">DIFFERENCE</td>
                            <td class="text-end" colspan="2">{{ difference|floatformat:2|intcomma }}</td>
                        </tr>
                        {% endif %}
                    </tfoot>
                    {% endif %}
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %} {% endcomment %}





{% comment %} {% extends "tenant_base.html" %}
{% load static core_tags humanize %}

{% block title %}{{ view_title|default:"Trial Balance Report" }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>{{ view_title|default:"Trial Balance Report" }}</h1>
    <p class="lead">Report content will be displayed here.</p>
    {# Add filter form and table later #}
    <hr>
    <a href="{% url 'schools:dashboard' %}" class="btn btn-secondary">Back to Dashboard</a>
</div>
{% endblock %} {% endcomment %}