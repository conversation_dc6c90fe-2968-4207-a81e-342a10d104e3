# D:\school_fees_saas_v2\apps\users\views.py

from django.conf import settings
from django.contrib import messages
from django.contrib.auth.forms import AuthenticationForm
from django.shortcuts import render, redirect
from django.urls import reverse, NoReverseMatch
from django.utils.http import url_has_allowed_host_and_scheme

from django.contrib.auth import login as django_auth_login, logout as django_auth_logout, authenticate

from urllib.parse import urlunsplit
from django.utils.http import url_has_allowed_host_and_scheme # urlunsplit is useful
from urllib.parse import urlsplit, urlencode # For query parameters

import logging

from apps.tenants.models import School # Your tenant model

logger = logging.getLogger(__name__)

def school_admin_login(request):
    # --- HANDLE ALREADY AUTHENTICATED PUBLIC USER ---
    if request.user.is_authenticated and not request.user.is_anonymous: # Ensure it's a real user
        logger.info(f"SCHOOL_ADMIN_LOGIN (Already Auth): User '{request.user.email}' already authenticated. Attempting tenant entry gate redirect.")
        try:
            # Get the PublicUser to access owned_schools
            from apps.users.models import PublicUser
            try:
                public_user = PublicUser.objects.get(email=request.user.email)
                school = public_user.owned_schools.filter(is_active=True).order_by('created_on').first()
            except PublicUser.DoesNotExist:
                logger.warning(f"SCHOOL_ADMIN_LOGIN (Already Auth): No PublicUser found for '{request.user.email}'.")
                school = None

            if not school:
                logger.info(f"SCHOOL_ADMIN_LOGIN (Already Auth): No active school for user '{request.user.email}'. Redirecting to public home.")
                return redirect(reverse('public_site:home'))

            primary_domain_obj = school.domains.filter(is_primary=True).first()
            if not primary_domain_obj:
                logger.warning(f"SCHOOL_ADMIN_LOGIN (Already Auth): No primary domain for school '{school.name}'. Redirecting to public home.")
                messages.warning(request, "Your school's primary domain is not configured. Please contact support.")
                return redirect(reverse('public_site:home'))

            try:
                entry_gate_path = reverse('portal_auth:owner_entry_gate')
            except NoReverseMatch:
                logger.error("SCHOOL_ADMIN_LOGIN (Already Auth): NoReverseMatch for 'portal_auth:owner_entry_gate'. Critical configuration error.")
                messages.error(request, "Portal entry misconfiguration. Please contact support.")
                return redirect(reverse('public_site:home'))
            
            # Preserve existing query parameters (like 'next' from the original URL)
            query_params = request.GET.copy() # Get a mutable copy
            # If you specifically want to pass a 'next' from this context to the entry gate,
            # you could set it here: query_params['next_after_entry_gate'] = some_tenant_path

            # Construct the full redirect URL
            # primary_domain_obj.domain from django-tenants usually includes the port if non-standard
            # e.g., "zharatest.myapp.test:8000"
            # settings.APP_SCHEME is 'http' or 'https'
            redirect_url_on_tenant_domain = urlunsplit((
                settings.APP_SCHEME,
                primary_domain_obj.domain, # This should be the FQDN, e.g., zharatest.myapp.test:8000
                entry_gate_path,
                urlencode(query_params), # Re-encode query parameters
                ''  # fragment
            ))
            
            # For url_has_allowed_host_and_scheme, it checks the hostname part against ALLOWED_HOSTS.
            # settings.ALLOWED_HOSTS should contain '.myapp.test' to cover all subdomains.
            if url_has_allowed_host_and_scheme(redirect_url_on_tenant_domain, allowed_hosts=settings.ALLOWED_HOSTS):
                logger.info(f"SCHOOL_ADMIN_LOGIN (Already Auth): Safe redirect to entry gate for {request.user.email} to {redirect_url_on_tenant_domain}.")
                return redirect(redirect_url_on_tenant_domain)
            else:
                target_netloc = urlsplit(redirect_url_on_tenant_domain).netloc
                logger.warning(
                    f"SCHOOL_ADMIN_LOGIN (Already Auth): Unsafe redirect attempt to {redirect_url_on_tenant_domain}. "
                    f"Target netloc: '{target_netloc}'. ALLOWED_HOSTS: {settings.ALLOWED_HOSTS}. "
                    f"Redirecting to public home."
                )
                messages.warning(request, "Could not securely redirect to your school portal. Please access it via its direct URL.")
                return redirect(reverse('public_site:home'))

        except Exception as e:
            logger.error(f"SCHOOL_ADMIN_LOGIN (Already Auth): Error during redirect for {request.user.email}: {e}", exc_info=True)
            messages.error(request, "An error occurred while accessing your school. Please contact support.")
            return redirect(reverse('public_site:home'))

    # --- HANDLE POST REQUEST (Actual Login Attempt) ---
    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            user = form.get_user()
            # Use the correct backend for public user login session
            public_user_backend = 'django.contrib.auth.backends.ModelBackend' # Or your custom public user backend if any
            django_auth_login(request, user, backend=public_user_backend)
            
            messages.success(request, f"Login successful! Welcome {user.first_name or user.email}.")
            logger.info(f"SCHOOL_ADMIN_LOGIN (POST): User '{user.email}' logged in successfully.")

            try:
                # Get the PublicUser to access owned_schools
                from apps.users.models import PublicUser
                try:
                    public_user = PublicUser.objects.get(email=user.email)
                    school = public_user.owned_schools.filter(is_active=True).order_by('created_on').first()
                except PublicUser.DoesNotExist:
                    logger.warning(f"SCHOOL_ADMIN_LOGIN (POST): No PublicUser found for '{user.email}'.")
                    school = None

                if not school:
                    logger.warning(f"SCHOOL_ADMIN_LOGIN (POST): No active school for user: '{user.email}'.")
                    messages.warning(request, "Login successful, but no active school is associated with your account or your school is inactive.")
                    return redirect(reverse('public_site:home')) # Or a page to prompt school creation

                logger.info(f"SCHOOL_ADMIN_LOGIN (POST): Found active school '{school.name}'.")
                primary_domain_obj = school.domains.filter(is_primary=True).first()
                if not primary_domain_obj:
                    logger.warning(f"SCHOOL_ADMIN_LOGIN (POST): No primary domain for school: '{school.name}'.")
                    messages.warning(request, "Login successful, but your school's primary domain is not configured. Please contact support.")
                    return redirect(reverse('public_site:home'))
                
                logger.info(f"SCHOOL_ADMIN_LOGIN (POST): Found primary domain '{primary_domain_obj.domain}'.")
                
                try:
                    entry_gate_path = reverse('portal_auth:owner_entry_gate')
                except NoReverseMatch:
                    logger.error("SCHOOL_ADMIN_LOGIN (POST): NoReverseMatch for 'portal_auth:owner_entry_gate'. Critical error.")
                    messages.error(request, "Portal misconfiguration. Please contact support.")
                    return redirect(reverse('public_site:home'))

                # Preserve 'next' query parameter if it was submitted with the login form
                # (e.g., from a redirect to login page)
                # The portal_entry_gate_view can then use this.
                next_val_from_form = request.POST.get('next', request.GET.get('next', '')) # Check POST then GET
                query_params = {}
                if next_val_from_form:
                    query_params['next'] = next_val_from_form
                
                redirect_url_on_tenant_domain = urlunsplit((
                    settings.APP_SCHEME,
                    primary_domain_obj.domain, # e.g., zharatest.myapp.test:8000
                    entry_gate_path,
                    urlencode(query_params),
                    '' 
                ))

                logger.info(f"SCHOOL_ADMIN_LOGIN (POST): Generated redirect URL to entry gate: {redirect_url_on_tenant_domain}")
                # For POST redirects after successful internal logic, we usually trust the constructed URL.
                # The main risk for open redirects is when `next` comes from untrusted GET params directly.
                return redirect(redirect_url_on_tenant_domain)

            except Exception as e:
                logger.error(f"SCHOOL_ADMIN_LOGIN (POST): Exception after login for user '{user.email}': {e}", exc_info=True)
                messages.error(request, "An error occurred accessing your school details after login. Please contact support.")
                return redirect(reverse('public_site:home'))
        else:
            logger.warning(f"SCHOOL_ADMIN_LOGIN (POST): Form invalid. Errors: {form.errors.as_json()}")
            # errors already in form, just re-render
            # messages.error(request, "Invalid email or password.") # Form will show field errors
    else: # GET request
        form = AuthenticationForm(request)
        # Capture 'next' from GET to pass to the template if needed
        # The login template should have <input type="hidden" name="next" value="{{ request.GET.next }}">
        # if request.GET.get('next'):
        #     logger.info(f"SCHOOL_ADMIN_LOGIN (GET): 'next' parameter found: {request.GET.get('next')}")


    context = {
        'form': form,
        'view_title': "School Administrator Login"
        # 'next': request.GET.get('next', '') # Pass 'next' to template if form needs to resubmit it
    }
    return render(request, 'users/login.html', context)


# school_admin_logout_view is fine as is.
def school_admin_logout_view(request):
    current_user_email = request.user.email if request.user.is_authenticated else "User"
    logger.info(f"SCHOOL_ADMIN_LOGOUT: Logging out public user: {current_user_email}")
    django_auth_logout(request) 
    messages.info(request, "You have been successfully logged out.")
    return redirect(reverse('public_site:home'))





# apps/users/views.py
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
# from apps.tenants.models import School # Your School model
# from django.conf import settings # If PublicUserModel is settings.AUTH_USER_MODEL

# Ensure School and PublicUserModel are imported correctly
School = None
try:
    from apps.tenants.models import School as TenantSchoolModel
    School = TenantSchoolModel
except ImportError:
    pass # Handle error or log

@login_required # Ensure user is logged in
def select_tenant_dashboard_view(request):
    if not School: # Check if School model was loaded
        messages.error(request, "System configuration error: Tenant model not found.")
        return redirect('public_site:home')

    # Ensure the user is a public admin/owner type
    # This check might be more robust if you have a specific flag on your PublicUserModel
    if not (request.user.is_superuser or (hasattr(request.user, 'is_staff') and request.user.is_staff) or hasattr(request.user, 'owned_schools')):
        messages.error(request, "You do not have permission to access this page.")
        return redirect('public_site:home')

    owned_schools = School.objects.filter(owner=request.user, is_active=True).order_by('name')
    
    if not owned_schools.exists():
        messages.info(request, "You do not currently own any active schools.")
        return redirect('public_site:home') # Or a page indicating they have no schools
    
    if owned_schools.count() == 1:
        # If only one school, redirect directly to its dashboard URL
        # This requires constructing the tenant's dashboard URL
        school = owned_schools.first()
        # Assuming your School model has a get_absolute_url() method that returns the tenant dashboard URL
        # e.g., http://schoolschema.myapp.test:8000/portal/dashboard/
        # If not, you need to construct it.
        if hasattr(school, 'get_absolute_url'):
            return redirect(school.get_absolute_url())
        else:
            # Fallback: try to construct if get_absolute_url is missing
            # This is less robust
            from django_tenants.utils import get_tenant_domain_model
            Domain = get_tenant_domain_model()
            try:
                domain_obj = Domain.objects.get(tenant=school, is_primary=True)
                # Assuming APP_SCHEME and TENANT_BASE_DOMAIN are from your settings or context
                # This part is tricky to get right universally here.
                # Your owned_school_for_public_admin context variable logic might be a better reference.
                # For now, let's just pass to template if more than one or no get_absolute_url
                pass # Will fall through to render template
            except Domain.DoesNotExist:
                messages.error(request, f"Primary domain not found for {school.name}.")
                # Fall through to render template with list

    context = {
        'view_title': "Select Your School Dashboard",
        'owned_schools': owned_schools,
    }
    return render(request, 'users/select_tenant_dashboard.html', context)