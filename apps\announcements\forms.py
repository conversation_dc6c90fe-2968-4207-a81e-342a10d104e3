# D:\school_fees_saas_v2\apps\announcements\forms.py
from django import forms
from .models import Announcement
from django.contrib.auth.models import Group
from django.conf import settings
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError # Ensure this is imported

import logging
logger = logging.getLogger(__name__)

class AnnouncementForm(forms.ModelForm):
    class Meta:
        model = Announcement
        fields = [
            'title', 'content', 'author', 
            'tenant', 'is_global', 'target_global_audience_type',
            'target_all_tenant_staff', 'target_tenant_staff_groups', 
            'target_all_tenant_parents', 
            'publish_date', 'expiry_date', 
            'is_published', 'is_sticky'
        ]
        widgets = {
            'content': forms.Textarea(attrs={'rows': 8, 'class': 'form-control'}),
            'publish_date': forms.DateTimeInput(
                attrs={'type': 'datetime-local', 'class': 'form-control'},
                format='%Y-%m-%dT%H:%M'
            ),
            'expiry_date': forms.DateTimeInput(
                attrs={'type': 'datetime-local', 'class': 'form-control'},
                format='%Y-%m-%dT%H:%M'
            ),
            'target_tenant_staff_groups': forms.CheckboxSelectMultiple( # <<< SET TO CHECKBOXES HERE
                attrs={'class': 'form-check-input-group'} # Optional custom class for styling the group of checkboxes
            ),
            'tenant': forms.Select(attrs={'class': 'form-select'}),
            'author': forms.Select(attrs={'class': 'form-select'}),
        }
        help_texts = {
            'is_global': _("Check if this announcement is platform-wide and not for a single school."),
            'target_tenant_staff_groups': _("Select staff roles. Ignored if 'Target All Staff' is checked."),
        }
        labels = {
            'target_all_tenant_staff': _("Make visible to all staff members of this school?"),
        }

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None)
        self.tenant_instance = kwargs.pop('tenant', None)
        super().__init__(*args, **kwargs)

        logger.info(f"AnnouncementForm __init__. Instance PK: {self.instance.pk if self.instance else 'New'}. tenant_instance from view: {self.tenant_instance}")

        # Set initial author for new announcements
        if 'author' in self.fields:
            if not self.instance.pk and self.request and hasattr(self.request, 'user') and self.request.user.is_authenticated:
                self.fields['author'].initial = self.request.user
                self.fields['author'].widget = forms.HiddenInput() # Hide if auto-set
                logger.debug(f"Set initial author to {self.request.user.email} and hid field.")
            elif self.instance.pk and self.instance.author:
                self.fields['author'].disabled = True

        # Set initial tenant for new non-global announcements
        if 'tenant' in self.fields:
            if self.tenant_instance and not self.instance.pk and not self.initial.get('tenant'):
                if not self.initial.get('is_global', getattr(self.instance, 'is_global', False)):
                    self.fields['tenant'].initial = self.tenant_instance
                    self.fields['tenant'].widget = forms.HiddenInput() # Hide if auto-set by context
                    logger.debug(f"Set initial tenant to {self.tenant_instance.name} and hid field.")
            elif self.instance.pk and self.instance.tenant:
                self.fields['tenant'].disabled = True

        # Populate queryset for target_tenant_staff_groups
        if 'target_tenant_staff_groups' in self.fields:
            all_groups = Group.objects.all().order_by('name')
            self.fields['target_tenant_staff_groups'].queryset = all_groups
            logger.info(f"target_tenant_staff_groups queryset set. Count: {all_groups.count()}")
            if not all_groups.exists():
                logger.warning("No Groups found for target_tenant_staff_groups. Selection will be empty.")
            # No need to override widget here if Meta specifies CheckboxSelectMultiple

        # Default publish_date
        if 'publish_date' in self.fields and not self.instance.pk and not self.initial.get('publish_date'):
            self.fields['publish_date'].initial = timezone.now()
        
        # Ensure Bootstrap classes on other fields
        for field_name, field in self.fields.items():
            widget = field.widget
            # Apply to general input types
            if isinstance(widget, (forms.TextInput, forms.EmailInput, forms.PasswordInput, 
                                    forms.NumberInput, forms.URLInput, forms.DateInput, 
                                    forms.TimeInput, forms.DateTimeInput, forms.Textarea)):
                current_class = widget.attrs.get('class', '')
                if 'form-control' not in current_class:
                    widget.attrs['class'] = f'{current_class} form-control'.strip()
            # Apply to select widgets
            elif isinstance(widget, forms.Select):
                current_class = widget.attrs.get('class', '')
                if 'form-select' not in current_class:
                    widget.attrs['class'] = f'{current_class} form-select'.strip()
            # CheckboxSelectMultiple and RadioSelect are handled differently by Bootstrap (often by wrapping inputs)
            # FileInput also has specific Bootstrap styling.
            # BooleanField (single checkbox) widget is CheckboxInput.

    def clean(self):
        cleaned_data = super().clean()
        is_global = cleaned_data.get('is_global')
        tenant_from_form_data = cleaned_data.get('tenant') # Tenant selected/submitted in the form
        context_tenant_from_view = self.tenant_instance # Tenant from the view's context

        logger.debug(f"Form Clean - is_global from form: {is_global}")
        logger.debug(f"Form Clean - Tenant from form data ('tenant'): {tenant_from_form_data}")
        logger.debug(f"Form Clean - Context tenant from view (self.tenant_instance): {context_tenant_from_view}")

        actual_tenant_for_validation = tenant_from_form_data

        # If creating a new instance AND the tenant field in form is not set/disabled,
        # AND it's not a global announcement, use the tenant from the view context.
        if not self.instance.pk and not tenant_from_form_data and context_tenant_from_view and not is_global:
            actual_tenant_for_validation = context_tenant_from_view
            cleaned_data['tenant'] = context_tenant_from_view # IMPORTANT: Update cleaned_data
            logger.debug(f"Form Clean - Using context_tenant '{context_tenant_from_view.name}' for validation and cleaned_data.")
        elif self.instance.pk and not tenant_from_form_data and not is_global:
            # If updating, and tenant field was not submitted (e.g., disabled),
            # use the instance's existing tenant.
            actual_tenant_for_validation = self.instance.tenant
            logger.debug(f"Form Clean - Updating. Using instance.tenant '{self.instance.tenant.name if self.instance.tenant else None}' for validation.")


        logger.debug(f"Form Clean - Final 'actual_tenant_for_validation': {actual_tenant_for_validation}")

        # --- Validation Logic ---
        if is_global and actual_tenant_for_validation:
            # If is_global is checked, tenant field should be None.
            # This validation might be better if 'tenant' field is cleared by JS when is_global is checked.
            self.add_error(None, _("A platform-wide (global) announcement cannot be assigned to a school if 'Is Platform-Wide' is checked. Please clear the 'School' field or uncheck 'Is Platform-Wide'."))
        
        if not is_global and not actual_tenant_for_validation:
            self.add_error('tenant', _("A school-specific announcement must be assigned to a school. Please select a 'School' or check 'Is Platform-Wide'."))
        
        if is_global and not cleaned_data.get('target_global_audience_type'):
            self.add_error('target_global_audience_type', _("A platform-wide announcement must have a 'Global Target Audience' selected."))

        # Audience validation for tenant-specific announcements
        if not is_global and actual_tenant_for_validation: # <<< CORRECTED VARIABLE NAME
            target_all_staff = cleaned_data.get('target_all_tenant_staff')
            selected_staff_groups = cleaned_data.get('target_tenant_staff_groups') 
            target_all_parents = cleaned_data.get('target_all_tenant_parents')
            has_group_selection = selected_staff_groups and selected_staff_groups.exists()
            
            if not (target_all_staff or has_group_selection or target_all_parents):
                self.add_error(None, 
                    _("For a school-specific announcement, you must select at least one audience: 'All Staff in this school', specific staff groups, or 'All Parents in this school'.")
                )
        
        if cleaned_data.get('target_all_tenant_staff'):
            cleaned_data['target_tenant_staff_groups'] = Group.objects.none()
        
        return cleaned_data
    
    


# D:\school_fees_saas_v2\apps\announcements\forms.py (or your platform_admin_tools app)
