{# D:\school_fees_saas_v2\apps\reporting\templates\reporting\fee_projection_report.html #}
{% extends "tenant_base.html" %}
{% load static humanize %}

{% comment %} {% block tenant_page_title %}{{ view_title|default:"Fee Projection Report" }}{% endblock %} {% endcomment %}

{% block extra_tenant_css %}
    {{ block.super }}
    {# Add any specific CSS for this report if needed #}
{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-graph-up-arrow me-2" %} {# Define icon for this specific report #}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Fee Projection Filters" %}

{% comment %} <div class="container-fluid">
    <div class="pagetitle mb-3">
        <h1><i class="bi bi-graph-up-arrow me-2"></i>{{ view_title }}</h1> {% endcomment %}
        <nav>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item">Reports</li>
                <li class="breadcrumb-item active">{{ view_title }}</li>
            </ol>
        </nav>
    </div><!-- End Page Title -->

    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">{{ report_description|default:"Details of projected fee income." }}</h5>
        </div>
        <div class="card-body mt-3">
            <p class="text-muted">
                This report is currently a placeholder. The full fee projection logic will be implemented here.
            </p>
            
            {% if projected_items %}
                {# Placeholder for how you might display the data later #}
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Projection Item</th>
                            <th>Estimated Amount</th>
                            <th>Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in projected_items %}
                        <tr>
                            <td>{{ item.name }}</td>
                            <td>{{ item.amount|intcomma }}</td>
                            <td>{{ item.notes }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <div class="alert alert-info" role="alert">
                    No projection data available yet or calculation pending.
                </div>
            {% endif %}
        </div>
        <div class="card-footer text-muted small">
            Report generated on: {% now "jS F Y, P" %}
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}


