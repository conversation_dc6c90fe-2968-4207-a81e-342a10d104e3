# D:\school_fees_saas_v2\apps\announcements\views.py
from django.views.generic import List<PERSON>iew, DetailView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib.auth.mixins import LoginRequiredMixin # Standard login required
from django.contrib.messages.views import SuccessMessageMixin
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.shortcuts import get_object_or_404

# Assuming your robust mixins are in apps.common.mixins
# Adjust imports if your mixins (TenantPermissionRequiredMixin etc.) are elsewhere
from apps.common.mixins import TenantPermissionRequiredMixin 
from .models import Announcement
from .forms import AnnouncementForm

import logging
logger = logging.getLogger(__name__)

# Ensure StaffUser can be imported for type checking or author assignment
try:
    from apps.schools.models import StaffUser
except ImportError:
    StaffUser = None
    logger.warning("announcements.views: Could not import StaffUser from apps.schools.models.")


class AnnouncementListView(LoginRequiredMixin, TenantPermissionRequiredMixin, ListView):
    model = Announcement
    template_name = 'announcements/announcement_list.html'
    context_object_name = 'announcements'
    permission_required = 'announcements.view_announcement'
    paginate_by = 15 # Optional pagination

    def get_queryset(self):
        # django-tenants handles schema isolation.
        # Order by most recent, pinned first.
        return Announcement.objects.all().select_related('author').order_by('-is_sticky', '-publish_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("School Announcements")
        return context


class AnnouncementDetailView(LoginRequiredMixin, TenantPermissionRequiredMixin, DetailView):
    model = Announcement
    template_name = 'announcements/announcement_detail.html'
    context_object_name = 'announcement'
    permission_required = 'announcements.view_announcement'

    def get_queryset(self):
        return super().get_queryset().select_related('author').prefetch_related('target_staff_groups')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = self.object.title
        return context


# apps/announcements/views.py
from django.views.generic.edit import CreateView, UpdateView
from .models import Announcement
from .forms import AnnouncementForm
from django.urls import reverse_lazy
from apps.common.mixins import StaffLoginRequiredMixin # Or your appropriate mixin

class AnnouncementCreateView(StaffLoginRequiredMixin, CreateView):
    model = Announcement
    form_class = AnnouncementForm
    template_name = 'announcements/announcement_form.html' # Or your form template
    success_url = reverse_lazy('announcements:list') # Or wherever you redirect

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['request'] = self.request # Pass request to form's __init__
        if hasattr(self.request, 'tenant'):
            kwargs['tenant'] = self.request.tenant
        return kwargs

    def form_valid(self, form):
        form.instance.author = self.request.user # Set author if not done in form
        if hasattr(self.request, 'tenant') and not form.instance.is_global:
            form.instance.tenant = self.request.tenant # Set tenant if not global
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Create New Announcement"
        return context

class AnnouncementUpdateView(StaffLoginRequiredMixin, UpdateView):
    model = Announcement
    form_class = AnnouncementForm
    template_name = 'announcements/announcement_form.html'
    success_url = reverse_lazy('announcements:list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['request'] = self.request # Pass request to form's __init__
        if hasattr(self.request, 'tenant'):
            kwargs['tenant'] = self.request.tenant
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Update Announcement: {self.object.title}"
        return context

# class AnnouncementCreateView(LoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, CreateView):
#     model = Announcement
#     form_class = AnnouncementForm
#     template_name = 'announcements/announcement_form.html'
#     permission_required = 'announcements.add_announcement'
#     success_url = reverse_lazy('announcements:announcement_list')
#     success_message = _("Announcement '%(title)s' was created successfully.")

#     def form_valid(self, form):
#         # Set author to current logged-in staff user if StaffUser is correctly imported and matches
#         if StaffUser and isinstance(self.request.user, StaffUser):
#             form.instance.author = self.request.user
#         else:
#             logger.warning(
#                 f"AnnouncementCreateView: Could not set author. "
#                 f"User '{self.request.user}' may not be a StaffUser instance or StaffUser model not loaded."
#             )
#             # Optionally, prevent saving if author cannot be determined:
#             # messages.error(self.request, _("Could not determine the author for the announcement."))
#             # return self.form_invalid(form)
        
#         logger.info(f"Announcement created by {form.instance.author.email if form.instance.author else 'Unknown'}: {form.instance.title}")
#         return super().form_valid(form)

#     def get_form_kwargs(self):
#         kwargs = super().get_form_kwargs()
#         kwargs['request'] = self.request # Pass request to form if needed
#         return kwargs
        
#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context['view_title'] = _("Create New Announcement")
#         context['form_mode'] = "create"
#         return context

# class AnnouncementUpdateView(LoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, UpdateView):
#     model = Announcement
#     form_class = AnnouncementForm
#     template_name = 'announcements/announcement_form.html'
#     permission_required = 'announcements.change_announcement'
#     success_url = reverse_lazy('announcements:announcement_list')
#     success_message = _("Announcement '%(title)s' was updated successfully.")

#     def get_queryset(self):
#         # Ensure only announcements from the current tenant can be edited
#         # (django-tenants should handle this, but an explicit filter can be added if needed)
#         return super().get_queryset().select_related('author')

#     def get_form_kwargs(self):
#         kwargs = super().get_form_kwargs()
#         kwargs['request'] = self.request
#         return kwargs

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context['view_title'] = _("Edit Announcement: %(title)s") % {'title': self.object.title}
#         context['form_mode'] = "update"
#         return context


class AnnouncementDeleteView(LoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, DeleteView):
    model = Announcement
    template_name = 'announcements/announcement_confirm_delete.html'
    permission_required = 'announcements.delete_announcement'
    success_url = reverse_lazy('announcements:announcement_list')
    
    def get_success_message(self, cleaned_data):
        return _("Announcement '%(title)s' was deleted successfully.") % {'title': self.object.title}

    def form_valid(self, form):
        # To display success message with object details before it's deleted
        # self.object is available here.
        title = self.object.title
        response = super().form_valid(form)
        messages.success(self.request, _("Announcement '%(title)s' was deleted successfully.") % {'title': title})
        logger.info(f"Announcement deleted by {self.request.user.email}: {title}")
        return response # super().form_valid(form) already returns redirect to success_url

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Confirm Delete Announcement")
        return context
    
