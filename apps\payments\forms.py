# # apps/payments/forms.py
# from django import forms
import django_filters # Import django_filters
# from .models import Payment, PaymentMethod # Import your models
# from apps.students.models import Student # Import Student if filtering by it
# from django.utils.translation import gettext_lazy as _
# from django.utils import timezone # For default payment_date

# from apps.fees.models import Invoice # Assuming Invoice is in fees.models
# from apps.schools.models import AcademicYear # Assuming AcademicYear is in schools.models
from apps.accounting.models import Account, AccountType # For the payment account field

from django import forms
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from decimal import Decimal

from .models import Payment, PaymentMethod # Assuming PaymentMethod is in this app's models
from apps.students.models import Student
from apps.fees.models import Invoice # Import Invoice model from fees app
from apps.schools.models import AcademicYear # Assuming AcademicYear location

from django import forms
from apps.fees.models import Invoice

import logging
logger = logging.getLogger(__name__)

# class PaymentForm(forms.ModelForm):
#     class Meta:
#         model = Payment
#         fields = [
#             'student', 
#             'invoice', 
#             'academic_year', 
#             'payment_method',
#             'payment_date', 
#             'amount', 
#             'reference_number', 
#             'notes',
#             'payment_type',
#             'status',
#         ]
#         widgets = {
#             'student': forms.Select(attrs={'class': 'form-select form-select-sm student-select2-payment-form'}), # Unique class
#             'invoice': forms.Select(attrs={'class': 'form-select form-select-sm invoice-select2-payment-form'}), # Unique class
#             'academic_year': forms.Select(attrs={'class': 'form-select form-select-sm'}),
#             'payment_method': forms.Select(attrs={'class': 'form-select form-select-sm payment-method-select2'}),
#             'payment_date': forms.DateTimeInput(
#                 attrs={'type': 'datetime-local', 'class': 'form-control form-control-sm flatpickr-datetime'},
#                 format='%Y-%m-%dT%H:%M' # Ensure this matches HTML5 input and JS parser if any
#             ),
#             'amount': forms.NumberInput(attrs={'class': 'form-control form-control-sm text-end', 'step': '0.01'}),
#             'reference_number': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
#             'notes': forms.Textarea(attrs={'rows': 2, 'class': 'form-control form-control-sm'}),
#             'payment_type': forms.Select(attrs={'class': 'form-select form-select-sm'}),
#             'status': forms.Select(attrs={'class': 'form-select form-select-sm'}),
#         }
#         labels = {
#             'academic_year': _("Academic Year"), # Made it not optional for clarity, model allows null
#             'invoice': _("Link to Invoice"),     # Made it not optional for clarity, model allows null
#         }
#         help_texts = {
#             'academic_year': _("The academic year this payment pertains to."),
#             'invoice': _("If this payment is for a specific invoice, select it here."),
#             'amount': _("Enter the positive amount received."),
#         }

#     def __init__(self, *args, **kwargs):
#         self.request = kwargs.pop('request', None)
#         self.tenant = getattr(self.request, 'tenant', None) if self.request else kwargs.pop('tenant', None)
        
#         # For pre-filling form if coming from a specific invoice or student
#         invoice_instance_initial = kwargs.pop('invoice_instance', None)
#         student_instance_initial = kwargs.pop('student_instance', None)

#         super().__init__(*args, **kwargs)

#         # Set initial value for payment_date if creating a new payment
#         if not self.instance.pk and 'payment_date' in self.fields:
#             self.fields['payment_date'].initial = timezone.now().strftime('%Y-%m-%dT%H:%M')
        
#         # Make fields not required if their model counterparts allow blank/null
#         # This allows the form to be submitted and then logic in view/model can handle defaults or errors
#         for field_name in ['student', 'invoice', 'academic_year']:
#             if field_name in self.fields:
#                 model_field = self.Meta.model._meta.get_field(field_name)
#                 if model_field.blank or model_field.null:
#                     self.fields[field_name].required = False
#                 if model_field.blank: # Also affects widget if you want '--------' for optional selects
#                     self.fields[field_name].empty_label = _("--------- (Optional)")


#         # Populate AcademicYear queryset
#         if 'academic_year' in self.fields:
#             self.fields['academic_year'].queryset = AcademicYear.objects.filter(is_active=True).order_by('-start_date', 'name')
#             if not self.fields['academic_year'].required: # Check updated required status
#                 self.fields['academic_year'].empty_label = _("Select Academic Year (Optional)")

#         # Populate PaymentMethod queryset
#         if 'payment_method' in self.fields:
#             self.fields['payment_method'].queryset = PaymentMethod.objects.filter(
#                 is_active=True,
#                 linked_account__isnull=False # Only methods linked to a CoA
#             ).select_related('linked_account').order_by('name')
#             self.fields['payment_method'].empty_label = _("Select Payment Method")

#         # Populate Student queryset (already tenant-scoped)
#         if 'student' in self.fields:
#             self.fields['student'].queryset = Student.objects.filter(is_active=True).order_by('last_name', 'first_name')
#             if not self.fields['student'].required:
#                 self.fields['student'].empty_label = _("Select Student (Optional)")

#         # Populate Invoice queryset - This needs to be dynamic, ideally based on selected student.
#         # For now, show all relevant invoices for the tenant, or none if no student context.
#         # JavaScript would typically update this dropdown when a student is selected.
#         if 'invoice' in self.fields:
#             initial_student_for_invoice_qs = None
#             if student_instance_initial: # If student passed directly for pre-fill
#                 initial_student_for_invoice_qs = student_instance_initial
#             elif self.initial.get('student'): # If student is in initial data (e.g. from GET param)
#                 try:
#                     initial_student_for_invoice_qs = Student.objects.get(pk=self.initial['student'])
#                 except (Student.DoesNotExist, ValueError, TypeError):
#                     pass
            
#             if initial_student_for_invoice_qs:
#                 self.fields['invoice'].queryset = Invoice.objects.filter(
#                     student=initial_student_for_invoice_qs,
#                     status__in=[Invoice.InvoiceStatus.SENT, Invoice.InvoiceStatus.PARTIALLY_PAID, Invoice.InvoiceStatus.OVERDUE]
#                 ).select_related('student').order_by('-issue_date')
#             elif self.instance and self.instance.pk and self.instance.student: # If editing existing payment with a student
#                 self.fields['invoice'].queryset = Invoice.objects.filter(
#                     student=self.instance.student,
#                     status__in=[Invoice.InvoiceStatus.SENT, Invoice.InvoiceStatus.PARTIALLY_PAID, Invoice.InvoiceStatus.OVERDUE]
#                 ).select_related('student').order_by('-issue_date')
#             else: # Default, show no invoices or all open invoices for tenant (could be many)
#                 self.fields['invoice'].queryset = Invoice.objects.filter(
#                     status__in=[Invoice.InvoiceStatus.SENT, Invoice.InvoiceStatus.PARTIALLY_PAID, Invoice.InvoiceStatus.OVERDUE]
#                 ).select_related('student').order_by('-issue_date') # All open for tenant
#                 # self.fields['invoice'].queryset = Invoice.objects.none() # Safer default if student must be chosen first

#             if not self.fields['invoice'].required:
#                 self.fields['invoice'].empty_label = _("Select Invoice (Optional)")

#         # Set initial values passed from view
#         if student_instance_initial and 'student' in self.fields:
#             self.initial['student'] = student_instance_initial.pk
#         if invoice_instance_initial and 'invoice' in self.fields:
#             self.initial['invoice'] = invoice_instance_initial.pk
#             if 'student' in self.fields and invoice_instance_initial.student: # Auto-select student if invoice is set
#                 self.initial['student'] = invoice_instance_initial.student.pk
#             if 'amount' in self.fields and not self.initial.get('amount'): # Pre-fill amount if not already set
#                 self.initial['amount'] = invoice_instance_initial.balance_due
#             if 'academic_year' in self.fields and invoice_instance_initial.academic_year:
#                 self.initial['academic_year'] = invoice_instance_initial.academic_year.pk


#     def clean_amount(self):
#         amount = self.cleaned_data.get('amount')
#         if amount is not None and amount <= Decimal('0.00'):
#             raise forms.ValidationError(_("Payment amount must be a positive value."))
#         return amount

#     def clean_payment_date(self):
#         payment_date = self.cleaned_data.get('payment_date')
#         if payment_date and payment_date.date() > timezone.now().date(): # Allow same day, but not future date
#             raise forms.ValidationError(_("Payment date cannot be in the future."))
#         return payment_date

#     def clean(self):
#         cleaned_data = super().clean()
#         payment_type = cleaned_data.get('payment_type')
#         invoice = cleaned_data.get('invoice')
#         student = cleaned_data.get('student') # Student from form selection
#         amount = cleaned_data.get('amount')

#         # If an invoice is selected, the student field should ideally be derived from it or match it.
#         # The form __init__ tries to pre-fill student if invoice is pre-filled.
#         # This clean method provides a final check.
#         if invoice:
#             if not invoice.student:
#                 # This indicates a data integrity issue with the invoice itself
#                 self.add_error('invoice', _("The selected invoice is not linked to any student."))
#             elif student and invoice.student != student:
#                 # If user manually changed student after invoice was selected (or if pre-fill failed)
#                 self.add_error('student', _("The selected student does not match the student on the selected invoice. Please clear and re-select if needed."))
#             elif not student: # If student field was somehow left empty but invoice is selected
#                 cleaned_data['student'] = invoice.student # Ensure student is set from invoice
        
#         # If payment_type is FEE_PAYMENT, ensure context (invoice or student) is present.
#         if payment_type == Payment.PaymentTypeChoices.FEE_PAYMENT: # Use choices from model
#             if not invoice and not student: # If it's a fee payment, must be for someone or something
#                 self.add_error(None, _("For a 'Fee Payment', either an Invoice or at least a Student must be specified."))
        
#         # Check for overpayment against a specific invoice
#         if invoice and amount is not None:
#             # Ensure invoice.balance_due is accurate before this check
#             # This might require invoice.recalculate_and_update_status(save_instance=False) if not already done
#             # For simplicity, assume invoice.balance_due is relatively current for this check.
#             if amount > invoice.balance_due:
#                 # Allowing overpayment but logging it. For strict prevention, raise ValidationError.
#                 logger.info(f"Payment form: Amount {amount} exceeds invoice {invoice.invoice_number} balance_due {invoice.balance_due}.")
#                 # self.add_error('amount', _(f"Payment amount ({amount}) exceeds the invoice balance ({invoice.balance_due})."))

#         return cleaned_data


# apps/payments/forms.py
from django import forms
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from decimal import Decimal
from django.core.validators import MinValueValidator
from django.conf import settings # For STAFF_USER_MODEL

from .models import Payment, PaymentMethod # Assuming PaymentMethod is in current app
from apps.students.models import Student
from apps.fees.models import Invoice # Ensure Invoice and its InvoiceStatus are defined
from apps.schools.models import AcademicYear

import logging
logger = logging.getLogger(__name__)

STAFF_USER_MODEL = settings.STAFF_USER_MODEL # From settings

# class PaymentForm(forms.ModelForm):
#     class Meta:
#         model = Payment
#         fields = [
#             'parent_payer',       # If you added this for who is paying
#             'student',            # If you kept this for direct student payments
#             'payment_method',
#             'academic_year',
#             'amount',
#             'payment_date',
#             'transaction_reference', # CORRECTED FIELD NAME
#             'notes',
#             'payment_type',
#             'status',
#             'processed_by_staff', # Or 'recorded_by' if that's your field name
#             # 'unallocated_amount' is usually calculated, not directly set by user in this form
#         ]
#         widgets = { # Example of setting widgets for all fields if not customized above
#             'parent_payer': forms.Select(attrs={'class': 'form-select form-select-sm select2-field'}),
#             'student': forms.Select(attrs={'class': 'form-select form-select-sm select2-field'}),
#             'payment_method': forms.Select(attrs={'class': 'form-select form-select-sm select2-field'}),
#             'academic_year': forms.Select(attrs={'class': 'form-select form-select-sm select2-field'}),
#             'amount': forms.NumberInput(attrs={'class': 'form-control form-control-sm text-end', 'step': '0.01'}),
#             # 'payment_date' is customized above
#             'transaction_reference': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
#             'notes': forms.Textarea(attrs={'class': 'form-control form-control-sm', 'rows': 3}),
#             'payment_type': forms.Select(attrs={'class': 'form-select form-select-sm'}),
#             'status': forms.Select(attrs={'class': 'form-select form-select-sm'}),
#             'processed_by_staff': forms.Select(attrs={'class': 'form-select form-select-sm select2-field'}),
#         }
#         labels = { # Define any labels that need to be different from model's verbose_name
#             'academic_year': _("Payment for Academic Year"),
#             'invoice': _("Apply to Invoice (Optional)"),
#             'payment_date': _("Date & Time of Payment"),
#         }
#         help_texts = {
#             'invoice': _("Select an outstanding invoice if this payment is for a specific one."),
#             'amount': _("Enter the positive amount received."),
#             'reference_number': _("Optional reference like transaction ID, cheque number."),
#         }

#     def __init__(self, *args, **kwargs):
#         self.request = kwargs.pop('request', None) # Store request if passed from view
#         # If tenant scoping is not handled by django-tenants for these shared/foreign models,
#         # you might need self.tenant = self.request.tenant if self.request else None
        
#         # For pre-filling form (e.g., when "Record Payment" is clicked from an invoice or student page)
#         self.initial_invoice = kwargs.pop('invoice_instance', None)
#         self.initial_student = kwargs.pop('student_instance', None)

#         super().__init__(*args, **kwargs)

#         # --- Set initial values ---
#         if not self.instance.pk: # If creating a new payment
#             if 'payment_date' in self.fields and not self.initial.get('payment_date'):
#                 # Default to now, formatted for datetime-local input
#                 self.initial['payment_date'] = timezone.now().strftime('%Y-%m-%dT%H:%M')
            
#             if self.initial_student and 'student' in self.fields:
#                 self.initial['student'] = self.initial_student.pk
            
#             if self.initial_invoice and 'invoice' in self.fields:
#                 self.initial['invoice'] = self.initial_invoice.pk
#                 if 'student' in self.fields and self.initial_invoice.student: # Auto-select student if invoice is set
#                     self.initial['student'] = self.initial_invoice.student.pk
#                 if 'amount' in self.fields and hasattr(self.initial_invoice, 'balance_due') and not self.initial.get('amount'): # Pre-fill amount
#                     self.initial['amount'] = self.initial_invoice.balance_due
#                 if 'academic_year' in self.fields and self.initial_invoice.academic_year: # Pre-fill AY
#                     self.initial['academic_year'] = self.initial_invoice.academic_year.pk
            
#             # Default payment type and status are set on the model, but can be overridden here if needed.
#             # if 'payment_type' in self.fields and not self.initial.get('payment_type'):
#             #     self.initial['payment_type'] = Payment.PaymentType.FEE # Using model's enum
#             # if 'status' in self.fields and not self.initial.get('status'):
#             #     self.initial['status'] = Payment.PaymentStatus.COMPLETED


#         # --- Dynamically set field properties and querysets ---
#         for field_name, field_obj in self.fields.items():
#             # Set 'required' based on model field's blank status, unless explicitly set otherwise
#             model_field = self.Meta.model._meta.get_field(field_name)
#             if not field_obj.required and not model_field.blank: # If form says not required but model implies it
#                 pass # logger.warning(f"Form field '{field_name}' is not required, but model field is not blank.")
#             elif field_obj.required and model_field.blank: # If form says required but model allows blank
#                 field_obj.required = False # Make it optional in form too

#             if model_field.blank and isinstance(field_obj.widget, forms.Select):
#                 field_obj.empty_label = _("--------- (Optional)")


#         # Populate Querysets (django-tenants should scope these automatically if models are tenant-aware)
#         if 'academic_year' in self.fields:
#             self.fields['academic_year'].queryset = AcademicYear.objects.filter(is_active=True).order_by('-start_date', 'name')
#             self.fields['academic_year'].empty_label = _("Select Academic Year") # Or optional if model allows

#         if 'payment_method' in self.fields:
#             self.fields['payment_method'].queryset = PaymentMethod.objects.filter(
#                 is_active=True,
#                 linked_account__isnull=False # Only methods linked to a CoA
#             ).select_related('linked_account').order_by('name')
#             self.fields['payment_method'].empty_label = _("Select Payment Method")

#         if 'student' in self.fields:
#             self.fields['student'].queryset = Student.objects.filter(is_active=True).order_by('last_name', 'first_name')
#             if self.fields['student'].required is False: # Check current required status
#                 self.fields['student'].empty_label = _("Select Student (Optional)")
#             else:
#                 self.fields['student'].empty_label = _("Select Student")


#         # Invoice queryset: dynamic based on student, or broader if no student context yet
#         if 'invoice' in self.fields:
#             student_for_invoice_qs = None
#             if self.initial_student:
#                 student_for_invoice_qs = self.initial_student
#             elif self.initial.get('student'):
#                 try: student_for_invoice_qs = Student.objects.get(pk=self.initial['student'])
#                 except (Student.DoesNotExist, ValueError, TypeError): pass
#             elif self.instance and self.instance.pk and self.instance.student:
#                 student_for_invoice_qs = self.instance.student
            
#             outstanding_statuses = [Invoice.InvoiceStatus.SENT, Invoice.InvoiceStatus.PARTIALLY_PAID, Invoice.InvoiceStatus.OVERDUE]
#             if student_for_invoice_qs:
#                 self.fields['invoice'].queryset = Invoice.objects.filter(
#                     student=student_for_invoice_qs, status__in=outstanding_statuses
#                 ).select_related('student').order_by('-issue_date')
#             else: # No specific student context, show all open invoices for tenant
#                 self.fields['invoice'].queryset = Invoice.objects.filter(
#                     status__in=outstanding_statuses
#                 ).select_related('student').order_by('-issue_date')
            
#             if self.fields['invoice'].required is False:
#                 self.fields['invoice'].empty_label = _("Select Invoice (Optional)")
#             else:
#                 self.fields['invoice'].empty_label = _("Select Invoice")


#     def clean_amount(self):
#         amount = self.cleaned_data.get('amount')
#         if amount is not None and amount <= Decimal('0.00'):
#             raise forms.ValidationError(_("Payment amount must be a positive value."))
#         return amount

#     def clean_payment_date(self):
#         payment_date = self.cleaned_data.get('payment_date')
#         if payment_date and payment_date > timezone.now() + timezone.timedelta(minutes=5): # Allow a small grace for clock skew
#             raise forms.ValidationError(_("Payment date and time cannot be in the future."))
#         return payment_date

#     def clean(self):
#         cleaned_data = super().clean()
#         payment_type = cleaned_data.get('payment_type')
#         invoice = cleaned_data.get('invoice')
#         student_from_form = cleaned_data.get('student') # Student selected in the form
#         amount = cleaned_data.get('amount')

#         # If an invoice is selected, ensure student matches or set student from invoice
#         if invoice:
#             if not invoice.student:
#                 self.add_error('invoice', _("The selected invoice is not linked to any student. Please check the invoice."))
#             elif student_from_form and invoice.student != student_from_form:
#                 self.add_error('student', _("The selected student does not match the student on the selected invoice. The invoice belongs to %(invoice_student)s.") % {'invoice_student': invoice.student.get_full_name()})
#             elif not student_from_form: # If student field was optional and left blank, but invoice selected
#                 cleaned_data['student'] = invoice.student # Set student from invoice

#         # Re-fetch student after potential update from invoice
#         final_student = cleaned_data.get('student')

#         if payment_type == Payment.PaymentType.FEE: # Using model's TextChoices
#             if not invoice and not final_student:
#                 self.add_error(None, _("For a 'Fee Payment', either an Invoice or at least a Student must be specified."))
        
#         if invoice and amount is not None and final_student: # Check for overpayment only if all context is clear
#             if final_student == invoice.student: # Ensure we are checking against the correct student's invoice
#                 # It's better to recalculate balance_due here or ensure it's very fresh
#                 # For simplicity, assuming invoice.balance_due is accurate enough for this validation.
#                 # invoice.recalculate_balance_due() # If you have such a method
#                 if amount > invoice.balance_due + Decimal('0.01'): # Add small tolerance for float issues
#                     self.add_error('amount', _("Payment amount (%(amount)s) exceeds the invoice balance (%(balance_due)s) for %(invoice_num)s.") % {
#                         'amount': amount, 
#                         'balance_due': invoice.balance_due,
#                         'invoice_num': invoice.invoice_number
#                     })
#         return cleaned_data

#     def save(self, commit=True):
#         payment = super().save(commit=False)
#         if self.request and hasattr(self.request, 'user') and self.request.user.is_authenticated:
#             # Check if request.user is a StaffUser before assigning to recorded_by
#             # This assumes your settings.AUTH_USER_MODEL_STAFF points to StaffUser
#             try:
#                 StaffUserModelClass = payment._meta.get_field('recorded_by').remote_field.model
#                 if isinstance(self.request.user, StaffUserModelClass):
#                     payment.recorded_by = self.request.user
#                 elif hasattr(self.request, 'effective_tenant_user') and isinstance(self.request.effective_tenant_user, StaffUserModelClass):
#                     payment.recorded_by = self.request.effective_tenant_user
#                 else:
#                     logger.warning(f"PaymentForm: Could not set 'recorded_by'. request.user is not a StaffUser. User: {self.request.user}")
#             except Exception as e:
#                 logger.error(f"PaymentForm: Error determining user type for 'recorded_by': {e}")

#         if commit:
#             payment.save()
#             # If payment affects invoice balance, update invoice here or via a signal
#             if payment.invoice and payment.status == Payment.PaymentStatus.COMPLETED:
#                 payment.invoice.update_payment_status() # Assuming Invoice has such a method
#         return payment



# D:\school_fees_saas_v2\apps\payments\forms.py
from django import forms
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from decimal import Decimal

# Ensure correct model imports based on your project structure
from .models import Payment, PaymentMethod
from apps.students.models import Student, ParentUser
from apps.schools.models import AcademicYear
from apps.fees.models import Invoice # For invoice choice field if used for pre-fill or direct link
from apps.schools.models import StaffUser # For processed_by_staff

import logging
logger = logging.getLogger(__name__)

class PaymentForm(forms.ModelForm):
    # --- Fields that might need more specific widgets or querysets ---
    payment_date = forms.DateTimeField(
        initial=timezone.now, # Default to current date and time
        widget=forms.DateTimeInput(attrs={'type': 'datetime-local', 'class': 'form-control form-control-sm'}),
        label=_("Payment Date & Time")
    )
    # This field is for selecting an invoice to pre-fill data or to directly link (if your model retains a single invoice FK)
    # If Payment model has NO direct invoice FK, this field is purely for UI pre-fill.
    # Let's assume for now Payment model does NOT have a direct invoice FK.
    # This field can be used to help select the student and pre-fill amount.
    invoice_to_apply_to = forms.ModelChoiceField(
        queryset=Invoice.objects.none(), # Populated dynamically in __init__
        required=False,
        label=_("Apply to Specific Invoice (Optional)"),
        help_text=_("Selecting an invoice will pre-fill student and amount. Payment will be allocated later."),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm select2-field'})
    )

    class Meta:
        model = Payment
        fields = [
            'parent_payer',         # Who is ultimately paying (Parent)
            'student',              # Which student is this payment primarily for (can be derived from invoice)
            'payment_method',
            'academic_year',
            'amount',
            'payment_date',
            'transaction_reference',
            'payment_type',
            'status',               # Staff can set status (e.g. 'Completed' for cash)
            'notes',
            'processed_by_staff',   # Staff member recording this payment
            # 'unallocated_amount' - usually not directly set by user in this form
            # 'created_by' - set in the view based on request.user
        ]
        widgets = {
            'parent_payer': forms.Select(attrs={'class': 'form-select form-select-sm select2-field'}),
            'student': forms.Select(attrs={'class': 'form-select form-select-sm select2-field'}),
            'payment_method': forms.Select(attrs={'class': 'form-select form-select-sm select2-field'}),
            'academic_year': forms.Select(attrs={'class': 'form-select form-select-sm select2-field'}),
            'amount': forms.NumberInput(attrs={'class': 'form-control form-control-sm text-end', 'step': '0.01'}),
            'transaction_reference': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
            'payment_type': forms.Select(attrs={'class': 'form-select form-select-sm'}),
            'status': forms.Select(attrs={'class': 'form-select form-select-sm'}),
            'notes': forms.Textarea(attrs={'class': 'form-control form-control-sm', 'rows': 3}),
            'processed_by_staff': forms.Select(attrs={'class': 'form-select form-select-sm select2-field'}),
        }
        labels = { # Customize labels if needed
            'parent_payer': _("Paying Parent (Optional)"),
            'student': _("Student this payment primarily concerns (Optional if Invoice selected)"),
            'academic_year': _("Payment for Academic Year"),
            'transaction_reference': _("Payment Reference (e.g., Txn ID, Cheque No)"),
            'processed_by_staff': _("Payment Processed/Recorded By Staff"),
        }

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None)
        # For pre-filling form from context (e.g., from an invoice detail page "Record Payment" button)
        initial_invoice_pk = kwargs.pop('initial_invoice_pk', None) 
        initial_student_pk = kwargs.pop('initial_student_pk', None)

        super().__init__(*args, **kwargs)

        # --- Set dynamic querysets ---
        if 'parent_payer' in self.fields:
            self.fields['parent_payer'].queryset = ParentUser.objects.all().order_by('user__last_name', 'user__first_name', 'user__email')
            self.fields['parent_payer'].required = False # Making it optional
            self.fields['parent_payer'].empty_label = _("Select Parent (Optional)")


        if 'student' in self.fields:
            self.fields['student'].queryset = Student.objects.filter(is_active=True).order_by('last_name', 'first_name')
            self.fields['student'].required = False # Payment might be general from parent
            self.fields['student'].empty_label = _("Select Student (Optional)")


        if 'academic_year' in self.fields:
            # Assuming AcademicYear is tenant-specific by schema or has tenant FK handled by manager
            self.fields['academic_year'].queryset = AcademicYear.objects.filter(is_active=True).order_by('-start_date')
            self.fields['academic_year'].empty_label = _("Select Academic Year")


        if 'payment_method' in self.fields:
            self.fields['payment_method'].queryset = PaymentMethod.objects.filter(
                is_active=True, linked_account__isnull=False
            ).select_related('linked_account').order_by('name')
            self.fields['payment_method'].empty_label = _("Select Payment Method")

        if 'processed_by_staff' in self.fields:
            self.fields['processed_by_staff'].queryset = StaffUser.objects.filter(is_active=True, user__is_active=True).select_related('user').order_by('user__last_name', 'user__first_name')
            self.fields['processed_by_staff'].required = True # Usually staff recording is required
            if self.request and hasattr(self.request.user, 'staffuser_profile') and not self.initial.get('processed_by_staff'):
                try: # Check if request.user is a StaffUser
                    self.initial['processed_by_staff'] = self.request.user.staffuser_profile # Or just self.request.user if StaffUser is AUTH_USER_MODEL for staff
                except AttributeError: pass # request.user might not be staff

        # --- Populate Invoice Choice Field (for pre-fill assistance) ---
        # This field is not directly on the Payment model in the consolidated version
        # but helps in the UI to select student/amount.
        outstanding_statuses = [Invoice.InvoiceStatus.SENT, Invoice.InvoiceStatus.PARTIALLY_PAID, Invoice.InvoiceStatus.OVERDUE]
        invoice_queryset = Invoice.objects.filter(status__in=outstanding_statuses).exclude(
            Q(total_amount__lte=F('amount_paid'))
        ).select_related('student__user', 'student__current_class').order_by('student__user__last_name', '-issue_date')
        self.fields['invoice_to_apply_to'].queryset = invoice_queryset
        self.fields['invoice_to_apply_to'].empty_label = _("Select Invoice to Pre-fill (Optional)")


        # --- Set initial values if creating a new payment ---
        if not self.instance.pk: # If creating a new payment
            if 'payment_date' in self.fields and not self.initial.get('payment_date'):
                self.initial['payment_date'] = timezone.now().strftime('%Y-%m-%dT%H:%M')
            
            if initial_student_pk and 'student' in self.fields:
                self.initial['student'] = initial_student_pk
            
            if initial_invoice_pk and 'invoice_to_apply_to' in self.fields:
                try:
                    invoice_instance = Invoice.objects.get(pk=initial_invoice_pk)
                    self.initial['invoice_to_apply_to'] = invoice_instance.pk
                    if 'student' in self.fields and invoice_instance.student:
                        self.initial['student'] = invoice_instance.student.pk
                        if invoice_instance.student.parents.exists() and 'parent_payer' in self.fields: # Assuming M2M from Student to ParentUser
                            self.initial['parent_payer'] = invoice_instance.student.parents.first().pk
                    if 'amount' in self.fields and hasattr(invoice_instance, 'balance_due'):
                        self.initial['amount'] = invoice_instance.balance_due
                    if 'academic_year' in self.fields and invoice_instance.academic_year:
                        self.initial['academic_year'] = invoice_instance.academic_year.pk
                except Invoice.DoesNotExist:
                    pass
            
            # Default status for manually recorded payments is often 'COMPLETED'
            if 'status' in self.fields and not self.initial.get('status'):
                self.initial['status'] = Payment.STATUS_COMPLETED

        # Make fields optional/required based on model's `blank` attribute
        for field_name, field_obj in self.fields.items():
            if field_name in self.Meta.fields: # Process only model fields
                model_field = self.Meta.model._meta.get_field(field_name)
                if model_field.blank and field_obj.required:
                    field_obj.required = False
                    if isinstance(field_obj.widget, forms.Select):
                        field_obj.empty_label = _("--------- (Optional)")


    def clean_amount(self):
        amount = self.cleaned_data.get('amount')
        if amount is not None and amount <= Decimal('0.00'):
            raise forms.ValidationError(_("Payment amount must be positive."))
        return amount

    def clean_payment_date(self):
        payment_date = self.cleaned_data.get('payment_date')
        # Allow payment date to be slightly in the future for back-office entry, but not too far.
        # Or restrict to today or past. For now, just check it exists.
        if payment_date and payment_date.date() > (timezone.now() + timezone.timedelta(days=7)).date(): # Example: not more than 7 days in future
            raise forms.ValidationError(_("Payment date cannot be too far in the future."))
        if payment_date and payment_date > timezone.now() + timezone.timedelta(minutes=15): # Generous grace for clock skew
            logger.warning(f"Payment date {payment_date} is in the future. Allowing with grace period.")
            # raise forms.ValidationError(_("Payment date and time cannot be in the future."))
        return payment_date

    def clean(self):
        cleaned_data = super().clean()
        invoice_to_apply = cleaned_data.get('invoice_to_apply_to') # This is the UI helper field
        student = cleaned_data.get('student')
        parent_payer = cleaned_data.get('parent_payer')
        amount = cleaned_data.get('amount')

        # If invoice_to_apply is selected, ensure student and parent_payer are consistent or set
        if invoice_to_apply:
            if not student and invoice_to_apply.student:
                cleaned_data['student'] = invoice_to_apply.student
                # Re-validate if student was changed:
                self.data = self.data.copy() # Make data mutable
                self.data[self.add_prefix('student')] = invoice_to_apply.student.pk
                
            student = cleaned_data.get('student') # Get updated student

            if student and invoice_to_apply.student != student:
                self.add_error('invoice_to_apply_to', 
                                _("This invoice belongs to %(invoice_student)s, not the selected student %(form_student)s.") % 
                                {'invoice_student': invoice_to_apply.student.get_full_name(), 'form_student': student.get_full_name()})
            
            # Try to set parent_payer from student if not already set
            if not parent_payer and student and student.parents.exists(): # Assuming Student has M2M to ParentUser called 'parents'
                cleaned_data['parent_payer'] = student.parents.first()

        final_parent_payer = cleaned_data.get('parent_payer')
        final_student = cleaned_data.get('student')

        if not final_parent_payer and not final_student:
            self.add_error(None, _("A payment must be associated with either a Paying Parent or a Student."))

        # Validation for overpayment against a single selected invoice (if invoice_to_apply_to is used meaningfully)
        if invoice_to_apply and amount is not None:
            if amount > invoice_to_apply.balance_due + Decimal('0.01'): # Small tolerance
                self.add_error('amount', _("Payment amount (%(amount)s) exceeds the selected invoice's balance (%(balance_due)s).") % {
                        'amount': amount, 
                        'balance_due': invoice_to_apply.balance_due
                    })
        return cleaned_data

    def save(self, commit=True):
        payment = super().save(commit=False)
        
        # Set created_by from the request user if available
        if self.request and hasattr(self.request, 'user') and self.request.user.is_authenticated:
            payment.created_by = self.request.user # General created_by

        # If processed_by_staff is not set and request.user is staff, set it.
        # This assumes processed_by_staff is a FK to your StaffUser model.
        if not payment.processed_by_staff_id and self.request and hasattr(self.request.user, 'staffuser_profile'):
            try:
                # Check if self.request.user is an instance of your StaffUser model
                # StaffUserModelClass = payment._meta.get_field('processed_by_staff').remote_field.model
                # if isinstance(self.request.user, StaffUserModelClass):
                payment.processed_by_staff = self.request.user.staffuser_profile # Or just self.request.user
            except AttributeError:
                logger.warning(f"Could not set processed_by_staff for payment by {self.request.user.email}, user may not be staff or staff profile missing.")


        if commit:
            payment.save()
            # After payment is saved, you would typically proceed to an allocation step
            # if the 'invoice_to_apply_to' field was used or if it's a general payment.
            # For now, this form just saves the Payment record.
            # The service function record_parent_payment handles allocations for online flow.
            # For manual staff entry, a separate allocation UI might be needed after this.
        return payment
    
    
    
class PaymentMethodForm(forms.ModelForm):
    class Meta:
        model = PaymentMethod
        fields = ['name', 'type', 'description', 'is_active', 'linked_account']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'type': forms.Select(attrs={'class': 'form-select'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'linked_account': forms.Select(attrs={'class': 'form-select select2-coa'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if 'linked_account' in self.fields:
            # This is where you filter the ChartOfAccount for the linked_account field
            actual_coa_code_field_name = 'account_code' # VERIFY THIS for ChartOfAccount model
            try:
                logger.info("--- PaymentMethodForm: Initializing 'linked_account' field ---")
                self.fields['linked_account'].queryset = Account.objects.filter(
                    account_type__classification='ASSET', # Payment methods usually link to Asset accounts (Cash/Bank)
                    is_active=True,
                    is_control_account=False 
                ).select_related('account_type').order_by(actual_coa_code_field_name)
                
                count = self.fields['linked_account'].queryset.count()
                logger.info(f"Filter for 'linked_account' successful. Found {count} ASSET accounts.")

                self.fields['linked_account'].label_from_instance = lambda obj: (
                    f"{getattr(obj, actual_coa_code_field_name, 'N/A_CODE')} - "
                    f"{obj.name} "
                    f"({obj.account_type.name if obj.account_type else 'N/A Type'})"
                )
            except Exception as e:
                logger.error(f"!!! Error setting queryset for 'linked_account' in PaymentMethodForm: {e}", exc_info=True)
                self.fields['linked_account'].queryset = Account.objects.filter(is_active=True).order_by('code')



class PaymentFilterForm(django_filters.FilterSet):
    # Define filters as they were in your views.py
    student = django_filters.ModelChoiceFilter(
        queryset=Student.objects.all(), # View's get_queryset might refine this further
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        label="Student"
    )
    payment_method = django_filters.ModelChoiceFilter(
        queryset=PaymentMethod.objects.all(),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        label="Payment Method"
    )
    payment_date = django_filters.DateFromToRangeFilter( # For filtering by date range
        widget=django_filters.widgets.RangeWidget(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        label="Payment Date Range"
    )
    # Add other filters as needed, e.g., invoice number if payments are linked
    # invoice_number = django_filters.CharFilter(field_name='invoice__invoice_number', lookup_expr='icontains', ...)

    class Meta:
        model = Payment
        # Fields from Payment model to filter on
        fields = ['student', 'payment_method', 'payment_date', 'payment_type'] # Add 'payment_type' if it exists

    def __init__(self, *args, **kwargs):
        # The view (FilterView) passes request to the FilterSet
        # No need to pop 'request' here if using FilterView's default instantiation
        super().__init__(*args, **kwargs)
        # Filter querysets based on current tenant if necessary,
        # though django-tenants should handle this if queries are made
        # after middleware has set the schema.
        # Example if direct filtering needed:
        if hasattr(self.request, 'tenant') and self.request.tenant:
            self.filters['student'].queryset = Student.objects.filter(is_active=True).order_by('last_name', 'first_name')
            self.filters['payment_method'].queryset = PaymentMethod.objects.filter(is_active=True).order_by('name')
        else: # Fallback for safety, though shouldn't be hit in tenant views
            self.filters['student'].queryset = Student.objects.none()
            self.filters['payment_method'].queryset = PaymentMethod.objects.none()

        # Customize labels if needed (FilterSet uses field's verbose_name by default)
        self.filters['student'].field.label_from_instance = lambda obj: f"{obj.full_name_admission_id}"
        
        
