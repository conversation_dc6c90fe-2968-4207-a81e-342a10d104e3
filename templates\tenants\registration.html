{# D:\school_fees_saas_v2\templates\tenants\registration.html #}
{% extends "public_base.html" %}

{% load static i18n widget_tweaks tenant_extras %}

{% block title %}{% trans "Register Your School" %}{% endblock %}

{% block content %}
<div class="container mt-5 mb-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-7">
            <div class="card shadow-lg">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">{% trans "Register Your School & Start Your Trial!" %}</h4>
                </div>
                <div class="card-body p-4 p-md-5">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">{{ form.non_field_errors|striptags }}</div>
                    {% endif %}

                    <form method="post" novalidate>
                        {% csrf_token %}
                        <fieldset class="mb-4">
                            <legend class="h5">{% trans "School Information" %}</legend>
                            <div class="mb-3">
                                <label for="{{ form.school_name.id_for_label }}" class="form-label">{{ form.school_name.label }}</label>
                                {% render_field form.school_name class+="form-control" placeholder="e.g., Bright Future Academy" %}
                                {{ form.school_name.errors }}
                            </div>
                            <div class="mb-3">
                                <label for="{{ form.schema_name.id_for_label }}" class="form-label">{{ form.schema_name.label }}</label>
                                {% render_field form.schema_name class+="form-control" placeholder="e.g., brightfuture" %}
                                <div class="form-text">{% trans "Your portal address will be: " %} {{ form.schema_name.value|default_if_none:"yourprefix" }}.<strong>{% get_base_domain %}</strong></div>
                                {{ form.schema_name.errors }}
                            </div>
                        </fieldset>

                        <fieldset class="mb-4">
                            <legend class="h5">{% trans "Your Administrator Account" %}</legend>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.owner_first_name.id_for_label }}" class="form-label">{{ form.owner_first_name.label }}</label>
                                    {% render_field form.owner_first_name class+="form-control" %}
                                    {{ form.owner_first_name.errors }}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.owner_last_name.id_for_label }}" class="form-label">{{ form.owner_last_name.label }}</label>
                                    {% render_field form.owner_last_name class+="form-control" %}
                                    {{ form.owner_last_name.errors }}
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="{{ form.owner_email.id_for_label }}" class="form-label">{{ form.owner_email.label }}</label>
                                {% render_field form.owner_email class+="form-control" placeholder="<EMAIL>" %}
                                {{ form.owner_email.errors }}
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.owner_password1.id_for_label }}" class="form-label">{{ form.owner_password1.label }}</label>
                                    {% render_field form.owner_password1 class+="form-control" %}
                                    {{ form.owner_password1.errors }}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.owner_password2.id_for_label }}" class="form-label">{{ form.owner_password2.label }}</label>
                                    {% render_field form.owner_password2 class+="form-control" %}
                                    {{ form.owner_password2.errors }}
                                </div>
                            </div>
                        </fieldset>

                        <fieldset class="mb-4">
                            <legend class="h5">{% trans "Select Your Plan" %}</legend>
                            <div class="mb-3">
                                {{ form.plan.errors }}
                                {% for radio in form.plan %}
                                <div class="form-check mb-2 plan-option" 
                                    data-price-monthly="{{ radio.choice_value.instance.price_monthly|default_if_none:'0.00' }}"
                                    data-price-annually="{{ radio.choice_value.instance.price_annually|default_if_none:'0.00' }}"
                                    data-trial-days="{{ radio.choice_value.instance.trial_period_days|default_if_none:'0' }}">
                                    {{ radio.tag }}
                                    <label for="{{ radio.id_for_label }}" class="form-check-label">
                                        <strong>{{ radio.choice_label }}</strong>
                                        {% with plan_instance=radio.choice_value.instance %}
                                            <small class="d-block text-muted">
                                                {% if plan_instance.trial_period_days > 0 %}
                                                    {% blocktrans with days=plan_instance.trial_period_days %}Includes a {{ days }}-day free trial.{% endblocktrans %}
                                                {% endif %}
                                                {{ plan_instance.description|truncatewords:20 }}
                                            </small>
                                            <div class="plan-pricing mt-1">
                                                {# Prices will be updated by JS #}
                                            </div>
                                            <ul class="list-unstyled list-inline list-inline-dotted small mt-1">
                                                {% for feature_item in plan_instance.features.all|slice:":3" %}
                                                    <li class="list-inline-item"><i class="bi bi-check-circle-fill text-success me-1"></i>{{ feature_item.name }}</li>
                                                {% empty %}
                                                    <li class="list-inline-item">{% trans "Core Features" %}</li>
                                                {% endfor %}
                                                {% if plan_instance.features.all.count > 3 %}<li class="list-inline-item">& {% trans "more" %}...</li>{% endif %}
                                            </ul>
                                        {% endwith %}
                                    </label>
                                </div>
                                {% endfor %}
                            </div>
                            <div class="mb-3">
                                <label class="form-label d-block mb-2">{% trans "Billing Cycle" %}:</label>
                                {{ form.billing_cycle.errors }}
                                {% for radio_cycle in form.billing_cycle %}
                                <div class="form-check form-check-inline">
                                    {{ radio_cycle.tag }}
                                    <label for="{{ radio_cycle.id_for_label }}" class="form-check-label">{{ radio_cycle.choice_label }}</label>
                                </div>
                                {% endfor %}
                            </div>
                            <div id="selected-plan-price-display" class="alert alert-info" style="display:none;">
                                {% trans "Selected Plan Price" %}: <strong id="plan-price"></strong>
                            </div>
                        </fieldset>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-check2-circle me-2"></i> {% trans "Complete Registration" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}

{% block page_specific_js %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function () {
    const planRadios = document.querySelectorAll('input[name="plan"]');
    const cycleRadios = document.querySelectorAll('input[name="billing_cycle"]');
    const priceDisplayDiv = document.getElementById('selected-plan-price-display');
    const priceStrong = document.getElementById('plan-price');
    const currencySymbol = "{{ school_currency_symbol|default:'$' }}"; // Get from context if available globally, or hardcode

    function updatePriceDisplay() {
        let selectedPlanRadio = document.querySelector('input[name="plan"]:checked');
        let selectedCycleRadio = document.querySelector('input[name="billing_cycle"]:checked');

        if (selectedPlanRadio && selectedCycleRadio) {
            const planOptionDiv = selectedPlanRadio.closest('.plan-option');
            const priceMonthly = parseFloat(planOptionDiv.dataset.priceMonthly);
            const priceAnnually = parseFloat(planOptionDiv.dataset.priceAnnually);
            const trialDays = parseInt(planOptionDiv.dataset.trialDays);
            const cycle = selectedCycleRadio.value;
            
            let priceText = "";
            if (trialDays > 0) {
                priceText += `{% blocktrans with days=999 %}First ${trialDays} days FREE, then {% endblocktrans %}`.replace('999', trialDays);
            }

            if (cycle === 'MONTHLY') {
                priceText += `${currencySymbol}${priceMonthly.toFixed(2)} {% trans "/ month" %}`;
            } else if (cycle === 'ANNUALLY') {
                priceText += `${currencySymbol}${priceAnnually.toFixed(2)} {% trans "/ year" %}`;
            }
            
            priceStrong.textContent = priceText;
            priceDisplayDiv.style.display = 'block';

        } else {
            priceDisplayDiv.style.display = 'none';
        }
    }

    planRadios.forEach(radio => radio.addEventListener('change', updatePriceDisplay));
    cycleRadios.forEach(radio => radio.addEventListener('change', updatePriceDisplay));

    // Initial update in case of pre-selection or errors
    updatePriceDisplay();
});
</script>
{% endblock %}





























{% comment %} {# D:\school_fees_saas_v2\templates\tenants\registration.html #}
{% extends "public_base.html" %}
{% load static %}

{% block title %}{{ view_title|default:"Register Your School" }}{% endblock %}

{% block page_specific_css %}
    {# If you have specific styles for public forms, link them here #}
    {# Example: <link rel="stylesheet" href="{% static 'css/public_form_styles.css' %}"> #}
    {# Using tenant_form_styles for consistency for now #}
    <link rel="stylesheet" href="{% static 'css/tenant_form_styles.css' %}">
{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-7">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">{{ view_title|default:"Register Your School" }}</h4>
                </div>
                <div class="card-body p-4">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" novalidate>
                        {% csrf_token %}
                        <p class="text-muted small mb-3">Fields marked with <span class="text-danger">*</span> are required.</p>

                        {# Display form non-field errors (e.g., from form.clean()) #}
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    {{ error|escape }}<br>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <fieldset class="mb-4">
                            <legend class="h5 mb-3 border-bottom pb-2">School Information</legend>
                            {# School Name #}
                            <div class="mb-3">
                                {{ form.school_name.label_tag }}
                                {{ form.school_name }} {# Widget should have form-control class #}
                                {% if form.school_name.help_text %}<small class="form-text text-muted d-block mt-1">{{ form.school_name.help_text }}</small>{% endif %}
                                {% if form.school_name.errors %}<div class="invalid-feedback d-block">{{ form.school_name.errors|striptags }}</div>{% endif %}
                            </div>

                            {# Subdomain with Input Group #}
                            <div class="mb-3">
                                {{ form.subdomain.label_tag }}
                                <div class="input-group">
                                    {{ form.subdomain }} {# Input field - widget should have form-control #}
                                    {# Append the base domain extension #}
                                    {% if tenant_base_hostname_for_display %}
                                        <span class="input-group-text">.{{ tenant_base_hostname_for_display }}</span>
                                    {% endif %}
                                </div>
                                {# Help text from the form will appear below #}
                                {% if form.subdomain.help_text %}<small class="form-text text-muted d-block mt-1">{{ form.subdomain.help_text }}</small>{% endif %}
                                {% if form.subdomain.errors %}<div class="invalid-feedback d-block">{{ form.subdomain.errors|striptags }}</div>{% endif %}
                            </div>
                        </fieldset>

                        <fieldset class="mt-4">
                            <legend class="h5 mb-3 border-bottom pb-2">Administrator Account Details</legend>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.admin_first_name.label_tag }}
                                    {{ form.admin_first_name }}
                                    {% if form.admin_first_name.help_text %}<small class="form-text text-muted d-block mt-1">{{ form.admin_first_name.help_text }}</small>{% endif %}
                                    {% if form.admin_first_name.errors %}<div class="invalid-feedback d-block">{{ form.admin_first_name.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    {{ form.admin_last_name.label_tag }}
                                    {{ form.admin_last_name }}
                                    {% if form.admin_last_name.help_text %}<small class="form-text text-muted d-block mt-1">{{ form.admin_last_name.help_text }}</small>{% endif %}
                                    {% if form.admin_last_name.errors %}<div class="invalid-feedback d-block">{{ form.admin_last_name.errors|striptags }}</div>{% endif %}
                                </div>
                            </div>
                            <div class="mb-3">
                                {{ form.admin_email.label_tag }}
                                {{ form.admin_email }}
                                {% if form.admin_email.help_text %}<small class="form-text text-muted d-block mt-1">{{ form.admin_email.help_text }}</small>{% endif %}
                                {% if form.admin_email.errors %}<div class="invalid-feedback d-block">{{ form.admin_email.errors|striptags }}</div>{% endif %}
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.admin_password1.label_tag }}
                                    {{ form.admin_password1 }}
                                    {% if form.admin_password1.help_text %}<small class="form-text text-muted d-block mt-1">{{ form.admin_password1.help_text }}</small>{% endif %}
                                    {% if form.admin_password1.errors %}<div class="invalid-feedback d-block">{{ form.admin_password1.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    {{ form.admin_password2.label_tag }}
                                    {{ form.admin_password2 }}
                                    {% if form.admin_password2.help_text %}<small class="form-text text-muted d-block mt-1">{{ form.admin_password2.help_text }}</small>{% endif %}
                                    {% if form.admin_password2.errors %}<div class="invalid-feedback d-block">{{ form.admin_password2.errors|striptags }}</div>{% endif %}
                                </div>
                            </div>
                        </fieldset>

                        <button type="submit" class="btn btn-success w-100 btn-lg mt-4">Register School</button>
                    </form>
                    <div class="text-center mt-3">
                        <p>Already registered a school? <a href="{% url 'users:school_admin_login' %}">Administrator Login</a>.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}
 {% endcomment %}

