{% extends "base.html" %}
{% load static i18n %}

{% block title %}{% trans "School Events" %} - {% trans "Calendar" %}{% endblock %}

{% block extra_css %}
<style>
    .events-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 10px 10px 0 0;
    }
    
    .event-card {
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .event-card:hover {
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }
    
    .event-date {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        text-align: center;
        min-width: 80px;
    }
    
    .event-day {
        font-size: 1.5rem;
        font-weight: bold;
        color: #007bff;
        line-height: 1;
    }
    
    .event-month {
        font-size: 0.875rem;
        color: #6c757d;
        text-transform: uppercase;
    }
    
    .priority-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .priority-low { background: #d4edda; color: #155724; }
    .priority-medium { background: #fff3cd; color: #856404; }
    .priority-high { background: #f8d7da; color: #721c24; }
    .priority-urgent { background: #f5c6cb; color: #721c24; }
    
    .filter-sidebar {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1.5rem;
        height: fit-content;
        position: sticky;
        top: 20px;
    }
    
    .event-meta {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .upcoming-badge {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .past-event {
        opacity: 0.7;
    }
    
    .ongoing-event {
        border-left-color: #ffc107 !important;
        background: linear-gradient(to right, #fff3cd, #ffffff);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="card shadow-sm mb-4">
        <div class="events-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-calendar-event me-2"></i>
                        {% trans "School Events" %}
                    </h1>
                    <p class="mb-0 opacity-75">{% trans "Stay updated with all school activities and important dates" %}</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <a href="{% url 'school_calendar:calendar' %}" class="btn btn-light">
                        <i class="bi bi-calendar me-2"></i>{% trans "Calendar View" %}
                    </a>
                    {% if can_manage %}
                    <a href="{% url 'school_calendar:admin_event_create' %}" class="btn btn-outline-light">
                        <i class="bi bi-plus-circle me-2"></i>{% trans "Add Event" %}
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Filter Sidebar -->
        <div class="col-lg-3 mb-4">
            <div class="filter-sidebar">
                <h5 class="mb-3">
                    <i class="bi bi-funnel me-2"></i>
                    {% trans "Filter Events" %}
                </h5>
                
                <form method="get">
                    <!-- Event Type Filter -->
                    <div class="mb-3">
                        <label class="form-label">{% trans "Event Type" %}</label>
                        <select name="type" class="form-select form-select-sm">
                            <option value="">{% trans "All Types" %}</option>
                            {% for value, label in event_types %}
                            <option value="{{ value }}" {% if current_filters.type == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- Category Filter -->
                    <div class="mb-3">
                        <label class="form-label">{% trans "Category" %}</label>
                        <select name="category" class="form-select form-select-sm">
                            <option value="">{% trans "All Categories" %}</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}" {% if current_filters.category == category.id|stringformat:"s" %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- Date Filter -->
                    <div class="mb-3">
                        <label class="form-label">{% trans "Date Range" %}</label>
                        <select name="date_filter" class="form-select form-select-sm">
                            <option value="">{% trans "All Dates" %}</option>
                            <option value="upcoming" {% if current_filters.date_filter == 'upcoming' %}selected{% endif %}>
                                {% trans "Upcoming Events" %}
                            </option>
                            <option value="this_month" {% if current_filters.date_filter == 'this_month' %}selected{% endif %}>
                                {% trans "This Month" %}
                            </option>
                            <option value="past" {% if current_filters.date_filter == 'past' %}selected{% endif %}>
                                {% trans "Past Events" %}
                            </option>
                        </select>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="bi bi-search me-1"></i>{% trans "Apply Filters" %}
                        </button>
                        <a href="{% url 'school_calendar:event_list' %}" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-x-circle me-1"></i>{% trans "Clear Filters" %}
                        </a>
                    </div>
                </form>
                
                <!-- Quick Stats -->
                <div class="mt-4 pt-3 border-top">
                    <h6 class="mb-3">{% trans "Quick Stats" %}</h6>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="text-primary h5 mb-1">{{ events|length }}</div>
                            <small class="text-muted">{% trans "Total" %}</small>
                        </div>
                        <div class="col-6">
                            <div class="text-success h5 mb-1">
                                {% for event in events %}{% if event.is_upcoming %}{{ forloop.counter }}{% endif %}{% endfor %}
                            </div>
                            <small class="text-muted">{% trans "Upcoming" %}</small>
                        </div>
                    </div>
                </div>
                
                <!-- Category Legend -->
                {% if categories %}
                <div class="mt-4 pt-3 border-top">
                    <h6 class="mb-3">{% trans "Categories" %}</h6>
                    {% for category in categories %}
                    <div class="d-flex align-items-center mb-2">
                        <div class="me-2" style="width: 16px; height: 16px; background-color: {{ category.color }}; border-radius: 3px;"></div>
                        <small>{{ category.name }}</small>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Events List -->
        <div class="col-lg-9">
            {% if events %}
            <div class="row">
                {% for event in events %}
                <div class="col-12 mb-4">
                    <div class="card event-card h-100 {% if event.is_ongoing %}ongoing-event{% elif event.is_past %}past-event{% endif %}" 
                         style="border-left-color: {{ event.category.color|default:'#007bff' }}"
                         onclick="window.location.href='{% url 'school_calendar:event_detail' event.pk %}'">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <!-- Date Column -->
                                <div class="col-auto">
                                    <div class="event-date">
                                        <div class="event-day">{{ event.start_date.day }}</div>
                                        <div class="event-month">{{ event.start_date|date:"M" }}</div>
                                    </div>
                                </div>
                                
                                <!-- Event Details Column -->
                                <div class="col">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h5 class="mb-1">{{ event.title }}</h5>
                                        <div class="d-flex align-items-center gap-2">
                                            {% if event.is_upcoming %}
                                            <span class="upcoming-badge">{% trans "Upcoming" %}</span>
                                            {% elif event.is_ongoing %}
                                            <span class="badge bg-warning">{% trans "Ongoing" %}</span>
                                            {% elif event.is_past %}
                                            <span class="badge bg-secondary">{% trans "Past" %}</span>
                                            {% endif %}
                                            
                                            {% if event.requires_rsvp %}
                                            <span class="badge bg-info">{% trans "RSVP" %}</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    <div class="d-flex align-items-center gap-3 mb-2">
                                        <span class="badge bg-secondary">{{ event.get_event_type_display }}</span>
                                        <span class="priority-badge priority-{{ event.priority|lower }}">
                                            {{ event.get_priority_display }}
                                        </span>
                                        {% if event.category %}
                                        <span class="badge" style="background-color: {{ event.category.color }}">
                                            {{ event.category.name }}
                                        </span>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="event-meta mb-2">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <i class="bi bi-calendar me-1"></i>
                                                {% if event.is_multi_day %}
                                                    {{ event.start_date|date:"M j" }} - {{ event.end_date|date:"M j, Y" }}
                                                {% else %}
                                                    {{ event.start_date|date:"l, M j, Y" }}
                                                {% endif %}
                                            </div>
                                            <div class="col-md-6">
                                                {% if not event.is_all_day and event.start_time %}
                                                <i class="bi bi-clock me-1"></i>
                                                {{ event.start_time|time:"g:i A" }}
                                                {% if event.end_time %}
                                                    - {{ event.end_time|time:"g:i A" }}
                                                {% endif %}
                                                {% else %}
                                                <i class="bi bi-sun me-1"></i>{% trans "All Day" %}
                                                {% endif %}
                                            </div>
                                        </div>
                                        {% if event.location %}
                                        <div class="mt-1">
                                            <i class="bi bi-geo-alt me-1"></i>{{ event.location }}
                                        </div>
                                        {% endif %}
                                    </div>
                                    
                                    {% if event.description %}
                                    <p class="text-muted mb-0">{{ event.description|truncatewords:25 }}</p>
                                    {% endif %}
                                </div>
                                
                                <!-- Action Column -->
                                <div class="col-auto">
                                    <div class="d-flex flex-column gap-2">
                                        <a href="{% url 'school_calendar:event_detail' event.pk %}" class="btn btn-outline-primary btn-sm">
                                            <i class="bi bi-eye me-1"></i>{% trans "View" %}
                                        </a>
                                        {% if can_manage %}
                                        <a href="{% url 'school_calendar:admin_event_edit' event.pk %}" class="btn btn-outline-secondary btn-sm">
                                            <i class="bi bi-pencil me-1"></i>{% trans "Edit" %}
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        {% if event.requires_rsvp %}
                        <div class="card-footer bg-light">
                            <small class="text-muted">
                                <i class="bi bi-people me-1"></i>
                                {% trans "RSVP Required" %}
                                {% if event.max_attendees %}
                                - {% trans "Max:" %} {{ event.max_attendees }} {% trans "attendees" %}
                                {% endif %}
                            </small>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
            <div class="row mt-4">
                <div class="col-12">
                    <nav aria-label="Events pagination">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.date_filter %}&date_filter={{ request.GET.date_filter }}{% endif %}">
                                    <i class="bi bi-chevron-left"></i> {% trans "Previous" %}
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.date_filter %}&date_filter={{ request.GET.date_filter }}{% endif %}">{{ num }}</a>
                            </li>
                            {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.date_filter %}&date_filter={{ request.GET.date_filter }}{% endif %}">
                                    {% trans "Next" %} <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            </div>
            {% endif %}
            
            {% else %}
            <!-- No Events Found -->
            <div class="text-center py-5">
                <i class="bi bi-calendar-x display-1 text-muted mb-3"></i>
                <h4 class="text-muted">{% trans "No Events Found" %}</h4>
                <p class="text-muted">
                    {% if current_filters.type or current_filters.category or current_filters.date_filter %}
                        {% trans "Try adjusting your filters to see more events." %}
                    {% else %}
                        {% trans "There are no events scheduled at this time." %}
                    {% endif %}
                </p>
                <div class="mt-3">
                    {% if current_filters.type or current_filters.category or current_filters.date_filter %}
                    <a href="{% url 'school_calendar:event_list' %}" class="btn btn-primary">
                        <i class="bi bi-x-circle me-2"></i>{% trans "Clear Filters" %}
                    </a>
                    {% endif %}
                    <a href="{% url 'school_calendar:calendar' %}" class="btn btn-outline-primary">
                        <i class="bi bi-calendar me-2"></i>{% trans "View Calendar" %}
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit filter form on change
    const filterSelects = document.querySelectorAll('.filter-sidebar select');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
    
    // Add click handlers for event cards
    const eventCards = document.querySelectorAll('.event-card');
    eventCards.forEach(card => {
        card.addEventListener('click', function(e) {
            // Don't trigger if clicking on buttons
            if (!e.target.closest('.btn')) {
                const url = this.getAttribute('onclick').match(/'([^']+)'/)[1];
                window.location.href = url;
            }
        });
    });
});
</script>
{% endblock %}
