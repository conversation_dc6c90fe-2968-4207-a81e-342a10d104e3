{# D:\school_fees_saas_v2\templates\fees\invoice_detail.html #}
{% extends "tenant_base.html" %}

{% load static humanize fees_tags %}

{% block title %}{{ view_title }} - {{ request.tenant.name }}{% endblock %}

{% block extra_tenant_css %}
<style>
    .invoice-box {
        max-width: 900px;
        margin: auto;
        padding: 30px;
        border: 1px solid #eee;
        box-shadow: 0 0 10px rgba(0, 0, 0, .15);
        font-size: 16px;
        line-height: 24px;
        font-family: 'Helvetica Neue', 'Helvetica', Helvetica, Arial, sans-serif;
        color: #555;
    }
    .invoice-box table { width: 100%; line-height: inherit; text-align: left; border-collapse: collapse; }
    .invoice-box table td { padding: 5px; vertical-align: top; }
    .invoice-box table tr td:nth-child(n+2) { text-align: right; } /* Align numbers to right */
    .invoice-box table tr.top table td { padding-bottom: 20px; }
    .invoice-box table tr.top table td.title { font-size: 45px; line-height: 45px; color: #333; }
    .invoice-box table tr.information table td { padding-bottom: 20px; }
    .invoice-box table tr.heading td { background: #eee; border-bottom: 1px solid #ddd; font-weight: bold; }
    .invoice-box table tr.details td { padding-bottom: 10px; }
    .invoice-box table tr.item td { border-bottom: 1px solid #eee; }
    .invoice-box table tr.item.last td { border-bottom: none; }
    .invoice-box table tr.total td:nth-child(2) { border-top: 2px solid #eee; font-weight: bold; }
    .invoice-actions { margin-top: 30px; padding-top: 20px; border-top: 1px dashed #ccc; }
    @media print {
        body, .invoice-box { margin: 0; box-shadow: none; border: none; }
        .no-print { display: none !important; }
        .container-fluid, .navbar, footer { display: none !important; } /* Hide non-invoice elements */
        .invoice-box { max-width: 100%; }
    }
    .status-tag { padding: .3em .8em; border-radius: .25rem; font-weight: 600; text-transform: uppercase; font-size: .9em; }
</style>
{% endblock %}


{% block content %}
<div class="container-fluid mt-4">
    {% include "partials/_breadcrumb.html" %}

    <div class="d-flex justify-content-end mb-3 gap-2 no-print">
        {% if invoice.is_editable and perms.fees.change_invoice %}
        <a href="{% url 'fees:invoice_update' invoice.pk %}" class="btn btn-outline-primary"><i class="bi bi-pencil-square me-1"></i> Edit Draft</a>
        {% endif %}
        <button onclick="window.print();" class="btn btn-outline-secondary"><i class="bi bi-printer me-1"></i> Print</button>
        
        <a href="{% url 'fees:invoice_pdf' invoice.pk %}" class="btn btn-secondary btn-sm" target="_blank" title="View PDF">PDF</a>
        {# <a href="#" class="btn btn-outline-info"><i class="bi bi-file-earmark-pdf me-1"></i> Download PDF</a> #}
        {% if invoice.is_payable %}
            {# <a href="#" class="btn btn-success"><i class="bi bi-credit-card me-1"></i> Pay Now</a> #}
            {# <a href="{% url 'fees:record_payment_for_invoice' invoice.pk %}" class="btn btn-primary"><i class="bi bi-cash-coin me-1"></i> Record Payment</a> #}
        {% endif %}
    </div>

    {% include "partials/_messages.html" %}

    <div class="invoice-box bg-white">
        <table cellpadding="0" cellspacing="0">
            <tr class="top">
                <td colspan="4">
                    <table>
                        <tr>
                            <td class="title">
                                {% if invoice.school.profile.logo %}
                                    <img src="{{ invoice.school.profile.logo.url }}" style="max-width:150px; max-height:80px;" alt="{{ invoice.school.name }} Logo">
                                {% else %}
                                    <h4 class="mb-0">{{ invoice.school.name }}</h4>
                                {% endif %}
                            </td>
                            <td style="text-align: right;">
                                <h2>INVOICE</h2>
                                <strong>#{{ invoice.invoice_number }}</strong><br>
                                Status: {% invoice_status_badge invoice.status %}<br>
                                Issued: {{ invoice.issue_date|date:"F d, Y" }}<br>
                                Due: {{ invoice.due_date|date:"F d, Y" }}
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            
            <tr class="information">
                <td colspan="4">
                    <table>
                        <tr>
                            <td>
                                <strong>{{ invoice.school.name }}</strong><br>
                                {{ invoice.school.profile.address_line_1|default:"" }}<br>
                                {% if invoice.school.profile.address_line_2 %}{{ invoice.school.profile.address_line_2 }}<br>{% endif %}
                                {{ invoice.school.profile.city|default:"" }}{% if invoice.school.profile.state %}, {{ invoice.school.profile.state }}{% endif %} {{ invoice.school.profile.postal_code|default:"" }}<br>
                                {{ invoice.school.profile.phone_number|default:"" }}<br>
                                {{ invoice.school.profile.email|default:"" }}
                            </td>
                            
                            <td style="text-align: right;">
                                <strong>Bill To:</strong><br>
                                {{ invoice.student.full_name }} (ID: {{ invoice.student.admission_number }})<br>
                                Class: {{ invoice.student.school_class.name }}<br>
                                {% if invoice.student.user.email %}{{ invoice.student.user.email }}<br>{% endif %}
                                {# Parent details could be added here if available #}
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            
            <tr class="heading">
                <td>Description</td>
                <td style="text-align:center;">Qty</td>
                <td style="text-align:right;">Unit Price</td>
                <td style="text-align:right;">Line Total</td>
            </tr>
            
            {% for item in invoice.details.all %}
            <tr class="item {% if forloop.last %}last{% endif %}">
                <td>
                    {% if item.line_type == 'FEE_ITEM' and item.fee_head %}
                        <strong>{{ item.fee_head.name }}</strong>
                        {% if item.description != item.fee_head.name %}<br><small class="text-muted">{{ item.description }}</small>{% endif %}
                    {% elif item.line_type == 'CONCESSION_ITEM' and item.concession_type %}
                        <strong>{{ item.concession_type.name }} (Discount)</strong>
                        {% if item.description %}<br><small class="text-muted">{{ item.description }}</small>{% endif %}
                    {% else %}
                        <strong>{{ item.description }}</strong>
                    {% endif %}
                </td>
                <td style="text-align:center;">{{ item.quantity|floatformat:2 }}</td>
                <td style="text-align:right;">{{ item.unit_price|intcomma }}</td>
                <td style="text-align:right;">{{ item.amount|intcomma }}</td>
            </tr>
            {% endfor %}
            
            <tr class="total">
                <td colspan="3" style="text-align:right; font-weight:bold;">Subtotal:</td>
                <td style="text-align:right; font-weight:bold;">{{ invoice.subtotal_amount|intcomma }}</td>
            </tr>
            {% if invoice.total_concession_amount > 0 %}
            <tr class="total">
                <td colspan="3" style="text-align:right;">Total Discounts:</td>
                <td style="text-align:right;">-{{ invoice.total_concession_amount|intcomma }}</td>
            </tr>
            <tr class="total">
                <td colspan="3" style="text-align:right; font-weight:bold;">Net Amount:</td>
                <td style="text-align:right; font-weight:bold;">{{ invoice.total_amount|intcomma }}</td>
            </tr>
            {% endif %}
            <tr class="total">
                <td colspan="3" style="text-align:right;">Amount Paid:</td>
                <td style="text-align:right;">{{ invoice.amount_paid|intcomma }}</td>
            </tr>
            <tr class="total" style="font-size: 1.2em;">
                <td colspan="3" style="text-align:right; font-weight:bold;">Balance Due:</td>
                <td style="text-align:right; font-weight:bold;">{{ invoice.balance_due|intcomma }} {{ request.tenant.profile.currency_code|default:"USD" }}</td>
            </tr>
        </table>

        {% if invoice.notes %}
        <div style="margin-top: 30px; padding-top:15px; border-top: 1px solid #eee;">
            <strong>Notes:</strong><br>
            <p style="white-space: pre-wrap;">{{ invoice.notes }}</p>
        </div>
        {% endif %}

        {# Payment History Section (if payments are tracked) #}
        {% if payments_for_invoice %}
        <div style="margin-top: 30px; padding-top:15px; border-top: 1px solid #eee;">
            <h5>Payment History</h5>
            <table style="font-size:0.9em;">
                <tr class="heading">
                    <td>Date</td>
                    <td>Method</td>
                    <td>Transaction ID</td>
                    <td style="text-align:right;">Amount</td>
                </tr>
                {% for payment in payments_for_invoice %}
                <tr class="item">
                    <td>{{ payment.payment_date|date:"d M Y" }}</td>
                    <td>{{ payment.get_payment_method_display }}</td>
                    <td>{{ payment.transaction_id|default:"N/A" }}</td>
                    <td style="text-align:right;">{{ payment.amount|intcomma }}</td>
                </tr>
                {% endfor %}
            </table>
        </div>
        {% endif %}
    </div>

    <div class="invoice-actions text-center no-print">
        {# More actions like "Send Reminder" could go here #}
        <p class="text-muted small">Thank you for your prompt payment.</p>
    </div>

</div>
{% endblock content %}

{% block page_specific_js %}
{# Any JS specific to invoice viewing, e.g., triggering PDF generation via AJAX #}
{% endblock %}

