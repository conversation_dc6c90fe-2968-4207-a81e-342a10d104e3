{% extends "base.html" %}
{% load static i18n calendar_tags %}

{% block title %}{% trans "School Calendar" %} - {{ month_name }} {{ year }}{% endblock %}

{% block extra_css %}
<style>
    .calendar-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        overflow: hidden;
        margin: 1rem 0;
    }

    .calendar-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        position: relative;
        overflow: hidden;
    }

    .calendar-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
        animation: float 20s infinite linear;
    }

    @keyframes float {
        0% { transform: translateX(-50px) translateY(-50px); }
        100% { transform: translateX(50px) translateY(50px); }
    }

    .calendar-nav {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        position: relative;
        z-index: 2;
    }

    .calendar-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        position: relative;
        z-index: 2;
    }

    .calendar-subtitle {
        opacity: 0.9;
        font-size: 1.1rem;
        margin-top: 0.5rem;
        position: relative;
        z-index: 2;
    }

    .nav-btn {
        background: rgba(255,255,255,0.2);
        border: 2px solid rgba(255,255,255,0.3);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        display: inline-block;
        margin: 0 0.5rem;
    }

    .nav-btn:hover {
        background: rgba(255,255,255,0.3);
        border-color: rgba(255,255,255,0.5);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.25);
    }

    .calendar-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
    }

    .calendar-table th {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 1.5rem 0.5rem;
        text-align: center;
        font-weight: 700;
        font-size: 1rem;
        color: #495057;
        border: 2px solid #dee2e6;
        text-transform: uppercase;
        letter-spacing: 1px;
        position: relative;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .calendar-table th::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
        height: 3px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 2px;
    }

    .calendar-table td {
        height: 120px;
        width: 14.28%;
        vertical-align: top;
        padding: 12px;
        border: 2px solid #dee2e6;
        position: relative;
        background: white;
        transition: all 0.3s ease;
        box-shadow: 0 2px 6px rgba(0,0,0,0.08);
    }

    .calendar-table td:hover {
        background: #f8f9ff;
        box-shadow: inset 0 0 0 2px #667eea;
    }

    .calendar-date {
        position: absolute;
        top: 8px;
        left: 8px;
        font-weight: 700;
        font-size: 1.1rem;
        color: #495057;
        z-index: 2;
        min-width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
        transition: all 0.3s ease;
        background: #f8f9fa;
        border: 2px solid #dee2e6;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .calendar-date.today {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        transform: scale(1.1);
        font-weight: 800;
    }

    .calendar-date.other-month {
        color: #adb5bd;
        opacity: 0.5;
    }

    .calendar-date.has-events {
        background: rgba(102, 126, 234, 0.1);
        border: 2px solid rgba(102, 126, 234, 0.3);
    }

    .calendar-events {
        margin-top: 3.2rem;
        padding: 0.5rem;
        max-height: 85px;
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: #667eea #f1f3f4;
    }

    .calendar-events::-webkit-scrollbar {
        width: 4px;
    }

    .calendar-events::-webkit-scrollbar-track {
        background: #f1f3f4;
        border-radius: 2px;
    }

    .calendar-events::-webkit-scrollbar-thumb {
        background: #667eea;
        border-radius: 2px;
    }

    .event-item {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border-left: 4px solid #2196f3;
        padding: 0.4rem 0.6rem;
        margin-bottom: 0.4rem;
        border-radius: 6px;
        font-size: 0.8rem;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
        overflow: hidden;
    }

    .event-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.3) 0%, transparent 100%);
        pointer-events: none;
    }

    .event-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border-left-width: 6px;
    }

    .event-item.priority-urgent {
        border-left-color: #e91e63;
        background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
    }

    .event-item.priority-high {
        border-left-color: #f44336;
        background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    }

    .event-item.priority-medium {
        border-left-color: #ff9800;
        background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    }

    .event-item.priority-low {
        border-left-color: #4caf50;
        background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    }

    .event-time {
        font-size: 0.7rem;
        color: #666;
        font-weight: 500;
    }

    .event-title {
        font-weight: 600;
        color: #333;
        display: block;
        margin-top: 0.1rem;
    }
    
    .legend {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .legend-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .legend-color {
        width: 16px;
        height: 16px;
        border-radius: 3px;
    }
    
    .view-controls {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }



    @media (max-width: 768px) {
        .calendar-table td {
            height: 80px;
            padding: 0.25rem;
        }

        .event-item {
            font-size: 0.65rem;
            padding: 1px 4px;
        }

        .calendar-nav {
            flex-direction: column;
            gap: 1rem;
        }

        .priority-items {
            flex-direction: column;
            align-items: center;
        }

        .action-buttons {
            flex-direction: column;
            align-items: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="calendar-container">
        <!-- Calendar Header -->
        <div class="calendar-header">
            <div class="calendar-nav">
                <a href="?year={{ prev_year }}&month={{ prev_month }}" class="nav-btn">
                    <i class="bi bi-chevron-left me-1"></i>{% trans "Previous" %}
                </a>

                <div class="text-center">
                    <h1 class="calendar-title">{{ month_name }} {{ year }}</h1>
                    <p class="calendar-subtitle">{% trans "School Calendar" %}</p>
                </div>

                <a href="?year={{ next_year }}&month={{ next_month }}" class="nav-btn">
                    {% trans "Next" %}<i class="bi bi-chevron-right ms-1"></i>
                </a>
            </div>

            <div class="view-controls">
                {% if can_manage %}
                    <a href="{% url 'school_calendar:admin_event_create' %}" class="nav-btn">
                        <i class="bi bi-plus-circle me-1"></i>{% trans "Add Event" %}
                    </a>
                    <a href="{% url 'school_calendar:admin_event_list' %}" class="nav-btn">
                        <i class="bi bi-gear me-1"></i>{% trans "Manage" %}
                    </a>
                {% endif %}

                <a href="{% url 'school_calendar:event_list' %}" class="nav-btn">
                        <i class="bi bi-list me-1"></i>{% trans "List View" %}
                    </a>
                </div>
            </div>

        </div>

        <!-- Calendar Grid -->
        <div class="p-0">
            <table class="calendar-table">
                <thead>
                    <tr>
                        <th>Sun</th>
                        <th>Mon</th>
                        <th>Tue</th>
                        <th>Wed</th>
                        <th>Thu</th>
                        <th>Fri</th>
                        <th>Sat</th>
                    </tr>
                </thead>
                <tbody>
                    {% for week in calendar %}
                    <tr>
                        {% for day in week %}
                        {% if day != 0 %}
                            {% with year|stringformat:"s"|add:"-"|add:month|stringformat:"02d"|add:"-"|add:day|stringformat:"02d" as current_date_str %}
                            {% with current_date_str|date:"Y-m-d" as date_key %}
                            {% with current_date_str|date:"w" as day_of_week %}
                            <td class="{% if day_of_week == '0' or day_of_week == '6' %}weekend{% endif %}">
                                <div class="calendar-date {% if current_date_str == today|date:'Y-m-d' %}today{% endif %}{% if events_by_date|lookup:date_key %} has-events{% endif %}">
                                    {{ day }}
                                </div>

                                <!-- Events for this day -->
                                <div class="calendar-events">
                                    {% for event in events_by_date|lookup:date_key %}
                                    <div class="event-item priority-{{ event.priority|lower }}"
                                        onclick="window.location.href='{% url 'school_calendar:event_detail' event.pk %}'"
                                        title="{{ event.title }} - {{ event.description|truncatewords:10 }}">
                                        {% if not event.is_all_day %}
                                        <div class="event-time">{{ event.start_time|time:"g:i A" }}</div>
                                        {% endif %}
                                        <div class="event-title">{{ event.title|truncatewords:3 }}</div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </td>
                            {% endwith %}
                            {% endwith %}
                            {% endwith %}
                        {% else %}
                            <td class="other-month"></td>
                        {% endif %}
                        {% endfor %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Calendar Legend -->
        <div class="calendar-legend">
            <h6 class="mb-3">
                <i class="bi bi-info-circle me-2"></i>
                {% trans "Legend" %}
            </h6>
            <div class="row">
                <div class="col-md-6">
                    <div class="legend-item">
                        <div class="legend-color" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);"></div>
                        <span>{% trans "Today" %}</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #e8f5e8;"></div>
                        <span>{% trans "Low Priority" %}</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #fff3e0;"></div>
                        <span>{% trans "Medium Priority" %}</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="legend-item">
                        <div class="legend-color" style="background: #ffebee;"></div>
                        <span>{% trans "High Priority" %}</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #fce4ec;"></div>
                        <span>{% trans "Urgent Priority" %}</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #f5f5f5;"></div>
                        <span>{% trans "Weekend" %}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h6 class="mb-3">
                <i class="bi bi-lightning-charge me-2"></i>
                {% trans "Quick Actions" %}
            </h6>
            <div class="d-flex flex-wrap">
                {% if can_manage %}
                <a href="{% url 'school_calendar:admin_event_create' %}" class="btn btn-primary action-btn">
                    <i class="bi bi-plus-circle me-1"></i>{% trans "Create Event" %}
                </a>
                <a href="{% url 'school_calendar:admin_event_list' %}" class="btn btn-outline-primary action-btn">
                    <i class="bi bi-gear me-1"></i>{% trans "Manage Events" %}
                </a>
                {% endif %}
                <a href="{% url 'school_calendar:event_list' %}" class="btn btn-outline-secondary action-btn">
                    <i class="bi bi-list me-1"></i>{% trans "List View" %}
                </a>
                <a href="?" class="btn btn-outline-info action-btn">
                    <i class="bi bi-arrow-clockwise me-1"></i>{% trans "Current Month" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers for event items
    const eventItems = document.querySelectorAll('.event-item');
    eventItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.stopPropagation();
            // The onclick is already handled in the template
        });
    });

    // Add hover effects for calendar dates
    const calendarDates = document.querySelectorAll('.calendar-date');
    calendarDates.forEach(date => {
        date.addEventListener('mouseenter', function() {
            if (!this.classList.contains('today')) {
                this.style.background = 'rgba(102, 126, 234, 0.1)';
                this.style.borderRadius = '8px';
            }
        });

        date.addEventListener('mouseleave', function() {
            if (!this.classList.contains('today')) {
                this.style.background = '';
                this.style.borderRadius = '';
            }
        });
    });

    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowLeft') {
            const prevBtn = document.querySelector('a[href*="prev"]');
            if (prevBtn) window.location.href = prevBtn.href;
        } else if (e.key === 'ArrowRight') {
            const nextBtn = document.querySelector('a[href*="next"]');
            if (nextBtn) window.location.href = nextBtn.href;
        }
    });
});
</script>
{% endblock %}


