{% extends "base.html" %}
{% load static i18n %}

{% block title %}{% trans "School Calendar" %} - {{ month_name }} {{ year }}{% endblock %}

{% block extra_css %}
<style>
    .calendar-container {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .calendar-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 10px 10px 0 0;
    }
    
    .calendar-nav {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .calendar-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .calendar-table th {
        background-color: #f8f9fa;
        padding: 1rem;
        text-align: center;
        font-weight: 600;
        border-bottom: 2px solid #dee2e6;
    }
    
    .calendar-table td {
        height: 120px;
        width: 14.28%;
        vertical-align: top;
        padding: 0.5rem;
        border: 1px solid #e9ecef;
        position: relative;
    }
    
    .calendar-date {
        font-weight: 600;
        margin-bottom: 0.25rem;
    }
    
    .calendar-date.today {
        background: #007bff;
        color: white;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .calendar-date.other-month {
        color: #6c757d;
    }
    
    .event-item {
        background: #007bff;
        color: white;
        padding: 2px 6px;
        margin: 1px 0;
        border-radius: 3px;
        font-size: 0.75rem;
        cursor: pointer;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    .event-item:hover {
        opacity: 0.8;
    }
    
    .legend {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .legend-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .legend-color {
        width: 16px;
        height: 16px;
        border-radius: 3px;
    }
    
    .view-controls {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }
    
    @media (max-width: 768px) {
        .calendar-table td {
            height: 80px;
            padding: 0.25rem;
        }
        
        .event-item {
            font-size: 0.65rem;
            padding: 1px 4px;
        }
        
        .calendar-nav {
            flex-direction: column;
            gap: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="calendar-container">
        <!-- Calendar Header -->
        <div class="calendar-header">
            <div class="calendar-nav">
                <div>
                    <h2 class="mb-0">{{ month_name }} {{ year }}</h2>
                    <p class="mb-0 opacity-75">{% trans "School Calendar" %}</p>
                </div>
                
                <div class="view-controls">
                    {% if can_manage %}
                        <a href="{% url 'school_calendar:admin_event_create' %}" class="btn btn-light btn-sm">
                            <i class="bi bi-plus-circle me-1"></i>{% trans "Add Event" %}
                        </a>
                        <a href="{% url 'school_calendar:admin_event_list' %}" class="btn btn-outline-light btn-sm">
                            <i class="bi bi-gear me-1"></i>{% trans "Manage" %}
                        </a>
                    {% endif %}
                    
                    <a href="{% url 'school_calendar:event_list' %}" class="btn btn-outline-light btn-sm">
                        <i class="bi bi-list me-1"></i>{% trans "List View" %}
                    </a>
                </div>
            </div>
            
            <!-- Navigation -->
            <div class="d-flex justify-content-between align-items-center">
                <a href="?year={{ prev_year }}&month={{ prev_month }}" class="btn btn-outline-light btn-sm">
                    <i class="bi bi-chevron-left"></i> {% trans "Previous" %}
                </a>
                
                <a href="?" class="btn btn-light btn-sm">
                    <i class="bi bi-house me-1"></i>{% trans "Today" %}
                </a>
                
                <a href="?year={{ next_year }}&month={{ next_month }}" class="btn btn-outline-light btn-sm">
                    {% trans "Next" %} <i class="bi bi-chevron-right"></i>
                </a>
            </div>
        </div>
        
        <!-- Calendar Grid -->
        <div class="p-3">
            <table class="calendar-table">
                <thead>
                    <tr>
                        <th>{% trans "Sunday" %}</th>
                        <th>{% trans "Monday" %}</th>
                        <th>{% trans "Tuesday" %}</th>
                        <th>{% trans "Wednesday" %}</th>
                        <th>{% trans "Thursday" %}</th>
                        <th>{% trans "Friday" %}</th>
                        <th>{% trans "Saturday" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for week in calendar %}
                    <tr>
                        {% for day in week %}
                        <td>
                            {% if day != 0 %}
                                {% with day|date:"Y-m-d" as day_str %}
                                {% with year|stringformat:"s"|add:"-"|add:month|stringformat:"02d"|add:"-"|add:day|stringformat:"02d" as current_date %}
                                {% with current_date|date:"Y-m-d" as date_key %}
                                
                                <div class="calendar-date {% if current_date == today|date:'Y-m-d' %}today{% endif %}">
                                    {{ day }}
                                </div>
                                
                                <!-- Events for this day -->
                                {% with current_date|date:"Y-m-d" as date_str %}
                                {% for event in events %}
                                    {% if event.start_date|date:"Y-m-d" <= date_str and event.end_date|date:"Y-m-d" >= date_str %}
                                    <div class="event-item"
                                         style="background-color: {{ event.category.color|default:'#007bff' }}"
                                         title="{{ event.title }} - {{ event.start_time|default:'' }}"
                                         onclick="window.location.href='{% url 'school_calendar:event_detail' event.pk %}'">
                                        {{ event.title|truncatechars:15 }}
                                    </div>
                                    {% endif %}
                                {% endfor %}
                                {% endwith %}
                                
                                {% endwith %}
                                {% endwith %}
                                {% endwith %}
                            {% endif %}
                        </td>
                        {% endfor %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Legend -->
        {% if categories %}
        <div class="p-3 border-top">
            <h6 class="mb-2">{% trans "Event Categories" %}</h6>
            <div class="legend">
                {% for category in categories %}
                <div class="legend-item">
                    <div class="legend-color" style="background-color: {{ category.color }}"></div>
                    <small>{{ category.name }}</small>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
    
    <!-- Upcoming Events Sidebar -->
    <div class="row mt-4">
        <div class="col-lg-8">
            <!-- Quick Stats -->
            <div class="row">
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="bi bi-calendar-event display-4 text-primary mb-2"></i>
                            <h5>{{ events|length }}</h5>
                            <small class="text-muted">{% trans "Events This Month" %}</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="bi bi-clock display-4 text-success mb-2"></i>
                            <h5>{% now "j" %}</h5>
                            <small class="text-muted">{% trans "Today" %}</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="bi bi-people display-4 text-info mb-2"></i>
                            <h5>{{ categories|length }}</h5>
                            <small class="text-muted">{% trans "Categories" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="bi bi-calendar-plus me-2"></i>
                        {% trans "Upcoming Events" %}
                    </h6>
                </div>
                <div class="card-body">
                    {% for event in events|slice:":5" %}
                        {% if event.is_upcoming %}
                        <div class="d-flex align-items-start mb-3">
                            <div class="flex-shrink-0">
                                <div class="badge" style="background-color: {{ event.category.color|default:'#007bff' }}">
                                    {{ event.start_date.day }}
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">
                                    <a href="{% url 'school_calendar:event_detail' event.pk %}" class="text-decoration-none">
                                        {{ event.title }}
                                    </a>
                                </h6>
                                <small class="text-muted">
                                    <i class="bi bi-calendar me-1"></i>{{ event.start_date|date:"M j" }}
                                    {% if event.start_time %}
                                        <i class="bi bi-clock ms-2 me-1"></i>{{ event.start_time|time:"g:i A" }}
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                        {% endif %}
                    {% empty %}
                    <p class="text-muted text-center py-3">
                        <i class="bi bi-calendar-x display-4 mb-2"></i><br>
                        {% trans "No upcoming events" %}
                    </p>
                    {% endfor %}
                    
                    <div class="text-center mt-3">
                        <a href="{% url 'school_calendar:event_list' %}?date_filter=upcoming" class="btn btn-outline-primary btn-sm">
                            {% trans "View All Upcoming" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects to calendar cells
    const calendarCells = document.querySelectorAll('.calendar-table td');
    calendarCells.forEach(cell => {
        cell.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        cell.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
    
    // Add click handlers for event items
    const eventItems = document.querySelectorAll('.event-item');
    eventItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.stopPropagation();
            // Click handler is already in the onclick attribute
        });
    });
});
</script>
{% endblock %}
