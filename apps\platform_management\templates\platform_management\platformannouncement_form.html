{# Example: D:\school_fees_saas_v2\apps\platform_management\templates\platform_management\platformsetting_form.html #}
{% extends "platform_management/generic_form.html" %}
{% load i18n %}

{# view_title will be passed from the view #}
{# form will be passed from the view #}
{# form_mode will be passed from the view ('create' or 'update') #}
{# cancel_url can be passed from the view or defaults to history.back() #}



















{% comment %} {# D:\school_fees_saas_v2\templates\platform_management\generic_form.html #}
{% extends "platform_management/platform_admin_base.html" %}
{% load static i18n widget_tweaks %}

{% block platform_admin_page_title %}{{ view_title|default:_("Manage Item") }}{% endblock %}

{% block platform_admin_page_content %}
<div class="pagetitle">
    <h1>{{ view_title }}</h1>
    {# Add breadcrumbs dynamically if needed based on context #}
</div>

<section class="section">
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h5 class="card-title">
                        {% if form_mode == "create" %}
                            {% trans "Create New" %}
                        {% else %}
                            {% trans "Update Item" %}
                        {% endif %}
                    </h5>

                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}

                        {% for hidden_field in form.hidden_fields %}
                            {{ hidden_field }}
                        {% endfor %}

                        {% for field in form.visible_fields %}
                            <div class="mb-3 row">
                                <label for="{{ field.id_for_label }}" class="col-sm-3 col-form-label fw-semibold">
                                    {{ field.label }}{% if field.field.required %}<span class="text-danger">*</span>{% endif %}
                                </label>
                                <div class="col-sm-9">
                                    {% if field|widget_type == 'checkboxinput' %}
                                        <div class="form-check mt-2">
                                            {% render_field field class+="form-check-input" %}
                                            {% if field.help_text %}
                                                <small class="form-text text-muted ms-2">{{ field.help_text|safe }}</small>
                                            {% endif %}
                                        </div>
                                    {% elif field|widget_type == 'checkboxselectmultiple' or field|widget_type == 'radioselect' %}
                                        <div class="checkbox-select-multiple-custom"> {# Custom class for styling if needed #}
                                            {% for choice in field %}
                                                <div class="form-check form-check-inline">
                                                    {{ choice.tag }}
                                                    <label for="{{ choice.id_for_label }}" class="form-check-label">{{ choice.choice_label }}</label>
                                                </div>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        {% render_field field class+="form-control" %}
                                    {% endif %}
                                    
                                    {% if field.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ field.errors|striptags }}
                                        </div>
                                    {% elif field.help_text and field|widget_type != 'checkboxinput' %}
                                        <small class="form-text text-muted">{{ field.help_text|safe }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        {% endfor %}

                        <div class="row mt-4">
                            <div class="col-sm-9 offset-sm-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-save-fill me-1"></i> 
                                    {% if form_mode == "create" %}{% trans "Save" %}{% else %}{% trans "Update" %}{% endif %}
                                </button>
                                <a href="{{ cancel_url|default:request.META.HTTP_REFERER|default_if_none:'javascript:history.back();' }}" class="btn btn-secondary ms-2">{% trans "Cancel" %}</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock platform_admin_page_content %}

{% block extra_platform_admin_js %}
    {{ block.super }}
    {# Add Select2 JS if used by any forms extending this generic template #}
    {# Example:
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            $('.select2-multiple').each(function() {
                $(this).select2({
                    theme: "bootstrap-5",
                    width: '100%', //$(this).data('width') ? $(this).data('width') : $(this).hasClass('w-100') ? '100%' : 'style',
                    placeholder: $(this).data('placeholder') || "{% trans 'Select options' %}",
                    // closeOnSelect: false, // For multiple selections
                    allowClear: Boolean($(this).data('allow-clear')) 
                });
            });
        });
    </script> #}
{% endblock %}
 {% endcomment %}
