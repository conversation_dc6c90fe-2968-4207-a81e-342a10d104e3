{# templates/fees/invoice_pdf_template.html #}
{% extends "reporting/pdf/_pdf_base.html" %} {# Extend your PDF base #}
{% load humanize %}

{% block pdf_title %}Invoice #{{ invoice.invoice_number_display }} - {{ school_profile.name|default:request.tenant.name }}{% endblock %}

{% block document_name_header %}INVOICE{% endblock %}

{% block document_specific_header_info %}
    <p class="mb-0">Invoice No: <strong>{{ invoice.invoice_number_display }}</strong></p>
    <p class="mb-0">Issue Date: {{ invoice.issue_date|date:"d M Y" }}</p>
    <p class="mb-0">Due Date: <strong>{{ invoice.due_date|date:"d M Y" }}</strong></p>
{% endblock %}

{% block pdf_main_content %}
    {# Bill To Section #}
    <table class="no-border" style="margin-bottom: 20px;">
        <tr>
            <td style="width: 50%;" class="no-border">
                <h4 class="fw-bold">Bill To:</h4>
                {% if invoice.student %}
                    <p class="mb-0"><strong>{{ invoice.student.full_name }}</strong></p>
                    <p class="mb-0">Admission No: {{ invoice.student.admission_number|default:"N/A" }}</p>
                    <p class="mb-0">Class: {{ invoice.student.current_class.name|default:"N/A" }}
                        {% if invoice.student.current_section %}- {{ invoice.student.current_section.name }}{% endif %}
                    </p>
                    {% with parent=invoice.student.parents.all.first %}
                        {% if parent %}
                            <p class="mb-0">Parent: {{ parent.full_name }}</p>
                            {% if parent.phone_number %}<p class="mb-0">Phone: {{ parent.phone_number }}</p>{% endif %}
                        {% endif %}
                    {% endwith %}
                {% else %}
                    <p>N/A</p>
                {% endif %}
            </td>
            <td style="width: 50%; text-align: right;" class="no-border">
                {# Optionally, payment instructions or bank details here #}
            </td>
        </tr>
    </table>

    {# Invoice Items #}
    <h4 class="fw-bold">Invoice Details:</h4>
    <p class="mb-1">Period: {{ invoice.period_description|default:"N/A" }}</p>
    {% if invoice.fee_structure %}
        <p class="mb-1">Fee Structure: {{ invoice.fee_structure.name }}</p>
    {% endif %}
    {% if invoice.term %}
        <p class="mb-1">Term: {{ invoice.term.name }} ({{ invoice.academic_year.name }})</p>
    {% elif invoice.academic_year %}
        <p class="mb-1">Academic Year: {{ invoice.academic_year.name }}</p>
    {% endif %}


    <table style="margin-top: 10px;">
        <thead style="background-color: #f2f2f2;">
            <tr>
                <th style="width: 60%;">Description</th>
                <th class="text-end" style="width: 20%;">Quantity</th>
                <th class="text-end" style="width: 20%;">Amount</th>
            </tr>
        </thead>
        <tbody>
            {# Billing Items #}
            {% for item in invoice.details.all %} {# Optimized in view with prefetch #}
                {% if item.line_type == 'FEE_ITEM' %}
                <tr>
                    <td>
                        {% if item.fee_head %}
                            {{ item.fee_head.name }}
                            {% if item.description != item.fee_head.name %}<br><small>{{ item.description }}</small>{% endif %}
                        {% else %}
                            {{ item.description }}
                        {% endif %}
                    </td>
                    <td class="text-end">{{ item.quantity|floatformat:0|intcomma|default:"1" }}</td>
                    <td class="text-end">{{ item.amount|floatformat:2|intcomma }}</td>
                </tr>
                {% endif %}
            {% endfor %}

            {# Subtotal before discounts #}
            <tr class="fw-bold">
                <td colspan="2" class="text-end no-border">Subtotal:</td>
                <td class="text-end no-border">{{ invoice.total_amount|floatformat:2|intcomma }}</td>
            </tr>

            {# Concession/Discount Items #}
            {% for item in invoice.details.all %}
                {% if item.is_concession %}
                <tr>
                    <td>{{ item.description|default:item.concession_type_applied.name }}</td>
                    <td class="text-end"></td> {# Concessions don't usually have quantity in this context #}
                    <td class="text-end">({{ item.amount|abs|floatformat:2|intcomma }})</td> {# Show as negative/deduction #}
                </tr>
                {% endif %}
            {% endfor %}

            {% if invoice.discount_applied > 0 %}
            <tr class="fw-bold">
                <td colspan="2" class="text-end no-border">Total Discount Applied:</td>
                <td class="text-end no-border">({{ invoice.discount_applied|floatformat:2|intcomma }})</td>
            </tr>
            {% endif %}

            {# Net Amount After Discount (if different from Amount Due initially) #}
            {% if invoice.discount_applied > 0 %}
            <tr class="fw-bold" style="border-top: 1px solid #333;">
                <td colspan="2" class="text-end no-border">Net Amount Payable:</td>
                <td class="text-end no-border">{{ invoice.net_amount_payable|floatformat:2|intcomma }}</td>
            </tr>
            {% endif %}

            {# Amount Paid #}
            {% if invoice.amount_paid > 0 %}
            <tr class="fw-bold">
                <td colspan="2" class="text-end no-border">Amount Paid:</td>
                <td class="text-end no-border text-success">{{ invoice.amount_paid|floatformat:2|intcomma }}</td>
            </tr>
            {% endif %}

            {# Amount Due #}
            <tr class="fw-bold" style="background-color: #ddeeff; font-size: 1.1em; border-top: 2px solid #333;">
                <td colspan="2" class="text-end">AMOUNT DUE:</td>
                <td class="text-end">{{ school_profile.currency_symbol|default:'$' }}{{ invoice.amount_due|floatformat:2|intcomma }}</td>
            </tr>
        </tbody>
    </table>

    {% if invoice.notes %}
        <div style="margin-top: 20px; border-top: 1px dashed #ccc; padding-top: 10px;">
            <h4 class="fw-bold">Notes:</h4>
            <p>{{ invoice.notes|linebreaksbr }}</p>
        </div>
    {% endif %}

    {# Payment Instructions from SchoolProfile later #}
    {% if school_profile.payment_instructions %}
        <div style="margin-top: 20px; font-size: 8pt;">
            <p class="fw-bold">Payment Instructions:</p>
            <p>{{ school_profile.payment_instructions|linebreaksbr }}</p>
        </div>
    {% endif %}
{% endblock %}

