{# D:\school_fees_saas_v2\apps\parent_portal\templates\parent_portal\mock_payment_gateway.html #}
{% extends "parent_portal/parent_portal_base.html" %}
{% load static i18n humanize %}

{% block parent_portal_page_title %}{{ view_title|default:_("Secure Payment Simulation") }}{% endblock %}

{% block extra_parent_portal_css %}
    {{ block.super }}
    <style>
        .mock-gateway-card { max-width: 600px; margin: 2rem auto; }
        .invoice-summary-item { font-size: 0.9rem; }
    </style>
{% endblock %}

{% block parent_portal_main_content %}
<div class="container mt-4">
    <div class="card shadow-lg mock-gateway-card">
        <div class="card-header bg-info text-white text-center">
            <h4 class="mb-0"><i class="bi bi-shield-lock-fill me-2"></i>{{ view_title }}</h4>
        </div>
        <div class="card-body p-4 p-md-5">
            {% if payment_error %}
                <div class="alert alert-danger" role="alert">
                    {% trans "There was an issue with your payment session. Please return to select invoices again." %}
                </div>
                <a href="{% url 'parent_portal:select_invoices_for_payment' %}" class="btn btn-primary">{% trans "Select Invoices" %}</a>
            {% elif invoice_details %}
                <p class="text-center text-muted">{% trans "You are about to make a simulated payment for the following:" %}</p>
                
                <div class="mb-3">
                    <h6>{% trans "Selected Invoices:" %}</h6>
                    <ul class="list-group list-group-flush mb-3">
                        {% for inv in invoice_details %}
                        <li class="list-group-item d-flex justify-content-between align-items-center invoice-summary-item px-0">
                            <span>{{ inv.number }}</span>
                            <span>{{ currency }}{{ inv.amount_due|floatformat:2|intcomma }}</span>
                        </li>
                        {% endfor %}
                    </ul>
                    <div class="d-flex justify-content-between fw-bold fs-5 border-top pt-2">
                        <span>{% trans "Total Amount:" %}</span>
                        <span>{{ currency }}{{ total_amount_to_pay|floatformat:2|intcomma }}</span>
                    </div>
                </div>
                <hr>
                <h5 class="text-center mb-3">{% trans "Simulated Card Details" %}</h5>
                <p class="text-center small text-muted">
                    <i class="bi bi-info-circle-fill me-1"></i>{% trans "This is a mock payment form. No real card details are processed or stored." %}
                </p>
                <form method="post" action="{% url 'parent_portal:process_mock_payment' %}">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="mockCardNumber" class="form-label">{% trans "Card Number" %}</label>
                        <input type="text" class="form-control" id="mockCardNumber" value="4242 4242 4242 4242" readonly>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="mockExpiry" class="form-label">{% trans "Expiry (MM/YY)" %}</label>
                            <input type="text" class="form-control" id="mockExpiry" value="12/28" readonly>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="mockCVC" class="form-label">{% trans "CVC" %}</label>
                            <input type="text" class="form-control" id="mockCVC" value="123" readonly>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="mockCardName" class="form-label">{% trans "Name on Card" %}</label>
                        <input type="text" class="form-control" id="mockCardName" value="{{ parent.get_full_name|default:parent.email }}" readonly>
                    </div>

                    <p class="text-center mt-4">{% trans "For simulation purposes, choose the outcome:" %}</p>
                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                        <button type="submit" name="payment_outcome" value="success" class="btn btn-success btn-lg">
                            <i class="bi bi-check-circle-fill me-2"></i>{% trans "Simulate Successful Payment" %}
                        </button>
                        <button type="submit" name="payment_outcome" value="failure" class="btn btn-danger btn-lg">
                            <i class="bi bi-x-circle-fill me-2"></i>{% trans "Simulate Failed Payment" %}
                        </button>
                    </div>
                </form>
            {% else %}
                <div class="alert alert-warning" role="alert">
                    {% trans "No payment details found in your session. Please select invoices to pay first." %}
                </div>
                <a href="{% url 'parent_portal:select_invoices_for_payment' %}" class="btn btn-primary">{% trans "Select Invoices" %}</a>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}


