# apps/fees/templatetags/fees_tags.py
from django import template
from django.utils.translation import gettext_lazy as _
from apps.fees.models import Invoice, InvoiceStatus # Import Invoice and InvoiceStatus

register = template.Library()

@register.inclusion_tag('fees/partials/_status_badge.html') # Path to your snippet
def invoice_status_badge(invoice_status_value): # invoice_status_value is 'DRAFT', 'SENT', etc.
    STATUS_DISPLAY_MAP = {
        InvoiceStatus.DRAFT:           {'class': 'secondary',    'text': _('Draft')},
        InvoiceStatus.SENT:            {'class': 'info',         'text': _('Sent')},
        InvoiceStatus.PARTIALLY_PAID:  {'class': 'primary',      'text': _('Partially Paid')},
        InvoiceStatus.PAID:            {'class': 'success',      'text': _('Paid')},
        InvoiceStatus.OVERDUE:         {'class': 'warning',      'text': _('Overdue')},
        InvoiceStatus.CANCELLED:       {'class': 'danger',       'text': _('Cancelled')},
        InvoiceStatus.VOID:            {'class': 'dark',         'text': _('Void')},
    }
    
    display_info = STATUS_DISPLAY_MAP.get(str(invoice_status_value))

    if display_info:
        return {"badge_class": display_info['class'], "badge_text": display_info['text']}
    else:
        return {"badge_class": "light text-dark", "badge_text": str(invoice_status_value).title()}
    
    
    




# @register.simple_tag # Or @register.inclusion_tag if it renders a snippet
# def invoice_status_badge(invoice_status_value): # invoice_status_value is the actual DB status string e.g. 'PARTIAL'
#     # Define default badge appearance
#     default_badge = ("secondary", str(invoice_status_value).title()) # Default if status not in map

#     # Map status DB values to badge class and display text
#     # The keys here should be the ACTUAL DATABASE VALUES for the status
#     status_map = {
#         Invoice.STATUS_DRAFT: ("secondary", _("Draft")),
#         Invoice.STATUS_PENDING: ("warning text-dark", _("Pending")),
#         Invoice.STATUS_SENT: ("primary", _("Sent")),
#         Invoice.STATUS_PAID: ("success", _("Paid")),
#         Invoice.STATUS_PARTIALLY_PAID: ("info text-dark", _("Partially Paid")), # <<< CORRECTED CONSTANT
#         Invoice.STATUS_OVERDUE: ("danger", _("Overdue")),
#         Invoice.STATUS_CANCELLED: ("dark", _("Cancelled")),
#         Invoice.STATUS_VOID: ("light text-dark", _("Void")),
#     }
    
#     badge_class, display_text = status_map.get(str(invoice_status_value).upper(), default_badge) # Use .upper() if DB values are uppercase

#     # Ensure you return HTML or use an inclusion tag to render a snippet
#     # For a simple_tag returning HTML, you'd use format_html
#     from django.utils.html import format_html
#     return format_html('<span class="badge bg-{}">{}</span>', badge_class, display_text)








# # D:\school_fees_saas_v2\apps\fees\templatetags\fees_tags.py
# from django import template
# from django.utils.html import format_html
# # Import Invoice model correctly (assuming it's in fees/models.py)
# from ..models import Invoice # Relative import from parent models.py
# from django.utils.translation import gettext_lazy as _

# register = template.Library()

# @register.simple_tag
# def invoice_status_badge(status_code):
#     status_map = {
#         Invoice.STATUS_DRAFT: ("secondary", "Draft"),
#         Invoice.STATUS_PENDING: ("warning text-dark", "Pending"), # Added text-dark for better contrast on warning
#         Invoice.STATUS_PAID: ("success", "Paid"),
#         Invoice.STATUS_PARTIALLY_PAID: ("info text-dark", _("Partially Paid")), # Added text-dark
#         Invoice.STATUS_OVERDUE: ("danger", "Overdue"),
#         Invoice.STATUS_CANCELLED: ("dark", "Cancelled"),
#         Invoice.STATUS_VOID: ("light text-dark border", "Void"), # Added border for light
#     }
#     # Default if status_code not in map
#     default_color = "light text-dark border"
#     default_text = status_code.replace('_', ' ').title() if status_code else "Unknown"
    
#     color, text = status_map.get(status_code, (default_color, default_text))
    
#     return format_html('<span class="badge bg-{0} status-badge">{1}</span>', color, text)

# # Add other custom tags for the 'fees' app here if needed

    