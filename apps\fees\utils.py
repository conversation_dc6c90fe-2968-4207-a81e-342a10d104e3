# apps/fees/utils.py

from io import BytesIO
from django.template.loader import render_to_string
from django.conf import settings
from django.http import HttpResponse
# Import the sequence model
from apps.tenants.models import School # Import the tenant model type
from django.db import connection # To potentially get current schema if tenant obj not passed
from django_tenants.utils import get_tenant_model # To get tenant model class
from django.utils import timezone

# --- Import xhtml2pdf ---
from xhtml2pdf import pisa # Import pisa directly
XHTML2PDF_AVAILABLE = True # Assume available if import succeeds

try:
    from apps.schools.models import SchoolProfile
except ImportError:
    SchoolProfile = None # Handle gracefully if schools app not ready?

# --- Define function to handle resource linking (CSS, Images) ---
# This is often needed for xhtml2pdf to find static/media files
def link_callback(uri, rel):
    """
    Convert HTML URIs to absolute system paths so xhtml2pdf can access files.
    Handles MEDIA_URL and STATIC_URL.
    """
    # Use slicing for STATIC_URL/MEDIA_URL comparison
    if uri.startswith(settings.MEDIA_URL):
        path = os.path.join(settings.MEDIA_ROOT, uri.replace(settings.MEDIA_URL, ""))
    elif uri.startswith(settings.STATIC_URL):
        path = os.path.join(settings.STATIC_ROOT, uri.replace(settings.STATIC_URL, ""))
        # Or search in STATICFILES_DIRS if using that for development
        # path = settings.STATICFILES_DIRS[0] + uri.replace(settings.STATIC_URL, "") # Simplified example
    else:
        # Use uri as is? Or maybe handle other cases?
        # For now, assume it's an external URL or already absolute
        return uri

    # Ensure path exists and is readable, handle potential errors
    # import os # Need os import at top
    # if not os.path.isfile(path):
    #     print(f"Warning: link_callback could not find file at path: {path}")
    #     return uri # Return original uri if path not found

    return path

# Need os import for link_callback if using os.path.join
import os



def generate_invoice_number(tenant_school: School) -> str:
    """
    Gets the next sequential invoice number for the given tenant.
    Uses the InvoiceSequence model for tracking.
    """
    
    # --- Import models needed ONLY by this function HERE ---
    from apps.schools.models import InvoiceSequence
    # --- End function-specific import ---
    
    if not isinstance(tenant_school, get_tenant_model()):
        raise ValueError("A valid tenant school object is required.")

    try:
        # Get or create the sequence record for this tenant, locking it for update
        # Note: Requires tenant_context if called outside tenant request cycle
        sequence, created = InvoiceSequence.objects.select_for_update().get_or_create(
            tenant=tenant_school, # Use the passed tenant object
            defaults={'prefix': 'INV-', 'last_number': 0, 'padding_digits': 5} # Sensible defaults
        )

        # Increment the last number
        sequence.last_number += 1
        sequence.save(update_fields=['last_number', 'last_updated'])

        # Format the number
        next_num_str = f"{sequence.prefix}{sequence.last_number:0{sequence.padding_digits}d}"
        print(f"--- Util: Generated Invoice Number {next_num_str} for tenant {tenant_school.schema_name} ---")
        return next_num_str

    except Exception as e:
        # Log the error, maybe return a placeholder or raise exception
        print(f"ERROR generating invoice number for tenant {tenant_school.schema_name}: {e}")
        # Depending on requirements, either raise the error or return a non-unique placeholder
        # raise # Re-raise to indicate failure
        # return f"ERR-{timezone.now().strftime('%Y%m%d%H%M%S')}" # Placeholder on error
        raise # Let the caller handle the exception
    
    

def generate_invoice_pdf(invoice_id): # Consider adding tenant arg if needed for profile lookup
    """
    Generates PDF content for a given Invoice ID using xhtml2pdf (Pisa).
    Returns PDF bytes or None if generation fails.
    """
    
    from .models import Invoice
    from apps.schools.models import SchoolProfile

    if not XHTML2PDF_AVAILABLE: # Should always be True now
        print("ERROR: xhtml2pdf library is missing.") # Should not happen
        return None

    try:
        invoice = Invoice.objects.select_related(
            'student', 'student__parent', 'academic_year'
        ).prefetch_related(
            'details__fee_head'
        ).get(pk=invoice_id)

        # --- Fetch School Profile (Still needs reliable method) ---
        # Using placeholder - REVISIT THIS
        # Best: Pass tenant object from view and use:
        # school_profile = SchoolProfile.objects.get(school=tenant)
        try:
            # Assuming profile PK matches tenant PK
            school_profile = SchoolProfile.objects.get(pk=invoice.student.tenant_id) # Still needs tenant_id on Student
            # Fallback - DANGEROUS
            # if not school_profile: school_profile = SchoolProfile.objects.first()
        except: # Catch specific errors later
            school_profile = None
        # --- End Placeholder ---


        context = {
            'invoice': invoice,
            'school_profile': school_profile,
            # Pass STATIC_URL/MEDIA_URL if your template uses them directly
            'STATIC_URL': settings.STATIC_URL,
            'MEDIA_URL': settings.MEDIA_URL,
        }

        html_string = render_to_string('fees/invoice_pdf.html', context)

        # Create a BytesIO buffer to receive the PDF data
        result_buffer = BytesIO()

        # --- Generate PDF using Pisa ---
        # Use link_callback to resolve static/media file paths
        pdf_status = pisa.CreatePDF(
            src=html_string,                # string containing HTML content
            dest=result_buffer,             # BytesIO buffer or file object
            link_callback=link_callback     # Function to handle resources
        )
        # --- End PDF Generation ---

        # Check if PDF generation was successful
        if pdf_status.err:
            print(f"ERROR generating PDF (pisa): {pdf_status.err}")
            return None

        # Get PDF content from buffer
        pdf_bytes = result_buffer.getvalue()
        result_buffer.close()

        return pdf_bytes

    except Invoice.DoesNotExist:
        print(f"ERROR: Invoice ID {invoice_id} not found for PDF generation.")
        return None
    except Exception as e:
        print(f"ERROR generating PDF for invoice {invoice_id}: {e}")
        import traceback
        traceback.print_exc()
        return None
    
from dateutil.relativedelta import relativedelta
from dateutil.rrule import rrule, MONTHLY, YEARLY


# --- CORRECTED Function Definition ---
# Use string 'AcademicYear' for type hint
def get_billing_periods(academic_year: 'AcademicYear', frequency: str):
    # --- Import model needed INSIDE this function ---
    from .models import AcademicYear, Term # Import Term if needed too
    # --- End import ---

    """Calculates billing periods (e.g., months, terms) within an academic year."""
    # Ensure input is the actual object if type hint used elsewhere
    if not isinstance(academic_year, AcademicYear):
        raise TypeError("get_billing_periods requires an AcademicYear model instance.")

    print(f"--- Util: Calculating periods for Year: {academic_year}, Freq: {frequency} ---")
    periods = []
    start = academic_year.start_date
    end = academic_year.end_date

    if frequency == 'ANNUALLY' or frequency == 'ONE_TIME':
        # For annual/one-time, the period is often the year itself or not applicable
        periods.append({'name': f"Annual {academic_year.name}", 'start': start, 'end': end})
    elif frequency == 'TERMLY':
        # Fetch terms associated with this academic year
        terms = Term.objects.filter(academic_year=academic_year).order_by('start_date')
        if terms.exists():
            for term in terms:
                periods.append({'name': term.name, 'start': term.start_date, 'end': term.end_date})
        else:
            print(f"WARNING: Termly frequency selected for year {academic_year} but no Terms found.")
            # Fallback? Maybe treat as annual? Or raise error?
            periods.append({'name': f"Full Year {academic_year.name}", 'start': start, 'end': end})

    elif frequency == 'MONTHLY':
        current_month_start = start
        while current_month_start <= end:
            # Find the end of the current month
            next_month = current_month_start.replace(day=28) + timezone.timedelta(days=4) # Go near end of month
            current_month_end = next_month - timezone.timedelta(days=next_month.day)
            # Ensure month end doesn't exceed academic year end
            current_month_end = min(current_month_end, end)

            periods.append({
                'name': current_month_start.strftime('%b %Y'), # e.g., Jan 2025
                'start': current_month_start,
                'end': current_month_end
            })
            # Move to start of next month
            current_month_start = current_month_end + timezone.timedelta(days=1)
    else:
        print(f"WARNING: Unknown frequency '{frequency}' in get_billing_periods.")
        # Default to annual?
        periods.append({'name': f"Annual {academic_year.name}", 'start': start, 'end': end})

    print(f"--- Util: Generated {len(periods)} periods ---")
    return periods


# --- Helper for Due Date Calculation (Needs School Policy) ---
def calculate_due_date(period_start_date, frequency):
    """ Calculates due date based on period start and school rules (placeholder) """
    # Example: Due 15 days after period start
    # In reality, might be 1st of month, 10th of month etc.
    from django.utils import timezone # Import here or globally
    # Placeholder: Due 15 days from period start
    # This needs to be configurable per school later
    return period_start_date + timezone.timedelta(days=15)

