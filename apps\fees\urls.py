# apps/fees/urls.py
from django.urls import path
from . import views

app_name = 'fees' # Namespace for this app

urlpatterns = [
    # --- Academic Year URLs ---
    path('academic-years/', views.AcademicYearListView.as_view(), name='academic_year_list'),
    path('academic-years/new/', views.AcademicYearCreateView.as_view(), name='academic_year_create'),
    path('academic-years/<int:pk>/edit/', views.AcademicYearUpdateView.as_view(), name='academic_year_update'),
    path('academic-years/<int:pk>/delete/', views.AcademicYearDeleteView.as_view(), name='academic_year_delete'),

    # --- Term URLs ---
    path('terms/', views.TermListView.as_view(), name='term_list'),
    path('terms/new/', views.TermCreateView.as_view(), name='term_create'),
    path('terms/<int:pk>/edit/', views.TermUpdateView.as_view(), name='term_update'),
    path('terms/<int:pk>/delete/', views.TermDeleteView.as_view(), name='term_delete'),

    # --- Fee Head URLs ---
    path('fee-heads/', views.FeeHeadListView.as_view(), name='fee_head_list'),
    path('fee-heads/new/', views.FeeHeadCreateView.as_view(), name='fee_head_create'),
    path('fee-heads/<int:pk>/edit/', views.FeeHeadUpdateView.as_view(), name='fee_head_update'),
    path('fee-heads/<int:pk>/delete/', views.FeeHeadDeleteView.as_view(), name='fee_head_delete'),


    path('invoices/', views.InvoiceListView.as_view(), name='invoice_list'),
    path('invoices/create-adhoc/', views.InvoiceCreateView.as_view(), name='invoice_create_adhoc'),
    path('invoices/<int:pk>/', views.InvoiceDetailView.as_view(), name='invoice_detail'),
    path('invoices/<int:pk>/pdf/', views.invoice_pdf_view, name='invoice_pdf'),
    path('invoices/<int:pk>/void/', views.void_invoice_view, name='invoice_void'),
    path('invoices/<int:pk>/issue/', views.issue_invoice_view, name='invoice_issue'),
    
    path('invoices/create/', views.ManualInvoiceCreateView.as_view(), name='invoice_create_manual'),
    path('invoices/<int:pk>/update/', views.InvoiceUpdateView.as_view(), name='invoice_update_manual'),
    path('invoices/generate-from-structure/', views.GenerateInvoicesFromStructureView.as_view(), name='generate_structure_invoices'),

    # --- Concession Type URLs ---
    path('concessions/', views.ConcessionTypeListView.as_view(), name='concession_type_list'),
    path('concessions/new/', views.ConcessionTypeCreateView.as_view(), name='concession_type_create'),
    path('concessions/<int:pk>/edit/', views.ConcessionTypeUpdateView.as_view(), name='concession_type_update'),
    path('concessions/<int:pk>/delete/', views.ConcessionTypeDeleteView.as_view(), name='concession_type_delete'),

    # --- Fee Structure URLs ---
    path('fee-structures/', views.FeeStructureListView.as_view(), name='fee_structure_list'),
    path('fee-structures/new/', views.FeeStructureCreateView.as_view(), name='fee_structure_create'),
    path('fee-structures/<int:pk>/edit/', views.FeeStructureUpdateView.as_view(), name='fee_structure_update'),
    path('fee-structures/<int:pk>/delete/', views.FeeStructureDeleteView.as_view(), name='fee_structure_delete'),
    #path('fee-structures/<int:structure_pk>/generate-invoices/', views.generate_invoices_for_structure, name='generate_invoices_for_structure'),

    # --- Student Fee Allocation URL ---
    # path('allocations/', views.StudentFeeAllocationListView.as_view(), name='allocation_list'), # Admin view
    # path('allocations/new/', views.StudentFeeAllocationCreateView.as_view(), name='allocation_create'), # If direct creation needed
    # path('allocations/<int:pk>/delete/', views.StudentFeeAllocationDeleteView.as_view(), name='allocation_delete'),

    # # Student Concession URLs
    # path('allocations/concessions/<int:pk>/delete/', views.StudentConcessionDeleteView.as_view(), name='student_concession_delete'),
    # path('allocations/concessions/<int:pk>/edit/', views.StudentConcessionUpdateView.as_view(), name='student_concession_update'), # <<< ADD THIS


    # Concession Type URLs
    path('concession-types/', views.ConcessionTypeListView.as_view(), name='concession_type_list'),
    path('concession-types/new/', views.ConcessionTypeCreateView.as_view(), name='concession_type_create'),
    path('concession-types/<int:pk>/edit/', views.ConcessionTypeUpdateView.as_view(), name='concession_type_update'),
    path('concession-types/<int:pk>/delete/', views.ConcessionTypeDeleteView.as_view(), name='concession_type_delete'),

    # Student Concession URLs
    # path('student-concessions/', views.StudentConcessionListView.as_view(), name='student_concession_list'), # Admin view
    # # path('student-concessions/new/', views.StudentConcessionCreateView.as_view(), name='student_concession_create'), # If direct creation needed
    # path('student-concessions/<int:pk>/edit/', views.StudentConcessionUpdateView.as_view(), name='student_concession_update'),
    # path('student-concessions/<int:pk>/delete/', views.StudentConcessionDeleteView.as_view(), name='student_concession_delete'),

    # --- Invoice URLs ---
    path('invoices/', views.InvoiceListView.as_view(), name='invoice_list'),
    path('invoices/<int:pk>/', views.InvoiceDetailView.as_view(), name='invoice_detail'),

    # path('invoices/<int:invoice_pk>/pdf/', views.generate_invoice_pdf, name='invoice_pdf'),

    # path('invoices/<int:pk>/cancel/', views.cancel_invoice, name='invoice_cancel'),




]



# # D:\school_fees_saas_V2\apps\fees\urls.py
# from django.urls import path
# from . import views

# app_name = 'fees'

# urlpatterns = [
#     # Academic Year URLs
#     path('academic-years/', views.AcademicYearListView.as_view(), name='academic_year_list'),
#     path('academic-years/new/', views.AcademicYearCreateView.as_view(), name='academic_year_create'),
#     path('academic-years/<int:pk>/edit/', views.AcademicYearUpdateView.as_view(), name='academic_year_update'),
#     path('academic-years/<int:pk>/delete/', views.AcademicYearDeleteView.as_view(), name='academic_year_delete'),

#     # Term URLs
#     path('terms/', views.TermListView.as_view(), name='term_list'),
#     path('terms/new/', views.TermCreateView.as_view(), name='term_create'),
#     path('terms/<int:pk>/edit/', views.TermUpdateView.as_view(), name='term_update'),
#     path('terms/<int:pk>/delete/', views.TermDeleteView.as_view(), name='term_delete'),

#     # Fee Head URLs
#     path('fee-heads/', views.FeeHeadListView.as_view(), name='fee_head_list'),
#     path('fee-heads/new/', views.FeeHeadCreateView.as_view(), name='fee_head_create'),
#     path('fee-heads/<int:pk>/edit/', views.FeeHeadUpdateView.as_view(), name='fee_head_update'),
#     path('fee-heads/<int:pk>/delete/', views.FeeHeadDeleteView.as_view(), name='fee_head_delete'),

#     # Concession Type URLs
#     path('concession-types/', views.ConcessionTypeListView.as_view(), name='concession_type_list'),
#     path('concession-types/new/', views.ConcessionTypeCreateView.as_view(), name='concession_type_create'),
#     path('concession-types/<int:pk>/edit/', views.ConcessionTypeUpdateView.as_view(), name='concession_type_update'),
#     path('concession-types/<int:pk>/delete/', views.ConcessionTypeDeleteView.as_view(), name='concession_type_delete'),

#     # Fee Structure URLs
#     path('fee-structures/', views.FeeStructureListView.as_view(), name='fee_structure_list'),
#     path('fee-structures/new/', views.FeeStructureCreateView.as_view(), name='fee_structure_create'),
#     path('fee-structures/<int:pk>/edit/', views.FeeStructureUpdateView.as_view(), name='fee_structure_update'),
#     path('fee-structures/<int:pk>/delete/', views.FeeStructureDeleteView.as_view(), name='fee_structure_delete'),

#     # Add this new URL pattern:
#     path('invoice/create-adhoc/', views.AdhocInvoiceCreateView.as_view(), name='invoice_create_adhoc'),
#     # Or if it's a function-based view:
#     # path('invoice/create-adhoc/', views.adhoc_invoice_create_view, name='invoice_create_adhoc'),
#     path('invoice/<int:pk>/', views.InvoiceDetailView.as_view(), name='invoice_detail'), # Assuming you have this
#     path('invoice/list/', views.InvoiceListView.as_view(), name='invoice_list'), # Assuming you have this
#     # ... other fee related URLs
    
#     # Student Concession Assignment URL (Accessed via Student Detail or dedicated page)
#     path('student/<int:student_pk>/assign-concession/', views.AssignStudentConcessionView.as_view(), name='assign_student_concession'),
#     # Example for deleting an assigned concession (you'd need a DeleteView for StudentConcession)
#     # path('assigned-concessions/<int:pk>/delete/', views.StudentConcessionDeleteView.as_view(), name='student_concession_delete'),

#     # Student Fee Allocation URL (Accessed via Student Detail or dedicated page)
#     path('student/<int:student_pk>/allocate-fee-structure/', views.AllocateStudentFeeView.as_view(), name='allocate_student_fee'),
#     # Example for deleting an allocation
#     # path('allocated-fees/<int:pk>/delete/', views.StudentFeeAllocationDeleteView.as_view(), name='student_fee_allocation_delete'),

#     # Invoice URLs
#     path('invoices/', views.InvoiceListView.as_view(), name='invoice_list'),
#     path('invoices/<int:pk>/', views.InvoiceDetailView.as_view(), name='invoice_detail'),
        
#     path('invoices/<int:pk>/update/', views.InvoiceUpdateView.as_view(), name='invoice_update'),
    
#     path('invoices/<int:pk>/delete/', views.InvoiceDeleteView.as_view(), name='invoice_delete'),
    
#     path('invoices/<int:pk>/pdf/', views.invoice_pdf_view, name='invoice_pdf'), # Function view
#     path('invoices/generate-by-structure/', views.GenerateStructureInvoiceView.as_view(), name='generate_structure_invoices'),
    
#     # Manual Invoice CRUD URLs
#     path('invoices/manual/create/', views.InvoiceManualCreateView.as_view(), name='invoice_create_manual'),
#     path('invoices/manual/<int:pk>/update/', views.InvoiceUpdateView.as_view(), name='invoice_update_manual'),
#     path('invoices/manual/<int:pk>/delete/', views.InvoiceDeleteView.as_view(), name='invoice_delete_manual'), # Add when view is ready

#     path('student-allocations/', views.StudentFeeAllocationListView.as_view(), name='student_fee_allocation_list'),
    
#     path('student-allocations/<int:pk>/delete/', views.StudentFeeAllocationDeleteView.as_view(), name='student_fee_allocation_delete'),
#     # You might add AJAX URLs here later, e.g., for fetching terms based on academic year
#     # path('ajax/load-terms/', views.load_terms_for_academic_year, name='ajax_load_terms'),
# ]



# # D:\school_fees_saas_v2\apps\fees\urls.py
# from django.urls import path
# from . import views

# app_name = 'fees'

# urlpatterns = [
#     # Academic Year URLs
#     path('academic-years/', views.AcademicYearListView.as_view(), name='academic_year_list'),
#     path('academic-years/new/', views.AcademicYearCreateView.as_view(), name='academic_year_create'),
#     path('academic-years/<int:pk>/edit/', views.AcademicYearUpdateView.as_view(), name='academic_year_update'),
#     path('academic-years/<int:pk>/delete/', views.AcademicYearDeleteView.as_view(), name='academic_year_delete'),

#     # Term URLs
#     path('terms/', views.TermListView.as_view(), name='term_list'),
#     path('terms/new/', views.TermCreateView.as_view(), name='term_create'),
#     path('terms/<int:pk>/edit/', views.TermUpdateView.as_view(), name='term_update'),
#     path('terms/<int:pk>/delete/', views.TermDeleteView.as_view(), name='term_delete'),

#     # Fee Type URLs# Fee Head URLs
#     path('fee-heads/', views.FeeHeadListView.as_view(), name='fee_head_list'), # <<< CHECK THIS LINE
#     path('fee-heads/new/', views.FeeHeadCreateView.as_view(), name='fee_head_create'),
#     path('fee-heads/<int:pk>/edit/', views.FeeHeadUpdateView.as_view(), name='fee_head_update'),
#     path('fee-heads/<int:pk>/delete/', views.FeeHeadDeleteView.as_view(), name='fee_head_delete'),

#     # ConcessionType URLs
#     path('concession-types/', views.ConcessionTypeListView.as_view(), name='concession_type_list'),
#     path('concession-types/new/', views.ConcessionTypeCreateView.as_view(), name='concession_type_create'),
#     path('concession-types/<int:pk>/edit/', views.ConcessionTypeUpdateView.as_view(), name='concession_type_update'),
#     path('concession-types/<int:pk>/delete/', views.ConcessionTypeDeleteView.as_view(), name='concession_type_delete'),

#     # Fee Structure URLs
#     path('fee-structures/', views.FeeStructureListView.as_view(), name='fee_structure_list'),
#     path('fee-structures/new/', views.FeeStructureCreateView.as_view(), name='fee_structure_create'),
#     path('fee-structures/<int:pk>/edit/', views.FeeStructureUpdateView.as_view(), name='fee_structure_update'),
#     path('fee-structures/<int:pk>/delete/', views.FeeStructureDeleteView.as_view(), name='fee_structure_delete'),
#     # path('fee-structures/<int:pk>/', views.FeeStructureDetailView.as_view(), name='fee_structure_detail'), # Optional DetailView
    
#     # Invoice URLs
#     path('invoices/', views.InvoiceListView.as_view(), name='invoice_list'),
#     path('invoices/create/', views.InvoiceCreateView.as_view(), name='invoice_create'),
#     path('invoices/<int:pk>/', views.InvoiceDetailView.as_view(), name='invoice_detail'),
#     path('invoices/<int:pk>/update/', views.InvoiceUpdateView.as_view(), name='invoice_update'),
#     #path('invoices/<int:pk>/delete/', views.InvoiceDeleteView.as_view(), name='invoice_delete'),
#     # Add URLs for actions like: record_payment, send_reminder, generate_pdf
    
#     # URL for Invoice Generation by Structure
#     path('invoices/generate-by-structure/', views.GenerateStructureInvoiceView.as_view(), name='generate_structure_invoices'),
#     # --- ADD THIS FOR MANUAL INVOICE CREATE ---
#     path('invoices/manual/create/', views.InvoiceCreateView.as_view(), name='invoice_create_manual'),
#     # --- AND FOR UPDATE IF YOU HAVE IT ---
#     path('invoices/manual/<int:pk>/update/', views.InvoiceUpdateView.as_view(), name='invoice_update_manual'),
#     # --- AND FOR DELETE IF YOU HAVE IT ---
#     #path('invoices/manual/<int:pk>/delete/', views.InvoiceDeleteView.as_view(), name='invoice_delete_manual'),
    
# ]