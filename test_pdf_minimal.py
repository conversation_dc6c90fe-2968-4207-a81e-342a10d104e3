#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from apps.common.utils import PDF_AVAILABLE, render_to_pdf
from django.template.loader import render_to_string

print(f"PDF_AVAILABLE: {PDF_AVAILABLE}")

if PDF_AVAILABLE:
    # Test minimal context
    context = {
        'invoice': {
            'pk': 21,
            'invoice_number_display': 'INV-001',
            'get_status_display': lambda: 'Draft',
            'issue_date': '2024-01-01',
            'due_date': None,
            'student': {
                'get_full_name': lambda: 'Test Student',
                'admission_number': 'ADM001',
                'current_class': {'name': 'Grade 1'},
                'current_section': None,
            },
            'notes_to_parent': None,
        },
        'school_profile': {
            'school_name_on_reports': 'Test School',
        },
        'tenant': {'name': 'Test School'},
        'charge_items': [],
        'concession_lines': [],
        'display_subtotal': 100.00,
        'display_total_concessions': 0.00,
        'display_amount_paid': 0.00,
        'display_balance_due': 100.00,
    }
    
    try:
        # Test HTML rendering
        html = render_to_string('fees/pdf/standalone_invoice.html', context)
        print(f"HTML rendered successfully, length: {len(html)}")
        
        # Test PDF generation
        pdf = render_to_pdf('fees/pdf/standalone_invoice.html', context)
        if pdf:
            print(f"PDF generated successfully, size: {len(pdf)} bytes")
            with open('test_invoice.pdf', 'wb') as f:
                f.write(pdf)
            print("PDF saved as test_invoice.pdf")
        else:
            print("PDF generation failed - returned None")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
else:
    print("PDF generation not available")
