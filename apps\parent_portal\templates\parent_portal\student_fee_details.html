{# templates/parent_portal/student_fee_details.html #}
{% extends "parent_portal/parent_portal_base.html" %}
{% load static humanize core_tags %}

{% block parent_portal_page_title %}Fee Details for {{ student.get_full_name|default:"Student" }}{% endblock %}

{% block parent_portal_extra_css %}
<style>
    .amount-due { font-weight: bold; color: var(--bs-danger); }
    .amount-paid { color: var(--bs-success); }
    .invoice-actions .btn { margin-right: 0.5rem; }
    .section-header {
        font-size: 1.25rem;
        font-weight: 500;
        color: var(--bs-primary);
        padding-bottom: 0.5rem;
        border-bottom: 2px solid var(--bs-primary-bg-subtle);
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block parent_portal_main_content %}
<div class="container mt-md-4">
    <div class="page-header mb-4">
        <h1 class="h2 mb-1">{{ view_title|default:"Fee Details" }}</h1>
        {% if student %}
            <p class="lead text-muted">
                Student: <strong>{{ student.get_full_name }}</strong> (Adm#: {{ student.admission_number }})
                <br>Class: {{ student.current_class.name|default:"-" }}{% if student.current_section %} / {{ student.current_section.name }}{% endif %}
            </p>
        {% endif %}
    </div>

    <div class="mb-3">
        <a href="{% url 'parent_portal:my_children_list' %}" class="btn btn-outline-secondary btn-sm"><i class="bi bi-arrow-left me-1"></i>Back to My Children</a>
        <a href="{% url 'parent_portal:dashboard' %}" class="btn btn-outline-secondary btn-sm"><i class="bi bi-house-door me-1"></i>Parent Dashboard</a>
    </div>

    {% include "partials/_messages.html" %}

    {% if not student %}
        <div class="alert alert-warning">Student details not found.</div>
        {% return %} {# Exit block if no student #}
    {% endif %}

    {# --- Outstanding Invoices Section --- #}
    <h3 class="section-header mt-4"><i class="bi bi-file-earmark-ruled-fill me-2"></i>Outstanding Invoices</h3>
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <span>Invoices Requiring Payment</span>
            {% if total_student_outstanding > 0 %}
                <span class="badge bg-danger rounded-pill fs-6">Total Due: {{ total_student_outstanding|currency:school_profile.currency_symbol }}</span>
            {% endif %}
        </div>
        {% if outstanding_invoices %}
            <div class="table-responsive">
                <table class="table table-sm table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Invoice #</th>
                            <th>Issue Date</th>
                            <th>Due Date</th>
                            <th>Description/Period</th>
                            <th class="text-end">Billed</th>
                            <th class="text-end">Discount</th>
                            <th class="text-end">Paid</th>
                            <th class="text-end">Amount Due</th>
                            <th class="text-center">Status</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for invoice in outstanding_invoices %}
                        <tr>
                            <td>{{ invoice.invoice_number|default:invoice.pk }}</td>
                            <td>{{ invoice.issue_date|date:"d M Y" }}</td>
                            <td>{{ invoice.due_date|date:"d M Y" }}</td>
                            <td>{{ invoice.primary_item_description|default:"School Fees" }}</td> {# Assumes a helper property #}
                            <td class="text-end">{{ invoice.total_billed_amount_before_concessions|currency:school_profile.currency_symbol }}</td>
                            <td class="text-end">
                                {% if invoice.total_concession_amount > 0 %}
                                    ({{ invoice.total_concession_amount|currency:school_profile.currency_symbol }})
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td class="text-end amount-paid">{{ invoice.amount_paid|currency:school_profile.currency_symbol }}</td>
                            <td class="text-end amount-due">{{ invoice.balance_due|currency:school_profile.currency_symbol }}</td>
                            <td class="text-center">
                                <span class="badge status-badge bg-{% if invoice.status == 'PAID' %}success{% elif invoice.status == 'OVERDUE' %}danger{% elif invoice.status == 'PARTIALLY_PAID' %}warning text-dark{% else %}primary{% endif %}">
                                    {{ invoice.get_status_display|capfirst }}
                                </span>
                            </td>
                            <td class="text-center invoice-actions">
                                <a href="{% url 'fees:invoice_pdf_public' pk=invoice.pk %}" class="btn btn-outline-secondary btn-sm" target="_blank" title="View PDF"><i class="bi bi-file-earmark-pdf"></i></a>
                                {% if invoice.balance_due > 0 and tenant_features.ONLINE_PAYMENTS %}
                                <a href="{% url 'payments:initiate_parent_payment' invoice_pk=invoice.pk %}" class="btn btn-success btn-sm" title="Pay Online"><i class="bi bi-credit-card"></i> Pay</a>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="card-body text-center py-4">
                <p class="text-success mb-0 fs-5"><i class="bi bi-check-circle-fill me-2"></i>No outstanding invoices for {{ student.get_full_name }}.</p>
            </div>
        {% endif %}
    </div>

    {# --- Recent Payment History Section --- #}
    <h3 class="section-header mt-5"><i class="bi bi-collection-fill me-2"></i>Recent Payments</h3>
    <div class="card shadow-sm">
        <div class="card-header bg-light">
            Last 10 Recorded Payments
        </div>
        {% if recent_payments %}
            <div class="table-responsive">
                <table class="table table-sm table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Date Paid</th>
                            <th class="text-end">Amount</th>
                            <th>Method</th>
                            <th>Reference</th>
                            <th>Status</th>
                            <th>For Invoice #</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in recent_payments %}
                        <tr>
                            <td>{{ payment.payment_date|date:"d M Y, H:i" }}</td>
                            <td class="text-end">{{ payment.amount|currency:school_profile.currency_symbol }}</td>
                            <td>{{ payment.payment_method.name|default:"N/A" }}</td>
                            <td>{{ payment.reference_number|default:"-" }}</td>
                            <td>
                                <span class="badge bg-{% if payment.status == payment.PaymentStatus.COMPLETED %}success{% elif payment.status == payment.PaymentStatus.FAILED %}danger{% elif payment.status == payment.PaymentStatus.PENDING %}warning text-dark{% else %}secondary{% endif %}">
                                    {{ payment.get_status_display|capfirst }}
                                </span>
                            </td>
                            <td>
                                {% if payment.invoice %}
                                    {{ payment.invoice.invoice_number|default:payment.invoice.pk }}
                                {% else %}General{% endif %}
                            </td>
                            <td class="text-center">
                                <a href="{% url 'payments:payment_receipt_pdf_public' pk=payment.pk %}" class="btn btn-outline-secondary btn-sm" target="_blank" title="View Receipt"><i class="bi bi-receipt"></i></a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <div class="card-footer text-center py-2">
                <a href="{% url 'parent_portal:payment_history_list' %}" class="btn btn-link btn-sm">View Full Payment History</a>
            </div>
        {% else %}
            <div class="card-body text-center py-4">
                <p class="text-muted mb-0">No recent payments found for {{ student.get_full_name }}.</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock parent_portal_main_content %}

































{% comment %} {# templates/parent_portal/student_fee_details.html #}
{% extends "parent_portal_base.html" %}

{% load humanize core_tags %}

{% block title %}{{ view_title|default:"Fee Details" }}{% endblock %}

{% block content %}
<div class="container mt-4">
    {# Display student name in heading #}
    <h1>{{ view_title|default:"Fee Details" }} {% if student %}for {{ student.full_name }}{% endif %}</h1>
    {% if student %}
        <p class="text-muted">Class: {{ student.school_class.name|default:'-' }} - {{ student.section.name|default:'-' }} | Admission No: {{ student.admission_number }}</p>
    {% endif %}

    <p>
        <a href="{% url 'parent_portal:my_children' %}" class="btn btn-secondary btn-sm">Back to My Children</a>
        <a href="{% url 'parent_portal:dashboard' %}" class="btn btn-secondary btn-sm">Back to Dashboard</a>
    </p>

    {% include "includes/_messages.html" %}

    {# --- Outstanding Invoices Section --- #}
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-file-earmark-text-fill me-2"></i>Outstanding Invoices
            {% if total_student_outstanding > 0 %}
                <span class="badge bg-danger float-end">Total Due: {{ total_student_outstanding|currency:school_profile.currency_symbol }}</span>
            {% endif %}
        </div>
        {% if outstanding_invoices %}
            <div class="table-responsive">
                <table class="table table-sm table-striped table-hover mb-0">
                    <thead>
                        <tr>
                            <th>Inv #</th>
                            <th>Issue Date</th>
                            <th>Due Date</th>
                            <th>Period</th>
                            <th style="text-align: right;">Net Amt</th>
                            <th class="text-end">Discount</th> {# <-- Add Header #}
                            <th style="text-align: right;">Paid</th>
                            <th style="text-align: right;">Due Now</th>
                            <th style="text-align: center;">Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for invoice in outstanding_invoices %}
                        <tr>
                            <td>{{ invoice.invoice_number|default:invoice.pk }}</td>
                            <td>{{ invoice.issue_date|date:"Y-m-d" }}</td>
                            <td>{{ invoice.due_date|date:"Y-m-d" }}</td>
                            <td>{{ invoice.period_description|default:"N/A" }}</td>
                            <td style="text-align: right;">{{ invoice.net_amount|floatformat:2|intcomma }}</td>
                            
                            {# --- Display Discount --- #}
                            <td class="text-end">
                                {# Check if discount_applied exists AND is greater than 0 #}
                                {% if invoice.discount_applied and invoice.discount_applied > 0 %}
                                    {{ invoice.discount_applied|currency:school_profile.currency_symbol }}
                                {% else %}
                                    - {# Show dash if no discount or zero #}
                                {% endif %}
                            </td>
                            {# --- End Discount --- #}

                            <td style="text-align: right;">{{ invoice.amount_paid|floatformat:2|intcomma }}</td>
                            <td style="text-align: right;">{{ invoice.amount_due|floatformat:2|intcomma }}</td>
                            <td style="text-align: center;">
                                <span class="badge bg-{% if invoice.status == 'PAID' %}success{% elif invoice.status == 'OVERDUE'%}danger{% elif invoice.status == 'PARTIAL'%}warning text-dark{% else %}primary{% endif %}">
                                    {{ invoice.get_status_display }}
                                </span>
                            </td>
                            <td> {# Actions #}
                                <a href="#" class="btn btn-secondary btn-sm" target="_blank" title="View PDF">PDF</a> {# Link to Invoice PDF later #}
                                <a href="#" class="btn btn-success btn-sm" title="Pay Online">Pay</a> {# Link to Payment Gateway later #}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="card-body">
                <p class="text-success mb-0"><i class="bi bi-check-circle-fill me-2"></i>No outstanding invoices found for this student.</p>
            </div>
        {% endif %}
    </div> {# End Outstanding Invoices Card #}


    {# --- Payment History Section --- #}
    <div class="card">
        <div class="card-header">
            <i class="bi bi-clock-history me-2"></i>Recent Payment History
        </div>
        {% if payment_history %}
            <div class="table-responsive">
                <table class="table table-sm table-striped table-hover mb-0">
                    <thead>
                        <tr>
                            <th>Date Paid</th>
                            <th>Amount</th>
                            <th>Mode</th>
                            <th>Reference</th>
                            <th>Invoice #</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in payment_history %}
                        <tr>
                            <td>{{ payment.payment_date|date:"Y-m-d" }}</td>
                            <td style="text-align: right;">{{ payment.amount_paid|currency:school_profile.currency_symbol }}</td>
                            <td>{{ payment.get_mode_display }}</td>
                            <td>{{ payment.reference_number|default:"-" }}</td>
                            <td>
                                {% if payment.invoice %}
                                    {{ payment.invoice.invoice_number|default:payment.invoice.pk }}
                                {% else %}N/A{% endif %}
                            </td>
                            <td>
                                <a href="{% url 'payments:payment_receipt_pdf' payment.pk %}" class="btn btn-secondary btn-sm" target="_blank" title="View Receipt">Receipt</a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <div class="card-footer text-center">
                <a href="{% url 'parent_portal:payment_history' %}">View Full Payment History</a>
            </div>
        {% else %}
            <div class="card-body">
                <p class="text-muted mb-0">No recent payments found for this student.</p>
            </div>
        {% endif %}
    </div> {# End Payment History Card #}


</div> {# End container #}
{% endblock %}
 {% endcomment %}
