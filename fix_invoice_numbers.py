import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
from django_tenants.utils import get_tenant_model

# Get the tenant
TenantModel = get_tenant_model()
tenant = TenantModel.objects.get(schema_name='alpha')

# Switch to tenant schema
connection.set_tenant(tenant)

# Fix invoices with empty invoice numbers
with connection.cursor() as cursor:
    # First, let's see what we have
    cursor.execute("SELECT id, invoice_number, student_id FROM fees_invoice WHERE invoice_number = '';")
    empty_invoices = cursor.fetchall()
    
    print(f'Found {len(empty_invoices)} invoices with empty invoice numbers:')
    for row in empty_invoices:
        print(f'  ID: {row[0]}, Number: "{row[1]}", Student ID: {row[2]}')
    
    # Generate proper invoice numbers for these invoices
    for invoice_id, _, student_id in empty_invoices:
        # Generate a simple invoice number based on ID
        new_invoice_number = f"INV-{invoice_id:06d}"
        
        # Update the invoice
        cursor.execute(
            "UPDATE fees_invoice SET invoice_number = %s WHERE id = %s;",
            [new_invoice_number, invoice_id]
        )
        print(f'Updated invoice {invoice_id} with number: {new_invoice_number}')
    
    # Verify the fix
    cursor.execute("SELECT COUNT(*) FROM fees_invoice WHERE invoice_number = '';")
    remaining_empty = cursor.fetchone()[0]
    print(f'\nRemaining invoices with empty invoice_number: {remaining_empty}')
    
    # Show all invoice numbers now
    cursor.execute("SELECT id, invoice_number FROM fees_invoice ORDER BY id;")
    all_invoices = cursor.fetchall()
    print('\nAll invoices after fix:')
    for row in all_invoices:
        print(f'  ID: {row[0]}, Number: "{row[1]}"')
