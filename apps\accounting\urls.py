# apps/accounting/urls.py
from django.urls import path
from . import views

app_name = 'accounting'

urlpatterns = [
    # Chart of Accounts
    path('accounts/', views.AccountListView.as_view(), name='account_list'),
    path('accounts/new/', views.AccountCreateView.as_view(), name='account_create'),
    path('accounts/<int:pk>/edit/', views.AccountUpdateView.as_view(), name='account_update'),
    path('accounts/<int:pk>/delete/', views.AccountDeleteView.as_view(), name='account_delete'),
    
    # path('chart-of-accounts/', views.AccountListView.as_view, name='coa_list'),
    
    path('chart-of-accounts/', views.AccountListView.as_view(), name='accounts_list'), 

    path('chart-of-accounts/new/', views.AccountCreateView.as_view(), name='accounts_create'), # <<<< THIS IS NEEDED
    path('chart-of-accounts/<int:pk>/update/', views.AccountUpdateView.as_view(), name='accounts_update'),
    path('chart-of-accounts/<int:pk>/delete/', views.AccountDeleteView.as_view(), name='accounts_delete'),
    
    
    # # Journal Entries
    # path('journal-entries/', views.JournalEntryListView.as_view(), name='journalentry_list'),
    # path('journal-entries/new/', views.JournalEntryCreateView.as_view(), name='journalentry_create'),
    # path('journal-entries/<int:pk>/', views.JournalEntryDetailView.as_view(), name='journalentry_detail'),
    # path('journal-entries/<int:pk>/edit/', views.JournalEntryUpdateView.as_view(), name='journalentry_update'),
    # path('journal-entries/<int:pk>/delete/', views.JournalEntryDeleteView.as_view(), name='journal_entry_delete'),
    
    # # --- Account Type URLs (assuming these already exist) ---
    # path('account-types/', views.AccountTypeListView.as_view(), name='account_type_list'),
    # path('account-types/create/', views.AccountTypeCreateView.as_view(), name='account_type_create'),
    # path('account-types/<int:pk>/update/', views.AccountTypeUpdateView.as_view(), name='account_type_update'),
    # path('account-types/<int:pk>/delete/', views.AccountTypeDeleteView.as_view(), name='account_type_delete'),

    # --- Manual Journal Entry URLs ---
    path('journal-entries/', views.JournalEntryListView.as_view(), name='journalentry_list'),
    path('journal-entries/create/', views.JournalEntryCreateView.as_view(), name='journalentry_create'),
    path('journal-entries/<int:pk>/', views.JournalEntryDetailView.as_view(), name='journalentry_detail'),
    path('journal-entries/<int:pk>/post/', views.post_journal_entry_view, name='journalentry_post'),
    
    # --- Placeholders for Future Journal Entry Actions (Update/Delete for Drafts) ---
    path('journal-entries/<int:pk>/update/', views.JournalEntryUpdateView.as_view(), name='journalentry_update'),
    path('journal-entries/<int:pk>/delete/', views.JournalEntryDeleteView.as_view(), name='journalentry_delete'),
    # path('journal-entries/<int:pk>/reverse/', views.JournalEntryReverseView.as_view(), name='journalentry_reverse'),
    
    # Detailed Account Ledger
    path('account-ledger/', views.AccountLedgerView.as_view(), name='account_ledger'),
    
    path('journal-entries/<int:pk>/confirm-reverse/', views.ConfirmReverseJournalEntryView.as_view(), name='journalentry_confirm_reverse'),
    path('journal-entries/<int:pk>/reverse-action/', views.reverse_journal_entry_action, name='journalentry_reverse_action'),
    
]

