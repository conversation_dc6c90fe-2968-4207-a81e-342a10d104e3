{# D:\school_fees_saas_v2\apps\announcements\templates\announcements\announcement_detail.html #}
{% extends "tenant_base.html" %}
{% load static i18n humanize %}

{% block tenant_page_title %}{{ announcement.title }} - {{ view_title|default:_("Announcement") }}{% endblock %}

{% block tenant_specific_content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2 class="mb-0">{{ announcement.title }}</h2>
        <div>
            {% if perms.announcements.change_announcement %}
            <a href="{% url 'announcements_tenant:update' pk=announcement.pk %}" class="btn btn-primary me-2">
                <i class="bi bi-pencil-square me-1"></i> {% trans "Edit" %}
            </a>
            {% endif %}
            <a href="{% url 'announcements_tenant:list' %}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left-circle me-1"></i> {% trans "Back to List" %}
            </a>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-header">
            <div class="row">
                <div class="col-md-6">
                    <small class="text-muted">{% trans "Author" %}: {{ announcement.author.get_full_name|default:announcement.author.email|default:"N/A" }}</small><br>
                    <small class="text-muted">{% trans "Published" %}: {{ announcement.publish_date|naturalday:"D, M j, Y P" }}</small>
                </div>
                <div class="col-md-6 text-md-end">
                    {% if announcement.expiry_date %}
                        <small class="text-muted">{% trans "Expires" %}: {{ announcement.expiry_date|naturalday:"D, M j, Y P" }}</small><br>
                    {% endif %}
                    <small class="text-muted">{% trans "Audience" %}: {{ announcement.get_audience_display }}</small>
                </div>
            </div>
            <div class="mt-1">
                {% if announcement.is_sticky %}<span class="badge bg-warning text-dark me-1"><i class="bi bi-pin-angle-fill"></i> {% trans "Pinned" %}</span>{% endif %}
                {% if announcement.is_published and announcement.is_currently_visible %}
                    <span class="badge bg-success">{% trans "Currently Visible" %}</span>
                {% elif announcement.is_published %}
                    <span class="badge bg-secondary">{% trans "Scheduled/Expired" %}</span>
                {% else %}
                    <span class="badge bg-light text-dark border">{% trans "Draft" %}</span>
                {% endif %}
            </div>
        </div>
        <div class="card-body">
            {# Assuming content is safe or you'll use a safe rendering method if it contains HTML #}
            <div class="announcement-content py-3">
                {{ announcement.content|linebreaksbr }} {# Use linebreaksbr for simple text, or |safe if it's trusted HTML #}
            </div>
        </div>
        <div class="card-footer text-muted small">
            {% trans "Created" %}: {{ announcement.created_at|timesince }} {% trans "ago" %} | 
            {% trans "Last updated" %}: {{ announcement.updated_at|timesince }} {% trans "ago" %}
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}




















