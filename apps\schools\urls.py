# D:\school_fees_saas_v2\apps\schools\urls.py
from django.urls import path, include
from . import views

from .views import load_sections_for_class # Import the AJAX view

app_name = 'schools'

urlpatterns = [
    path('dashboard/', views.tenant_dashboard, name='dashboard'),
    path('profile/update', views.SchoolProfileUpdateView.as_view(), name='profile_update'),

    # Staff URLs
    path('staff/login/', views.staff_login_view, name='staff_login'),
    path('staff/logout/', views.staff_logout_view, name='staff_logout_view'),
    path('staff/', views.StaffListView.as_view(), name='staff_list'),
    path('staff/new/', views.StaffCreateView.as_view(), name='staff_create'),
    path('staff/<int:pk>/', views.StaffDetailView.as_view(), name='staff_detail'),
    path('staff/<int:pk>/edit/', views.StaffUpdateView.as_view(), name='staff_update'),
    path('staff/<int:pk>/delete/', views.StaffDeleteView.as_view(), name='staff_delete'),
    path('staff/<int:pk>/roles/', views.AssignStaffRolesView.as_view(), name='staff_assign_roles'),
    
    
    path('staff/profile/<int:pk>/', views.StaffProfileDetailView.as_view(), name='staff_profile_detail'),
    

    # --- SchoolClass (Class/Grade) URLs ---
    path('classes/', views.SchoolClassListView.as_view(), name='class_list'),
    path('classes/new/', views.SchoolClassCreateView.as_view(), name='class_create'),
    path('classes/<int:pk>/edit/', views.SchoolClassUpdateView.as_view(), name='class_update'),
    path('classes/<int:pk>/delete/', views.SchoolClassDeleteView.as_view(), name='class_delete'),

    # --- Section URLs (Nested under a specific class via class_pk) ---
    path('classes/<int:class_pk>/sections/', views.SectionListViewForClass.as_view(), name='section_list_for_class'),
    path('classes/<int:class_pk>/sections/new/', views.SectionCreateViewForClass.as_view(), name='section_create_for_class'),
    # For updating/deleting sections, we use the section's own PK
    path('sections/<int:pk>/edit/', views.SectionUpdateView.as_view(), name='section_update'),
    path('sections/<int:pk>/delete/', views.SectionDeleteView.as_view(), name='section_delete'),
    
    # Parent
    path('parent/login/', views.parent_login_view, name='parent_login'),
    path('parent/logout/', views.parent_logout_view, name='parent_logout'),
    path('parent/dashboard/', views.parent_dashboard_view, name='parent_dashboard'),
    
    # path('profile/update/', views.school_profile_update_view, name='profile_update'),
    
    path('ajax/load-sections/', load_sections_for_class, name='ajax_load_sections'),
    
    path('academic-settings/update/', views.academic_settings_update_view, name='academic_settings_update'),
    
    path('settings/academic/', views.AcademicSettingUpdateView.as_view(), name='academic_settings_update'),
    
    # --- Academic Year CRUD URLs ---
    path('academic-years/', views.AcademicYearListView.as_view(), name='academic_year_list'),
    path('academic-years/new/', views.AcademicYearCreateView.as_view(), name='academic_year_create'),
    path('academic-years/<int:pk>/edit/', views.AcademicYearUpdateView.as_view(), name='academic_year_update'),
    path('academic-years/<int:pk>/delete/', views.AcademicYearDeleteView.as_view(), name='academic_year_delete'),
    
    # --- Term CRUD URLs ---
    path('terms/', views.TermListView.as_view(), name='term_list'),
    path('terms/new/', views.TermCreateView.as_view(), name='term_create'),
    path('terms/<int:pk>/edit/', views.TermUpdateView.as_view(), name='term_update'),
    path('terms/<int:pk>/delete/', views.TermDeleteView.as_view(), name='term_delete'),
    
    
    # Include announcements URLs under a prefix like 'announcements/'
    path('announcements/', include('apps.announcements.urls', namespace='announcements')), # <<< ADD THIS
    
    path('school-bulletin/', include('apps.announcements.urls', namespace='bulletin')),
    
    # Include communication URLs under a prefix like 'communication/'
    # path('communication/', include('apps.communication.urls', namespace='communication')), # <<< ADD THIS
]