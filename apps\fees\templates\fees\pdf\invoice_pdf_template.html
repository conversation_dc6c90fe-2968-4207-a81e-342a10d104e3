{# D:\school_fees_saas_v2\apps\fees\templates\fees\pdf\invoice_pdf_template.html #}
{% extends "reporting/pdf/_pdf_base.html" %}
{% load static humanize fees_tags core_tags math_filters %}

{% block pdf_title %}Invoice #{{ invoice.invoice_number_display|default:invoice.pk }} - {{ school_profile.school_name_on_reports|default:tenant.name }}{% endblock %}

{% block pdf_header_content %}
    {# Your pdf_header_content is good, no changes needed here. #}
    <table style="width: 100%; border-collapse: collapse; vertical-align: top;">
        <tr>
            <td style="width: 55%; padding-right: 10mm; vertical-align: top;">
                {% if school_profile.logo and school_profile.logo.path %}
                    <img src="{{ school_profile.logo.path }}" style="max-height: 16mm; max-width: 45mm; margin-bottom: 3mm;" alt="School Logo">
                {% endif %}
                <h2 style="margin: 0 0 2mm 0; font-size: 14pt; color: #2c3e50; font-weight:bold;">
                    {{ school_profile.school_name_on_reports|default:school_profile.school_name_override|default:tenant.name }}
                </h2>
                <div style="font-size: 8pt; color: #444; line-height: 1.25;">
                    {% if school_profile.address_line_1 %}{{ school_profile.address_line_1 }}<br>{% endif %}
                    {% if school_profile.address_line_2 %}{{ school_profile.address_line_2 }}<br>{% endif %}
                    {% if school_profile.city %}{{ school_profile.city }}{% endif %}{% if school_profile.city and school_profile.state_province %}, {% endif %}{% if school_profile.state_province %}{{ school_profile.state_province }}{% endif %} {% if school_profile.postal_code %}{{ school_profile.postal_code }}{% endif %}
                    {% if school_profile.country_name %}<br>{{ school_profile.country_name }}{% endif %}
                </div>
            </td>
            <td style="width: 45%; text-align: right; vertical-align: top;">
                <h1 style="margin:0 0 8px 0; font-size:22pt; color: #0056b3; text-transform: uppercase; letter-spacing: 0.5px;">INVOICE</h1>
                <div style="background-color: #e7f3fe; padding: 2mm 3mm; border-left: 1mm solid #007bff; font-size: 9pt; display: inline-block; text-align: left;">
                    <p style="margin:0.5mm 0; font-weight: bold;">Invoice No: {{ invoice.invoice_number_display|default:invoice.pk }}</p>
                    <p style="margin:0.5mm 0;">Status: <strong style="text-transform: capitalize;">{% invoice_status_badge invoice.status plain_text=True %}</strong></p>
                    <p style="margin:0.5mm 0;">Issue Date: {{ invoice.issue_date|date:"F d, Y" }}</p>
                    {% if invoice.due_date %}<p style="margin:0.5mm 0;">Due Date: <strong class="warning-text">{{ invoice.due_date|date:"F d, Y" }}</strong></p>{% endif %}
                </div>
            </td>
        </tr>
    </table>
    <hr style="margin-top: 1.5mm; margin-bottom: 1.5mm; border: none; border-top: 0.5px solid #333;">
{% endblock pdf_header_content %}

{% block pdf_extra_styles %}
    {{ block.super }}
    <style type="text/css">
        /* --- General Invoice Styles --- */
        .address-box { padding: 2.5mm; font-size: 8.5pt; line-height: 1.35; vertical-align: top; }
        .bill-to-box { background-color: #f8f9fa; border: 0.5px solid #dee2e6; border-left: 2px solid #007bff; }
        .bill-to-box h4, .period-info-box h4 { margin: 0 0 1.5mm 0; color: #0056b3; font-size: 9.5pt; font-weight: bold; padding-bottom: 1mm; border-bottom: 0.5px solid #cce5ff; }
        .period-info-box { text-align: right; }

        /* === Items Table - AGGRESSIVE FIXES for column squashing === */
        .items-table {
            table-layout: fixed !important; /* Force fixed layout */
            width: 100% !important;         /* Force 100% width */
            border-collapse: collapse;
            margin: 0;
            padding: 0;
        }
        .items-table th, .items-table td {
             box-sizing: border-box;
             padding: 2.5mm 3mm;
             font-size: 9pt;
             vertical-align: top;
             line-height: 1.3;
             word-wrap: break-word; /* Apply to ALL cells to handle any long unbreakable strings */
        }
        .items-table thead th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
            border: 0.5px solid #0056b3;
            text-align: left;
        }
        .items-table tbody td {
            border: 0.5px solid #e9ecef;
        }
        .items-table .item-description strong { color: #2c3e50; }
        .items-table .item-description small { color: #7f8c8d; font-style: italic; font-size: 8pt; }
        .items-table td.text-center, .items-table td.text-right {
            white-space: nowrap; /* Keep this for numbers */
        }
        .items-table tbody tr:nth-child(even) td { background-color: #f7faff; }
        .items-table .discount-row td { color: #096dd9; font-style: italic; background-color: #f0f9ff; }
        .items-table .total-amount-cell { font-weight: 500; }

        /* --- Summary & Other Styles --- */
        .summary-table { width: 48%; margin-left: 52%; border: 1px solid #007bff; margin-top: 5mm; font-size: 8.5pt; border-collapse: collapse; }
        .summary-table td { padding: 1.5mm 2.5mm; border-top: 0.5px solid #ecf0f1; }
        .summary-table .label { font-weight: 600; text-align: right; background-color: #f0f7ff; color: #004085; width: 60%; }
        .summary-table .value { text-align: right; width: 40%; font-family: 'Menlo', 'Consolas', 'Courier New', monospace; }
        .summary-table tr.grand-total td { background-color: #0056b3; color: white; font-weight: bold; font-size: 10pt; padding: 1.8mm 2.5mm; }
    </style>
{% endblock %}

{% block pdf_main_content %}
    <!-- Bill To & Academic Period Table -->
    <table class="no-border" style="margin-bottom: 7mm; width: 100%;">
        {# Your bill-to section is good, no changes needed here. #}
        <tr>
            <td style="width: 55%;" class="address-box bill-to-box">
                <h4>Bill To</h4>
                {% if invoice.student %}
                    <p style="font-weight: bold; font-size: 10pt; color: #2c3e50;">{{ invoice.student.get_full_name }}</p>
                    {% if invoice.student.admission_number %}<p>Adm. No: {{ invoice.student.admission_number }}</p>{% endif %}
                    {% if invoice.student.current_class %}
                        <p>Class: {{ invoice.student.current_class.name }}{% if invoice.student.current_section %} - {{ invoice.student.current_section.name }}{% endif %}</p>
                    {% endif %}
                {% endif %}
            </td>
            <td style="width: 45%; vertical-align: top;" class="address-box period-info-box">
                <h4>Period Details</h4>
                {% if invoice.academic_year %}<p><strong>Academic Year:</strong> {{ invoice.academic_year.name }}</p>{% endif %}
                {% if invoice.term %}<p><strong>Term:</strong> {{ invoice.term.name }}</p>{% endif %}
            </td>
        </tr>
    </table>

    <h3 class="section-title">Invoice Breakdown</h3>
    <table class="items-table">
        <colgroup>
            <col style="width: 40%;">
            <col style="width: 30%;">
            <col style="width: 10%;">
            <col style="width: 20%;">
        </colgroup>
        <thead>
            <tr>
                <th>Item / Description</th>
                <th class="text-right">Unit Price</th>
                <th class="text-center">Qty</th>
                <th class="text-right">Total</th>
            </tr>
        </thead>
        <tbody>
            {% for item in charge_items %}
            <tr>
                <td class="item-description">
                    <strong>{% if item.fee_head %}{{ item.fee_head.name }}{% else %}{{ item.description }}{% endif %}</strong>
                    {% if item.description and item.fee_head and item.description|lower != item.fee_head.name|lower %}<br><small>{{ item.description }}</small>{% endif %}
                </td>
                <td class="text-right">{{ school_profile.currency_symbol|default:'$' }}{{ item.unit_price|floatformat:2|intcomma }}</td>
                <td class="text-center">{{ item.quantity|floatformat:0 }}</td>
                <td class="text-right total-amount-cell">{{ school_profile.currency_symbol|default:'$' }}{{ item.amount|floatformat:2|intcomma }}</td>
            </tr>
            {% endfor %}

            {% for item in concession_lines %}
            <tr class="discount-row">
                <td class="item-description">
                    <strong>{% if item.concession_type %}{{ item.concession_type.name }}{% else %}{{ item.description }}{% endif %}</strong>
                    <em class="muted">(Discount)</em>
                    {% if item.description and item.concession_type and item.description|lower != item.concession_type.name|lower %}<br><small>{{ item.description }}</small>{% endif %}
                </td>
                <td class="text-right"></td>
                <td class="text-center">-</td>
                <td class="text-right total-amount-cell">({{ school_profile.currency_symbol|default:'$' }}{{ item.amount|abs|floatformat:2|intcomma }})</td>
            </tr>
            {% endfor %}

            {% if not charge_items and not concession_lines %}

            <tr>
                <td colspan="4" class="text-center italic" style="padding: 4mm; color: #7f8c8d;">
                    No items found for this invoice.
                </td>
            </tr>
            
            {% endif %}
        </tbody>
    </table>

    <table class="summary-table">
        {# Your summary table is good, no changes needed here. #}
        <tr>
            <td class="label">Subtotal:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_subtotal|floatformat:2|intcomma }}</td>
        </tr>
        {% if display_total_concessions > 0 %}
        <tr class="discount-amount">
            <td class="label">Total Discounts:</td>
            <td class="value">- {{ school_profile.currency_symbol|default:'$' }}{{ display_total_concessions|floatformat:2|intcomma }}</td>
        </tr>
        <tr class="net-billable">
            <td class="label">Net Billable Amount:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_net_billable|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}
        {% if display_amount_paid > 0 %}
        <tr class="paid-amount">
            <td class="label">Amount Paid:</td>
            <td class="value">- {{ school_profile.currency_symbol|default:'$' }}{{ display_amount_paid|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}
        <tr class="grand-total">
            <td class="label">BALANCE DUE:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_balance_due|floatformat:2|intcomma }}</td>
        </tr>
    </table>

    {# The rest of your notes and final message sections are good. #}
    {% if invoice.notes_to_parent %}
        <div class="notes-section-box">
            <h4>Important Notes</h4>
            <div>{{ invoice.notes_to_parent|linebreaksbr }}</div>
        </div>
    {% endif %}

    {% if school_profile.payment_instructions %}
        <div class="notes-section-box" style="border-left-color: #f39c12; margin-top:5mm;">
            <h4 style="color: #d35400;">Payment Instructions</h4>
            <div>{{ school_profile.payment_instructions|linebreaksbr }}</div>
        </div>
    {% endif %}

{% endblock pdf_main_content %}

{% block pdf_footer_content %}
    {# Your footer content is good. #}
    <p>{{ school_profile.school_name_on_reports|default:tenant.name }} - Generated: {% now "F d, Y H:i" %}</p>
    <p class="page-number"></p>
{% endblock pdf_footer_content %}






















{% comment %} {# D:\school_fees_saas_v2\apps\fees\templates\fees\pdf\invoice_pdf_template.html #}
{% extends "reporting/pdf/_pdf_base.html" %}
{% load static humanize fees_tags core_tags math_filters %}

{% block pdf_title %}Invoice #{{ invoice.invoice_number_display|default:invoice.pk }} - {{ school_profile.school_name_on_reports|default:tenant.name }}{% endblock %}

{# This block OVERRIDES pdf_header_content from _pdf_base.html to provide INVOICE-SPECIFIC header #}
{% block pdf_header_content %}
    <table style="width: 100%; border-collapse: collapse; vertical-align: top;">
        <tr>
            <td style="width: 55%; padding-right: 10mm; vertical-align: top;"> {# School Info Column #}
                {% if school_profile.logo and school_profile.logo.path %}
                    <img src="{{ school_profile.logo.path }}" style="max-height: 16mm; max-width: 45mm; margin-bottom: 3mm;" alt="School Logo">
                {% endif %}

                <h2 style="margin: 0 0 2mm 0; font-size: 14pt; color: #2c3e50; font-weight:bold;">
                    {{ school_profile.school_name_on_reports|default:school_profile.school_name_override|default:tenant.name }}
                </h2>

                {% if school_profile.school_motto %}
                    <p style="margin: 0 0 2mm 0; font-style: italic; color: #555; font-size: 8pt;">
                        "{{ school_profile.school_motto }}"
                    </p>
                {% endif %}

                <div style="font-size: 8pt; color: #444; line-height: 1.25;">
                    {% if school_profile.address_line_1 %}{{ school_profile.address_line_1 }}<br>{% endif %}
                    {% if school_profile.address_line_2 %}{{ school_profile.address_line_2 }}<br>{% endif %}
                    {% if school_profile.city %}{{ school_profile.city }}{% endif %}{% if school_profile.city and school_profile.state_province %}, {% endif %}{% if school_profile.state_province %}{{ school_profile.state_province }}{% endif %} {% if school_profile.postal_code %}{{ school_profile.postal_code }}{% endif %}
                    {% if school_profile.country_name %}<br>{{ school_profile.country_name }}{% endif %}
                    {% if school_profile.phone_number %}<br><strong>P:</strong> {{ school_profile.phone_number }}{% endif %}
                    {% if school_profile.school_email %}<br><strong>E:</strong> {{ school_profile.school_email }}{% endif %}
                </div>

            </td>
            <td style="width: 45%; text-align: right; vertical-align: top;"> {# Invoice Info Column #}
                <h1 style="margin:0 0 8px 0; font-size:22pt; color: #0056b3; text-transform: uppercase; letter-spacing: 0.5px;">INVOICE</h1>
                <div style="background-color: #e7f3fe; padding: 2mm 3mm; border-left: 1mm solid #007bff; font-size: 9pt; display: inline-block; text-align: left;">
                    <p style="margin:0.5mm 0; font-weight: bold;">Invoice No: {{ invoice.invoice_number_display|default:invoice.pk }}</p>
                    <p style="margin:0.5mm 0;">Status: <strong style="text-transform: capitalize;">{% invoice_status_badge invoice.status plain_text=True %}</strong></p>
                    <p style="margin:0.5mm 0;">Issue Date: {{ invoice.issue_date|date:"F d, Y" }}</p>
                    {% if invoice.due_date %}<p style="margin:0.5mm 0;">Due Date: <strong class="warning-text">{{ invoice.due_date|date:"F d, Y" }}</strong></p>{% endif %}
                </div>
            </td>
        </tr>
    </table>
    <hr style="margin-top: 1.5mm; margin-bottom: 1.5mm; border: none; border-top: 0.5px solid #333;">
{% endblock pdf_header_content %}


{% block pdf_extra_styles %}
    {{ block.super }}
    <style type="text/css">
        /* --- INVOICE SPECIFIC STYLES --- */
        .address-box { padding: 2.5mm; font-size: 8.5pt; line-height: 1.35; vertical-align: top; }
        .bill-to-box { background-color: #f8f9fa; border: 0.5px solid #dee2e6; border-left: 2px solid #007bff; }
        .bill-to-box h4, .period-info-box h4 { margin: 0 0 1.5mm 0; color: #0056b3; font-size: 9.5pt; font-weight: bold; padding-bottom: 1mm; border-bottom: 0.5px solid #cce5ff; }
        .period-info-box { text-align: right; }
        .period-info-box p { margin: 0.8mm 0; }

        /* === Items Table - with refinements for stability === */
        .items-table {
            table-layout: fixed; /* CRITICAL: Force fixed table layout to respect column widths */
            width: 100%;
            border-collapse: collapse;
        }
        .items-table th, .items-table td {
             box-sizing: border-box; /* BEST PRACTICE: Include padding/border in width calculation */
        }
        .items-table thead th {
            background-color: #007bff;
            color: white;
            font-size: 9pt;
            font-weight: bold;
            padding: 2.5mm 3mm;
            border: 0.5px solid #0056b3;
            text-align: left;
        }
        .items-table tbody td {
            padding: 2.5mm 3mm;
            border: 0.5px solid #e9ecef;
            font-size: 9pt;
            vertical-align: top;
            line-height: 1.3;
        }
        .items-table .item-description {
             word-wrap: break-word; /* CRITICAL: Force long words without spaces to break and wrap */
        }
        .items-table .item-description strong { color: #2c3e50; }
        .items-table .item-description small { color: #7f8c8d; font-style: italic; font-size: 8pt; line-height: 1.2; }
        .items-table td.text-center, .items-table td.text-right { white-space: nowrap; }
        .items-table tbody tr:nth-child(even) td { background-color: #f7faff; }
        .items-table .discount-row td { color: #096dd9; font-style: italic; background-color: #f0f9ff; }
        .items-table .total-amount-cell { font-weight: 500; }

        /* === Summary Table & Other Styles --- */
        .summary-table { width: 48%; margin-left: 52%; border: 1px solid #007bff; margin-top: 5mm; font-size: 8.5pt; border-collapse: collapse; }
        .summary-table td { padding: 1.5mm 2.5mm; border-top: 0.5px solid #ecf0f1; }
        .summary-table .label { font-weight: 600; text-align: right; background-color: #f0f7ff; color: #004085; width: 60%; }
        .summary-table .value { text-align: right; width: 40%; font-family: 'Menlo', 'Consolas', 'Courier New', monospace; }
        .summary-table tr.grand-total td { background-color: #0056b3; color: white; font-weight: bold; font-size: 10pt; padding: 1.8mm 2.5mm; }
        .summary-table tr.net-billable td { border-top: 0.75px solid #7f8c8d; font-weight: bold; font-size: 9pt; }
        .summary-table .paid-amount td { color: #228b22; font-weight: 500; font-size: 8.5pt; }
        .summary-table .discount-amount td { color: #007bff; font-size: 8.5pt; }
        .notes-section-box { margin-top: 6mm; padding: 2.5mm; background-color: #f9f9f9; border: 0.5px solid #e0e0e0; border-left: 2px solid #17a2b8; font-size: 7.5pt; line-height: 1.3; }
        .notes-section-box h4 { margin: 0 0 1.5mm 0; color: #117a8b; font-size: 9pt; font-weight:bold; }
        .notes-section-box div, .notes-section-box p { margin-bottom: 0; }
        .notes-section-box div p { margin-bottom: 0.8mm; }
        .final-message-section { margin-top: 6mm; padding-top: 3mm; border-top: 0.75px solid #007bff; text-align: center; font-size: 8pt; color: #495057; }
        .final-message-section p { margin: 0.8mm 0; }
        .final-message-section .due-date-emphasis { font-weight: bold; color: #c0392b; }
    </style>
{% endblock %}

{% block pdf_main_content %}
    <!-- Bill To & Academic Period Table -->
    <table class="no-border" style="margin-bottom: 7mm; width: 100%;">
        <tr>
            <td style="width: 55%;" class="address-box bill-to-box">
                <h4>Bill To</h4>
                {% if invoice.student %}
                    <p style="font-weight: bold; font-size: 10pt; color: #2c3e50;">{{ invoice.student.get_full_name }}</p>
                    {% if invoice.student.admission_number %}<p>Adm. No: {{ invoice.student.admission_number }}</p>{% endif %}
                    {% if invoice.student.current_class %}
                        <p>Class: {{ invoice.student.current_class.name }}{% if invoice.student.current_section %} - {{ invoice.student.current_section.name }}{% endif %}</p>
                    {% endif %}
                    {% with primary_parent=invoice.student.get_primary_parent %}
                        {% if primary_parent %}
                            <div style="margin-top: 2mm; padding-top: 1.5mm; border-top: 0.5px dotted #bdc3c7;">
                                <p class="muted" style="margin-bottom:1mm; font-size:8pt;">Parent/Guardian:</p>
                                <p><strong>{{ primary_parent.get_full_name }}</strong></p>
                                {% if primary_parent.phone_number %}<p>Phone: {{ primary_parent.phone_number }}</p>{% endif %}
                            </div>
                        {% endif %}
                    {% endwith %}
                {% else %}
                    <p class="warning-text italic">Student information missing.</p>
                {% endif %}
            </td>
            <td style="width: 45%; vertical-align: top;" class="address-box period-info-box">
                <h4>Period Details</h4>
                {% if invoice.academic_year %}<p><strong>Academic Year:</strong> {{ invoice.academic_year.name }}</p>{% endif %}
                {% if invoice.term %}<p><strong>Term:</strong> {{ invoice.term.name }}</p>{% endif %}
                {% if invoice.fee_structure_used %}<p><strong>Fee Structure:</strong> {{ invoice.fee_structure_used.name }}</p>{% endif %}
            </td>
        </tr>
    </table>

    <h3 class="section-title">Invoice Breakdown</h3>
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 35%;">Item / Description</th> {# Adjusted Width #}
                <th class="text-right" style="width: 30%;">Unit Price</th>
                <th class="text-center" style="width: 15%;">Qty</th>
                <th class="text-right" style="width: 20%;">Total</th> {# Adjusted Width #}
            </tr>
        </thead>
        <tbody>
            {% for item in charge_items %}
            <tr>
                <td class="item-description">
                    <strong>{% if item.fee_head %}{{ item.fee_head.name }}{% else %}{{ item.description }}{% endif %}</strong>
                    {% if item.description and item.fee_head and item.description|lower != item.fee_head.name|lower %}<br><small>{{ item.description }}</small>{% endif %}
                </td>
                <td class="text-right">{{ school_profile.currency_symbol|default:'$' }}{{ item.unit_price|floatformat:2|intcomma }}</td>
                <td class="text-center">{{ item.quantity|floatformat:0 }}</td>
                <td class="text-right total-amount-cell">{{ school_profile.currency_symbol|default:'$' }}{{ item.amount|floatformat:2|intcomma }}</td>
            </tr>
            {% endfor %}

            {% for item in concession_lines %}
            <tr class="discount-row">
                <td class="item-description">
                    <strong>{% if item.concession_type %}{{ item.concession_type.name }}{% else %}{{ item.description }}{% endif %}</strong>
                    <em class="muted">(Discount)</em>
                    {% if item.description and item.concession_type and item.description|lower != item.concession_type.name|lower %}<br><small>{{ item.description }}</small>{% endif %}
                </td>
                <td class="text-right"></td>
                <td class="text-center">-</td>
                <td class="text-right total-amount-cell">({{ school_profile.currency_symbol|default:'$' }}{{ item.amount|abs|floatformat:2|intcomma }})</td>
            </tr>
            {% endfor %}

            {% if not charge_items and not concession_lines %}
                <tr><td colspan="4" class="text-center italic" style="padding: 4mm; color: #7f8c8d;">No items found for this invoice.</td></tr>
            {% endif %}
        </tbody>
    </table>

    <table class="summary-table">
        <tr>
            <td class="label">Subtotal:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_subtotal|floatformat:2|intcomma }}</td>
        </tr>
        {% if display_total_concessions > 0 %}
        <tr class="discount-amount">
            <td class="label">Total Discounts:</td>
            <td class="value">- {{ school_profile.currency_symbol|default:'$' }}{{ display_total_concessions|floatformat:2|intcomma }}</td>
        </tr>
        <tr class="net-billable">
            <td class="label">Net Billable Amount:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_net_billable|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}
        {% if display_amount_paid > 0 %}
        <tr class="paid-amount">
            <td class="label">Amount Paid:</td>
            <td class="value">- {{ school_profile.currency_symbol|default:'$' }}{{ display_amount_paid|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}
        <tr class="grand-total">
            <td class="label">BALANCE DUE:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_balance_due|floatformat:2|intcomma }}</td>
        </tr>
    </table>

    {% if invoice.notes_to_parent %}
        <div class="notes-section-box">
            <h4>Important Notes</h4>
            <div>{{ invoice.notes_to_parent|linebreaksbr }}</div>
        </div>
    {% endif %}

    {% if school_profile.payment_instructions %}
        <div class="notes-section-box" style="border-left-color: #f39c12; margin-top:5mm;">
            <h4 style="color: #d35400;">Payment Instructions</h4>
            <div>{{ school_profile.payment_instructions|linebreaksbr }}</div>
        </div>
    {% endif %}

    <div class="final-message-section">
        <p>For any inquiries regarding this invoice, please contact the school accounts office.</p>
        {% if invoice.due_date and invoice.balance_due > 0 %}
            <p class="due-date-emphasis">Kindly settle the outstanding balance by {{ invoice.due_date|date:"F d, Y" }}.</p>
        {% endif %}
        <p style="margin-top: 7mm; font-weight: bold;">Thank you!</p>
    </div>
{% endblock pdf_main_content %}

{% block pdf_footer_content %}
    {# Override footer from _pdf_base.html for invoice-specific footer #}
    <p>{{ school_profile.school_name_on_reports|default:tenant.name }} - Generated: {% now "F d, Y H:i" %}</p>
    <p class="page-number"></p> {# CSS in _pdf_base.html handles 'Page X of Y' via ::after #}
    {% if school_profile.website %}<p style="font-size:7pt;">{{ school_profile.website }}</p>{% endif %}
{% endblock pdf_footer_content %}



 {% endcomment %}






















{% comment %} {# D:\school_fees_saas_v2\apps\fees\templates\fees\pdf\invoice_pdf_template.html #}
{% extends "reporting/pdf/_pdf_base.html" %}
{% load static humanize fees_tags core_tags math_filters %}

{% block pdf_title %}Invoice #{{ invoice.invoice_number_display|default:invoice.pk }} - {{ school_profile.school_name_on_reports|default:tenant.name }}{% endblock %}

{# This block OVERRIDES pdf_header_content from _pdf_base.html to provide INVOICE-SPECIFIC header #}
{% block pdf_header_content %}
    <table style="width: 100%; border-collapse: collapse; vertical-align: top;">
        <tr>
            <td style="width: 55%; padding-right: 10mm; vertical-align: top;"> {# School Info Column #}
                {% if school_profile.logo and school_profile.logo.path %}
                    <img src="{{ school_profile.logo.path }}" style="max-height: 16mm; max-width: 45mm; margin-bottom: 3mm;" alt="School Logo">
                {% endif %}

                <h2 style="margin: 0 0 2mm 0; font-size: 14pt; color: #2c3e50; font-weight:bold;">
                    {{ school_profile.school_name_on_reports|default:school_profile.school_name_override|default:tenant.name }}
                </h2>

                {% if school_profile.school_motto %}
                    <p style="margin: 0 0 2mm 0; font-style: italic; color: #555; font-size: 8pt;">
                        "{{ school_profile.school_motto }}"
                    </p>
                {% endif %}

                <div style="font-size: 8pt; color: #444; line-height: 1.25;">
                    {% if school_profile.address_line_1 %}{{ school_profile.address_line_1 }}<br>{% endif %}
                    {% if school_profile.address_line_2 %}{{ school_profile.address_line_2 }}<br>{% endif %}
                    {% if school_profile.city %}{{ school_profile.city }}{% endif %}{% if school_profile.city and school_profile.state_province %}, {% endif %}{% if school_profile.state_province %}{{ school_profile.state_province }}{% endif %} {% if school_profile.postal_code %}{{ school_profile.postal_code }}{% endif %}
                    {% if school_profile.country_name %}<br>{{ school_profile.country_name }}{% endif %}
                    {% if school_profile.phone_number %}<br><strong>P:</strong> {{ school_profile.phone_number }}{% endif %}
                    {% if school_profile.school_email %}<br><strong>E:</strong> {{ school_profile.school_email }}{% endif %}
                </div>

            </td>
            <td style="width: 45%; text-align: right; vertical-align: top;"> {# Invoice Info Column #}
                <h1 style="margin:0 0 8px 0; font-size:22pt; color: #0056b3; text-transform: uppercase; letter-spacing: 0.5px;">INVOICE</h1>
                <div style="background-color: #e7f3fe; padding: 2mm 3mm; border-left: 1mm solid #007bff; font-size: 9pt; display: inline-block; text-align: left;">
                    <p style="margin:0.5mm 0; font-weight: bold;">Invoice No: {{ invoice.invoice_number_display|default:invoice.pk }}</p>
                    <p style="margin:0.5mm 0;">Status: <strong style="text-transform: capitalize;">{% invoice_status_badge invoice.status plain_text=True %}</strong></p>
                    <p style="margin:0.5mm 0;">Issue Date: {{ invoice.issue_date|date:"F d, Y" }}</p>
                    {% if invoice.due_date %}<p style="margin:0.5mm 0;">Due Date: <strong class="warning-text">{{ invoice.due_date|date:"F d, Y" }}</strong></p>{% endif %}
                </div>
            </td>

        </tr>
    </table>
    <hr style="margin-top: 1.5mm; margin-bottom: 1.5mm; border: none; border-top: 0.5px solid #333;">
{% endblock pdf_header_content %}


{% block pdf_extra_styles %}
    {{ block.super }}
    <style type="text/css">
        /* --- INVOICE SPECIFIC STYLES --- */

        /* Bill To & Period Info Sections */
        .address-box { 
            padding: 2.5mm; /* Slightly reduced padding */
            font-size: 8.5pt; /* Slightly reduced font */
            line-height: 1.35; 
            vertical-align: top; 
        }
        .bill-to-box { 
            background-color: #f8f9fa; 
            border: 0.5px solid #dee2e6; 
            border-left: 2px solid #007bff; 
        }
        .bill-to-box h4, .period-info-box h4 {
            margin: 0 0 1.5mm 0; /* Reduced bottom margin */
            color: #0056b3; 
            font-size: 9.5pt; /* Slightly reduced */
            font-weight: bold;
            padding-bottom: 1mm; 
            border-bottom: 0.5px solid #cce5ff;
        }
        .period-info-box { text-align: right; }
        .period-info-box p { margin: 0.8mm 0; } /* Reduced margin */

        /* === Items Table === */
        .items-table {
            table-layout: fixed; /* Force fixed table layout to respect column widths */
            width: 100%;
        }
        .items-table thead th {
            background-color: #007bff;
            color: white;
            font-size: 9.5pt;  /* Increased font size */
            font-weight: bold;
            padding: 2.5mm 3mm; /* Increased padding */
            border: 0.5px solid #0056b3;
            text-align: left;
        }
        /* Ensure alignment classes for th are inherited from _pdf_base.html or defined here if needed */
        /* .items-table thead th.text-center { text-align: center; } */
        /* .items-table thead th.text-right { text-align: right; } */

        .items-table tbody td {
            padding: 2mm 3mm; /* Increased padding */
            border: 0.5px solid #e9ecef; /* Lighter border */
            font-size: 9pt;   /* Increased font size */
            vertical-align: top;
            line-height: 1.3; /* Better line height */
        }
        .items-table tbody tr:nth-child(even) td { background-color: #f7faff; }
        .items-table .item-description strong { color: #2c3e50; }
        .items-table .item-description small {
            color: #7f8c8d;
            font-style: italic;
            font-size: 8pt; /* Increased font size */
            line-height: 1.2;
        }
        .items-table .discount-row td { 
            color: #096dd9; 
            font-style: italic; 
            background-color: #f0f9ff; /* Keep if desired */
        }
        .items-table .total-amount-cell { font-weight: 500; /* text-align: right; should be handled by th/td class */ }

        /* Prevent text wrapping in numeric columns */
        .items-table td.text-center,
        .items-table td.text-right {
            white-space: nowrap;
        }

        /* === Summary Table - REDUCED ROW HEIGHT === */
        .summary-table { 
            width: 48%; 
            margin-left: 52%; 
            border: 1px solid #007bff; 
            margin-top: 5mm; /* REDUCED margin-top */
            font-size: 8.5pt; /* REDUCED base font for summary */
            border-collapse: collapse; 
        }
        .summary-table td {
            padding: 1.5mm 2.5mm; /* REDUCED padding significantly */
            border-top: 0.5px solid #ecf0f1; 
        }
        .summary-table .label {
            font-weight: 600; text-align: right; background-color: #f0f7ff; 
            color: #004085; width: 60%; 
        }
        .summary-table .value {
            text-align: right; width: 40%;
            font-family: 'Menlo', 'Consolas', 'Courier New', monospace;
        }
        .summary-table tr.grand-total td { /* BALANCE DUE row */
            background-color: #0056b3; color: white; font-weight: bold;
            font-size: 10pt;    /* REDUCED font size */
            padding: 1.8mm 2.5mm; /* REDUCED padding */
        }
        .summary-table tr.net-billable td {
             border-top: 0.75px solid #7f8c8d; font-weight: bold; font-size: 9pt; /* REDUCED */
        }
        .summary-table .paid-amount td { color: #228b22; font-weight: 500; font-size: 8.5pt; } /* REDUCED */
        .summary-table .discount-amount td { color: #007bff; font-size: 8.5pt; } /* REDUCED */

        /* Notes & Instructions */
        .notes-section-box { 
            margin-top: 6mm; /* REDUCED margin */
            padding: 2.5mm;   /* REDUCED padding */
            background-color: #f9f9f9; 
            border: 0.5px solid #e0e0e0; 
            border-left: 2px solid #17a2b8; 
            font-size: 7.5pt; /* REDUCED font size */
            line-height: 1.3; 
        }
        .notes-section-box h4 { 
            margin: 0 0 1.5mm 0; /* REDUCED margin */
            color: #117a8b; 
            font-size: 9pt; /* REDUCED font size */
            font-weight:bold; 
        }
        .notes-section-box div p, .notes-section-box p { margin-bottom: 0.8mm; } /* REDUCED margin */
        
        .final-message-section { 
            margin-top: 6mm; /* REDUCED margin */
            padding-top: 3mm; /* REDUCED padding */
            border-top: 0.75px solid #007bff; 
            text-align: center; 
            font-size: 8pt; /* REDUCED font size */
            color: #495057; 
        }
        .final-message-section p { margin: 0.8mm 0; } /* REDUCED margin */
        .final-message-section .due-date-emphasis { font-weight: bold; color: #c0392b; }
    </style>
{% endblock %}


{% block pdf_main_content %}
    <!-- Bill To & Academic Period Table -->
    <table class="no-border" style="margin-bottom: 7mm; width: 100%;">
        <tr>
            <td style="width: 55%;" class="address-box bill-to-box">
                <h4>Bill To</h4>
                {% if invoice.student %}
                    <p style="font-weight: bold; font-size: 10pt; color: #2c3e50;">{{ invoice.student.get_full_name }}</p>
                    {% if invoice.student.admission_number %}<p>Adm. No: {{ invoice.student.admission_number }}</p>{% endif %}
                    {% if invoice.student.current_class %}
                        <p>Class: {{ invoice.student.current_class.name }}{% if invoice.student.current_section %} - {{ invoice.student.current_section.name }}{% endif %}</p>
                    {% endif %}
                    {% with primary_parent=invoice.student.get_primary_parent %}
                        {% if primary_parent %}
                            <div style="margin-top: 2mm; padding-top: 1.5mm; border-top: 0.5px dotted #bdc3c7;">
                                <p class="muted" style="margin-bottom:1mm; font-size:8pt;">Parent/Guardian:</p>
                                <p><strong>{{ primary_parent.get_full_name }}</strong></p>
                                {% if primary_parent.phone_number %}<p>Phone: {{ primary_parent.phone_number }}</p>{% endif %}
                            </div>
                        {% endif %}
                    {% endwith %}
                {% else %}
                    <p class="warning-text italic">Student information missing.</p>
                {% endif %}
            </td>
            <td style="width: 45%; vertical-align: top;" class="address-box period-info-box">
                <h4>Period Details</h4>
                {% if invoice.academic_year %}<p><strong>Academic Year:</strong> {{ invoice.academic_year.name }}</p>{% endif %}
                {% if invoice.term %}<p><strong>Term:</strong> {{ invoice.term.name }}</p>{% endif %}
                {% if invoice.fee_structure_used %}<p><strong>Fee Structure:</strong> {{ invoice.fee_structure_used.name }}</p>{% endif %}
            </td>
        </tr>
    </table>

    <h3 class="section-title">Invoice Breakdown</h3>
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 35%;">Item / Description</th>
                <th class="text-center" style="width: 15%;">Qty</th>
                <th class="text-right" style="width: 35%;">Unit Price</th>
                <th class="text-right" style="width: 15%;">Total</th>
            </tr>
        </thead>
        <tbody>
            {% for item in charge_items %}
            <tr>
                <td class="item-description">
                    <strong>{% if item.fee_head %}{{ item.fee_head.name }}{% else %}{{ item.description }}{% endif %}</strong>
                    {% if item.description and item.fee_head and item.description|lower != item.fee_head.name|lower %}<br><small>{{ item.description }}</small>{% endif %}
                </td>
                <td class="text-center">{{ item.quantity|floatformat:0 }}</td>
                <td class="text-right">{{ school_profile.currency_symbol|default:'$' }}{{ item.unit_price|floatformat:2|intcomma }}</td>
                <td class="text-right total-amount-cell">{{ school_profile.currency_symbol|default:'$' }}{{ item.amount|floatformat:2|intcomma }}</td>
            </tr>
            {% endfor %}

            {% for item in concession_lines %}
            <tr class="discount-row">
                <td class="item-description">
                    <strong>{% if item.concession_type %}{{ item.concession_type.name }}{% else %}{{ item.description }}{% endif %}</strong>
                    <em class="muted">(Discount)</em>
                    {% if item.description and item.concession_type and item.description|lower != item.concession_type.name|lower %}<br><small>{{ item.description }}</small>{% endif %}
                </td>
                <td class="text-center">-</td>
                <td class="text-right"></td>
                <td class="text-right total-amount-cell">({{ school_profile.currency_symbol|default:'$' }}{{ item.amount|abs|floatformat:2|intcomma }})</td>
            </tr>
            {% endfor %}

            {% if not charge_items and not concession_lines %}
                <tr><td colspan="4" class="text-center" style="padding: 4mm; color: #7f8c8d;" class="italic">No items found for this invoice.</td></tr>
            {% endif %}
        </tbody>
    </table>

    <table class="summary-table">
        <tr>
            <td class="label">Subtotal:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_subtotal|floatformat:2|intcomma }}</td>
        </tr>
        {% if display_total_concessions > 0 %}
        <tr class="discount-amount">
            <td class="label">Total Discounts:</td>
            <td class="value">- {{ school_profile.currency_symbol|default:'$' }}{{ display_total_concessions|floatformat:2|intcomma }}</td>
        </tr>
        <tr class="net-billable">
            <td class="label">Net Billable Amount:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_net_billable|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}
        {% if display_amount_paid > 0 %}
        <tr class="paid-amount">
            <td class="label">Amount Paid:</td>
            <td class="value">- {{ school_profile.currency_symbol|default:'$' }}{{ display_amount_paid|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}
        <tr class="grand-total">
            <td class="label">BALANCE DUE:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_balance_due|floatformat:2|intcomma }}</td>
        </tr>
    </table>

    {% if invoice.notes_to_parent %}
        <div class="notes-section-box">
            <h4>Important Notes</h4>
            <div>{{ invoice.notes_to_parent|linebreaksbr }}</div>
        </div>
    {% endif %}

    {% if school_profile.payment_instructions %}
        <div class="notes-section-box" style="border-left-color: #f39c12; margin-top:5mm;">
            <h4 style="color: #d35400;">Payment Instructions</h4>
            <div>{{ school_profile.payment_instructions|linebreaksbr }}</div>
        </div>
    {% endif %}

    <div class="final-message-section">
        <p>For any inquiries regarding this invoice, please contact the school accounts office.</p>
        {% if invoice.due_date and invoice.balance_due > 0 %}
            <p class="due-date-emphasis">Kindly settle the outstanding balance by {{ invoice.due_date|date:"F d, Y" }}.</p>
        {% endif %}
        <p style="margin-top: 7mm; font-weight: bold;">Thank you!</p>
    </div>
{% endblock pdf_main_content %}

{% block pdf_footer_content %}
    {# Override footer from _pdf_base.html for invoice-specific footer #}
    <p>{{ school_profile.school_name_on_reports|default:tenant.name }} - Generated: {% now "F d, Y H:i" %}</p>
    <p class="page-number"></p> {# CSS in _pdf_base.html handles 'Page X of Y' via ::after #}
    {% if school_profile.website %}<p style="font-size:7pt;">{{ school_profile.website }}</p>{% endif %}
{% endblock pdf_footer_content %}
















 {% endcomment %}
