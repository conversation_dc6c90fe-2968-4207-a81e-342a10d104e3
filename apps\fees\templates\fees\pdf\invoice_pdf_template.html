{% extends "reporting/pdf/_pdf_base.html" %}

{% load static humanize mptt_tags core_tags math_filters %} {# Ensure 'humanize' is loaded #}

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Invoice #{{ invoice.invoice_number_display }} - {{ school_profile.school_name_on_reports|default:tenant.name }}</title>
    <style>
        @page {
            size: A4;
            margin: 1cm;
        }
        body {
            font-family: Arial, sans-serif;
            font-size: 10pt;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 24pt;
        }
        .school-info {
            font-size: 9pt;
            margin-top: 10px;
        }
        .invoice-info {
            background-color: #f8f9fa;
            padding: 10px;
            border: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        .invoice-info table {
            width: 100%;
            border-collapse: collapse;
        }
        .invoice-info td {
            padding: 3px 0;
            font-size: 10pt;
        }
        .bill-to {
            background-color: #f8f9fa;
            padding: 15px;
            border: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        .bill-to h3 {
            margin: 0 0 10px 0;
            color: #007bff;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .items-table th {
            background-color: #007bff;
            color: white;
            padding: 10px 8px;
            text-align: left;
            border: 1px solid #007bff;
        }
        .items-table td {
            padding: 8px;
            border: 1px solid #dee2e6;
        }
        .items-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .summary-table {
            width: 50%;
            margin-left: 50%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .summary-table td {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
        }
        .summary-table .label {
            font-weight: bold;
            text-align: right;
            background-color: #f8f9fa;
        }
        .summary-table .value {
            text-align: right;
        }
        .grand-total {
            background-color: #28a745;
            color: white;
            font-weight: bold;
            font-size: 12pt;
        }
        .notes {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #17a2b8;
        }
        .footer {
            margin-top: 30px;
            padding-top: 15px;
            border-top: 2px solid #007bff;
            text-align: center;
            font-size: 8pt;
            color: #6c757d;
        }
    </style>
</head>
<body>

    <!-- Header Section -->
    <div class="header">
        <h1>INVOICE</h1>
        {% if school_profile %}
            <div class="school-info">
                <strong>{{ school_profile.school_name_on_reports|default:tenant.name }}</strong><br>
                {% if school_profile.address_line_1 %}{{ school_profile.address_line_1 }}<br>{% endif %}
                {% if school_profile.address_line_2 %}{{ school_profile.address_line_2 }}<br>{% endif %}
                {% if school_profile.city %}{{ school_profile.city }}{% endif %}{% if school_profile.city and school_profile.state_province %}, {% endif %}{% if school_profile.state_province %}{{ school_profile.state_province }}{% endif %} {% if school_profile.postal_code %}{{ school_profile.postal_code }}{% endif %}<br>
                {% if school_profile.phone_number %}Phone: {{ school_profile.phone_number }}{% endif %}
                {% if school_profile.school_email %} | Email: {{ school_profile.school_email }}{% endif %}
            </div>
        {% else %}
            <div class="school-info">
                <strong>{{ tenant.name }}</strong>
            </div>
        {% endif %}
    </div>

    <!-- Invoice Information -->
    <div class="invoice-info">
        <table>
            <tr>
                <td style="width: 50%;"><strong>Invoice Number:</strong> {{ invoice.invoice_number_display }}</td>
                <td style="width: 50%;"><strong>Status:</strong> {{ invoice.get_status_display }}</td>
            </tr>
            <tr>
                <td><strong>Issue Date:</strong> {{ invoice.issue_date|date:"F d, Y" }}</td>
                <td><strong>Due Date:</strong> {% if invoice.due_date %}{{ invoice.due_date|date:"F d, Y" }}{% else %}N/A{% endif %}</td>
            </tr>
            {% if invoice.academic_year or invoice.term %}
            <tr>
                <td>{% if invoice.academic_year %}<strong>Academic Year:</strong> {{ invoice.academic_year.name }}{% endif %}</td>
                <td>{% if invoice.term %}<strong>Term:</strong> {{ invoice.term.name }}{% endif %}</td>
            </tr>
            {% endif %}
        </table>
    </div>

    <!-- Bill To Section -->
    <div class="bill-to">
        <h3>Bill To</h3>
        {% if invoice.student %}
            <p style="font-weight: bold; font-size: 11pt; color: #007bff; margin: 0 0 5px 0;">{{ invoice.student.get_full_name }}</p>
            {% if invoice.student.admission_number %}
                <p style="margin: 3px 0;"><strong>Admission No:</strong> {{ invoice.student.admission_number }}</p>
            {% endif %}
            {% if invoice.student.current_class %}
                <p style="margin: 3px 0;"><strong>Class:</strong> {{ invoice.student.current_class.name }}{% if invoice.student.current_section %} - {{ invoice.student.current_section.name }}{% endif %}</p>
            {% endif %}
            {% with primary_parent=invoice.student.get_primary_parent %}
                {% if primary_parent %}
                    <p style="margin: 8px 0 3px 0; padding-top: 8px; border-top: 1px solid #dee2e6;">
                        <strong>Parent/Guardian:</strong> {{ primary_parent.get_full_name }}
                    </p>
                    {% if primary_parent.phone_number %}
                        <p style="margin: 3px 0;"><strong>Phone:</strong> {{ primary_parent.phone_number }}</p>
                    {% endif %}
                    {% if primary_parent.email %}
                        <p style="margin: 3px 0;"><strong>Email:</strong> {{ primary_parent.email }}</p>
                    {% endif %}
                {% endif %}
            {% endwith %}
        {% else %}
            <p style="color: #dc3545;">Student information not available</p>
        {% endif %}
    </div>

    <!-- Invoice Items Table -->
    <h3 style="color: #495057; margin-bottom: 10px;">Invoice Items</h3>
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 45%;">Description</th>
                <th class="text-center" style="width: 12%;">Quantity</th>
                <th class="text-right" style="width: 18%;">Unit Price</th>
                <th class="text-right" style="width: 25%;">Line Total</th>
            </tr>
        </thead>
        <tbody>
            {% for item in charge_items %}
            <tr>
                <td>
                    <strong>
                        {% if item.fee_head %}
                            {{ item.fee_head.name }}
                        {% else %}
                            {{ item.description }}
                        {% endif %}
                    </strong>
                    {% if item.description and item.fee_head and item.description|lower != item.fee_head.name|lower %}
                        <br><small style="color: #6c757d; font-style: italic;">{{ item.description }}</small>
                    {% endif %}
                </td>
                <td class="text-center">{{ item.quantity|floatformat:"-2"|default:"1" }}</td>
                <td class="text-right">{{ school_profile.currency_symbol|default:'$' }}{{ item.unit_price|floatformat:2|intcomma }}</td>
                <td class="text-right" style="font-weight: bold;">{{ school_profile.currency_symbol|default:'$' }}{{ item.amount|floatformat:2|intcomma }}</td>
            </tr>
            {% endfor %}

            {% if concession_lines %}
                {% for item in concession_lines %}
                <tr style="background-color: #f0f9ff; color: #0c5460;">
                    <td>
                        <strong>
                            {% if item.concession_type %}
                                {{ item.concession_type.name }}
                            {% else %}
                                {{ item.description }}
                            {% endif %}
                        </strong>
                        <em> (Discount)</em>
                        {% if item.description and item.concession_type and item.description|lower != item.concession_type.name|lower %}
                            <br><small style="font-style: italic;">{{ item.description }}</small>
                        {% endif %}
                    </td>
                    <td class="text-center">-</td>
                    <td class="text-right"></td>
                    <td class="text-right" style="font-weight: bold;">
                        ({{ school_profile.currency_symbol|default:'$' }}{{ item.amount|abs|floatformat:2|intcomma }})
                    </td>
                </tr>
                {% endfor %}
            {% endif %}

            {% if not charge_items and not concession_lines %}
                <tr>
                    <td colspan="4" class="text-center" style="padding: 20px; color: #6c757d; font-style: italic;">
                        No billable items or concessions on this invoice.
                    </td>
                </tr>
            {% endif %}
        </tbody>
    </table>

    <!-- Summary Table -->
    <table class="summary-table">
        <tr>
            <td class="label">Subtotal:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_subtotal|floatformat:2|intcomma }}</td>
        </tr>
        {% if display_total_concessions > 0 %}
        <tr>
            <td class="label">Total Discounts:</td>
            <td class="value" style="color: #17a2b8;">- {{ school_profile.currency_symbol|default:'$' }}{{ display_total_concessions|floatformat:2|intcomma }}</td>
        </tr>
        <tr>
            <td class="label">Net Billable Amount:</td>
            <td class="value" style="font-weight: bold;">{{ school_profile.currency_symbol|default:'$' }}{{ display_net_billable|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}
        {% if display_amount_paid > 0 %}
        <tr style="background-color: #d4edda;">
            <td class="label">Amount Paid:</td>
            <td class="value">- {{ school_profile.currency_symbol|default:'$' }}{{ display_amount_paid|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}
        <tr class="grand-total">
            <td class="label">BALANCE DUE:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_balance_due|floatformat:2|intcomma }}</td>
        </tr>
    </table>

    <!-- Notes Section -->
    {% if invoice.notes_to_parent %}
        <div class="notes">
            <h4 style="margin: 0 0 8px 0; color: #17a2b8;">Important Notes</h4>
            <div style="line-height: 1.5;">
                {{ invoice.notes_to_parent|linebreaksbr }}
            </div>
        </div>
    {% endif %}

    <!-- Payment Instructions -->
    {% if school_profile.payment_instructions %}
        <div style="margin-top: 20px; padding: 15px; background-color: #fff3cd; border: 1px solid #ffeaa7;">
            <h4 style="margin: 0 0 8px 0; color: #856404;">Payment Instructions</h4>
            <div style="line-height: 1.5;">
                {{ school_profile.payment_instructions|linebreaksbr }}
            </div>
        </div>
    {% endif %}

    <!-- Footer -->
    <div class="footer">
        <p style="margin: 0;">
            <strong>{{ school_profile.school_name_on_reports|default:tenant.name }}</strong>
            {% if school_profile.website %} | {{ school_profile.website }}{% endif %}
        </p>
        <p style="margin: 5px 0 0 0;">
            Thank you for your partnership in your child's education.
        </p>
        {% if invoice.due_date %}
            <p style="margin: 5px 0 0 0; font-weight: bold; color: #dc3545;">
                Please ensure payment is made by {{ invoice.due_date|date:"F d, Y" }}
            </p>
        {% endif %}
    </div>

</body>
</html>
