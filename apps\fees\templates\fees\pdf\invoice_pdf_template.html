{# D:\school_fees_saas_v2\apps\fees\templates\fees\pdf\invoice_pdf_template.html #}
{% extends "reporting/pdf/_pdf_base.html" %}
{% load static humanize fees_tags core_tags math_filters %}

{% block pdf_title %}Invoice #{{ invoice.invoice_number_display|default:invoice.pk }} - {{ school_profile.school_name_on_reports|default:tenant.name }}{% endblock %}

{# This block OVERRIDES pdf_header_content from _pdf_base.html to provide INVOICE-SPECIFIC header #}
{% block pdf_header_content %}
    <table style="width: 100%; border-collapse: collapse; vertical-align: top;">
        <tr>
            <td style="width: 55%; padding-right: 10mm; vertical-align: top;"> {# School Info Column #}
                {% if school_profile.logo and school_profile.logo.path %}
                    <img src="{{ school_profile.logo.path }}" style="max-height: 16mm; max-width: 45mm; margin-bottom: 3mm;" alt="School Logo">
                {% endif %}

                <h2 style="margin: 0 0 2mm 0; font-size: 14pt; color: #2c3e50; font-weight:bold;">
                    {{ school_profile.school_name_on_reports|default:school_profile.school_name_override|default:tenant.name }}
                </h2>

                {% if school_profile.school_motto %}
                    <p style="margin: 0 0 2mm 0; font-style: italic; color: #555; font-size: 8pt;">
                        "{{ school_profile.school_motto }}"
                    </p>
                {% endif %}

                <div style="font-size: 8pt; color: #444; line-height: 1.25;">
                    {% if school_profile.address_line_1 %}{{ school_profile.address_line_1 }}<br>{% endif %}
                    {% if school_profile.address_line_2 %}{{ school_profile.address_line_2 }}<br>{% endif %}
                    {% if school_profile.city %}{{ school_profile.city }}{% endif %}{% if school_profile.city and school_profile.state_province %}, {% endif %}{% if school_profile.state_province %}{{ school_profile.state_province }}{% endif %} {% if school_profile.postal_code %}{{ school_profile.postal_code }}{% endif %}
                    {% if school_profile.country_name %}<br>{{ school_profile.country_name }}{% endif %}
                    {% if school_profile.phone_number %}<br><strong>P:</strong> {{ school_profile.phone_number }}{% endif %}
                    {% if school_profile.school_email %}<br><strong>E:</strong> {{ school_profile.school_email }}{% endif %}
                </div>

            </td>
            <td style="width: 45%; text-align: right; vertical-align: top;"> {# Invoice Info Column #}
                <h1 style="margin:0 0 8px 0; font-size:22pt; color: #0056b3; text-transform: uppercase; letter-spacing: 0.5px;">INVOICE</h1>
                <div style="background-color: #e7f3fe; padding: 2mm 3mm; border-left: 1mm solid #007bff; font-size: 9pt; display: inline-block; text-align: left;">
                    <p style="margin:0.5mm 0; font-weight: bold;">Invoice No: {{ invoice.invoice_number_display|default:invoice.pk }}</p>
                    <p style="margin:0.5mm 0;">Status: <strong style="text-transform: capitalize;">{% invoice_status_badge invoice.status plain_text=True %}</strong></p>
                    <p style="margin:0.5mm 0;">Issue Date: {{ invoice.issue_date|date:"F d, Y" }}</p>
                    {% if invoice.due_date %}<p style="margin:0.5mm 0;">Due Date: <strong class="warning-text">{{ invoice.due_date|date:"F d, Y" }}</strong></p>{% endif %}
                </div>
            </td>

        </tr>
    </table>
    <hr style="margin-top: 1.5mm; margin-bottom: 1.5mm; border: none; border-top: 0.5px solid #333;">
{% endblock pdf_header_content %}


{% block pdf_extra_styles %}
    {{ block.super }}
    <style type="text/css">
        /* --- INVOICE SPECIFIC STYLES --- */

        /* Bill To & Period Info Sections */
        .address-box { 
            padding: 2.5mm; /* Slightly reduced padding */
            font-size: 8.5pt; /* Slightly reduced font */
            line-height: 1.35; 
            vertical-align: top; 
        }
        .bill-to-box { 
            background-color: #f8f9fa; 
            border: 0.5px solid #dee2e6; 
            border-left: 2px solid #007bff; 
        }
        .bill-to-box h4, .period-info-box h4 {
            margin: 0 0 1.5mm 0; /* Reduced bottom margin */
            color: #0056b3; 
            font-size: 9.5pt; /* Slightly reduced */
            font-weight: bold;
            padding-bottom: 1mm; 
            border-bottom: 0.5px solid #cce5ff;
        }
        .period-info-box { text-align: right; }
        .period-info-box p { margin: 0.8mm 0; } /* Reduced margin */

        /* === Items Table - REDUCED ROW HEIGHT === */
        .items-table thead th {
            background-color: #007bff; 
            color: white;              
            font-size: 8.5pt;  /* REDUCED font size */
            font-weight: bold;
            padding: 1.5mm 2mm; /* REDUCED vertical padding */    
            border: 0.5px solid #0056b3; 
            text-align: left; 
        }
        /* Ensure alignment classes for th are inherited from _pdf_base.html or defined here if needed */
        /* .items-table thead th.text-center { text-align: center; } */
        /* .items-table thead th.text-right { text-align: right; } */

        .items-table tbody td {
            padding: 1.5mm 2mm; /* REDUCED vertical padding */
            border: 0.5px solid #e9ecef; /* Lighter border */
            font-size: 8pt;   /* REDUCED font size */
            vertical-align: top; 
            line-height: 1.25; /* Slightly tighter line height */
        }
        .items-table tbody tr:nth-child(even) td { background-color: #f7faff; }
        .items-table .item-description strong { color: #2c3e50; }
        .items-table .item-description small { 
            color: #7f8c8d; 
            font-style: italic; 
            font-size: 7pt; /* REDUCED font size */
            line-height: 1.1;
        }
        .items-table .discount-row td { 
            color: #096dd9; 
            font-style: italic; 
            background-color: #f0f9ff; /* Keep if desired */
        }
        .items-table .total-amount-cell { font-weight: 500; /* text-align: right; should be handled by th/td class */ }

        /* === Summary Table - REDUCED ROW HEIGHT === */
        .summary-table { 
            width: 48%; 
            margin-left: 52%; 
            border: 1px solid #007bff; 
            margin-top: 5mm; /* REDUCED margin-top */
            font-size: 8.5pt; /* REDUCED base font for summary */
            border-collapse: collapse; 
        }
        .summary-table td {
            padding: 1.5mm 2.5mm; /* REDUCED padding significantly */
            border-top: 0.5px solid #ecf0f1; 
        }
        .summary-table .label {
            font-weight: 600; text-align: right; background-color: #f0f7ff; 
            color: #004085; width: 60%; 
        }
        .summary-table .value {
            text-align: right; width: 40%;
            font-family: 'Menlo', 'Consolas', 'Courier New', monospace;
        }
        .summary-table tr.grand-total td { /* BALANCE DUE row */
            background-color: #0056b3; color: white; font-weight: bold;
            font-size: 10pt;    /* REDUCED font size */
            padding: 1.8mm 2.5mm; /* REDUCED padding */
        }
        .summary-table tr.net-billable td {
             border-top: 0.75px solid #7f8c8d; font-weight: bold; font-size: 9pt; /* REDUCED */
        }
        .summary-table .paid-amount td { color: #228b22; font-weight: 500; font-size: 8.5pt; } /* REDUCED */
        .summary-table .discount-amount td { color: #007bff; font-size: 8.5pt; } /* REDUCED */

        /* Notes & Instructions */
        .notes-section-box { 
            margin-top: 6mm; /* REDUCED margin */
            padding: 2.5mm;   /* REDUCED padding */
            background-color: #f9f9f9; 
            border: 0.5px solid #e0e0e0; 
            border-left: 2px solid #17a2b8; 
            font-size: 7.5pt; /* REDUCED font size */
            line-height: 1.3; 
        }
        .notes-section-box h4 { 
            margin: 0 0 1.5mm 0; /* REDUCED margin */
            color: #117a8b; 
            font-size: 9pt; /* REDUCED font size */
            font-weight:bold; 
        }
        .notes-section-box div p, .notes-section-box p { margin-bottom: 0.8mm; } /* REDUCED margin */
        
        .final-message-section { 
            margin-top: 6mm; /* REDUCED margin */
            padding-top: 3mm; /* REDUCED padding */
            border-top: 0.75px solid #007bff; 
            text-align: center; 
            font-size: 8pt; /* REDUCED font size */
            color: #495057; 
        }
        .final-message-section p { margin: 0.8mm 0; } /* REDUCED margin */
        .final-message-section .due-date-emphasis { font-weight: bold; color: #c0392b; }
    </style>
{% endblock %}



{% block pdf_main_content %}
    <!-- Bill To & Academic Period Table -->
    <table class="no-border" style="margin-bottom: 7mm; width: 100%;">
        <tr>
            <td style="width: 55%;" class="address-box bill-to-box">
                <h4>Bill To</h4>
                {% if invoice.student %}
                    <p style="font-weight: bold; font-size: 10pt; color: #2c3e50;">{{ invoice.student.get_full_name }}</p>
                    {% if invoice.student.admission_number %}<p>Adm. No: {{ invoice.student.admission_number }}</p>{% endif %}
                    {% if invoice.student.current_class %}
                        <p>Class: {{ invoice.student.current_class.name }}{% if invoice.student.current_section %} - {{ invoice.student.current_section.name }}{% endif %}</p>
                    {% endif %}
                    {% with primary_parent=invoice.student.get_primary_parent %}
                        {% if primary_parent %}
                            <div style="margin-top: 2mm; padding-top: 1.5mm; border-top: 0.5px dotted #bdc3c7;">
                                <p class="muted" style="margin-bottom:1mm; font-size:8pt;">Parent/Guardian:</p>
                                <p><strong>{{ primary_parent.get_full_name }}</strong></p>
                                {% if primary_parent.phone_number %}<p>Phone: {{ primary_parent.phone_number }}</p>{% endif %}
                            </div>
                        {% endif %}
                    {% endwith %}
                {% else %}
                    <p class="warning-text italic">Student information missing.</p>
                {% endif %}
            </td>
            <td style="width: 45%; vertical-align: top;" class="address-box period-info-box">
                <h4>Period Details</h4>
                {% if invoice.academic_year %}<p><strong>Academic Year:</strong> {{ invoice.academic_year.name }}</p>{% endif %}
                {% if invoice.term %}<p><strong>Term:</strong> {{ invoice.term.name }}</p>{% endif %}
                {% if invoice.fee_structure_used %}<p><strong>Fee Structure:</strong> {{ invoice.fee_structure_used.name }}</p>{% endif %}
            </td>
        </tr>
    </table>

    <h3 class="section-title">Invoice Breakdown</h3>
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 45%;">Item / Description</th>
                <th class="text-center" style="width: 10%;">Qty</th>
                <th class="text-right" style="width: 20%;">Unit Price</th>
                <th class="text-right" style="width: 25%;">Total</th>
            </tr>
        </thead>
        <tbody>
            {% for item in charge_items %}
            <tr>
                <td class="item-description">
                    <strong>{% if item.fee_head %}{{ item.fee_head.name }}{% else %}{{ item.description }}{% endif %}</strong>
                    {% if item.description and item.fee_head and item.description|lower != item.fee_head.name|lower %}<br><small>{{ item.description }}</small>{% endif %}
                </td>
                <td class="text-center">{{ item.quantity|floatformat:"-2"|default:"1" }}</td>
                <td class="text-right">{{ school_profile.currency_symbol|default:'$' }}{{ item.unit_price|floatformat:2|intcomma }}</td>
                <td class="text-right total-amount-cell">{{ school_profile.currency_symbol|default:'$' }}{{ item.amount|floatformat:2|intcomma }}</td>
            </tr>
            {% endfor %}

            {% for item in concession_lines %}
            <tr class="discount-row">
                <td class="item-description">
                    <strong>{% if item.concession_type %}{{ item.concession_type.name }}{% else %}{{ item.description }}{% endif %}</strong>
                    <em class="muted">(Discount)</em>
                    {% if item.description and item.concession_type and item.description|lower != item.concession_type.name|lower %}<br><small>{{ item.description }}</small>{% endif %}
                </td>
                <td class="text-center">-</td>
                <td class="text-right"></td>
                <td class="text-right total-amount-cell">({{ school_profile.currency_symbol|default:'$' }}{{ item.amount|abs|floatformat:2|intcomma }})</td>
            </tr>
            {% endfor %}

            {% if not charge_items and not concession_lines %}
                <tr><td colspan="4" class="text-center" style="padding: 4mm; color: #7f8c8d;" class="italic">No items found for this invoice.</td></tr>
            {% endif %}
        </tbody>
    </table>

    <table class="summary-table">
        <tr>
            <td class="label">Subtotal:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_subtotal|floatformat:2|intcomma }}</td>
        </tr>
        {% if display_total_concessions > 0 %}
        <tr class="discount-amount">
            <td class="label">Total Discounts:</td>
            <td class="value">- {{ school_profile.currency_symbol|default:'$' }}{{ display_total_concessions|floatformat:2|intcomma }}</td>
        </tr>
        <tr class="net-billable">
            <td class="label">Net Billable Amount:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_net_billable|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}
        {% if display_amount_paid > 0 %}
        <tr class="paid-amount">
            <td class="label">Amount Paid:</td>
            <td class="value">- {{ school_profile.currency_symbol|default:'$' }}{{ display_amount_paid|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}
        <tr class="grand-total">
            <td class="label">BALANCE DUE:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_balance_due|floatformat:2|intcomma }}</td>
        </tr>
    </table>

    {% if invoice.notes_to_parent %}
        <div class="notes-section-box">
            <h4>Important Notes</h4>
            <div>{{ invoice.notes_to_parent|linebreaksbr }}</div>
        </div>
    {% endif %}

    {% if school_profile.payment_instructions %}
        <div class="notes-section-box" style="border-left-color: #f39c12; margin-top:5mm;">
            <h4 style="color: #d35400;">Payment Instructions</h4>
            <div>{{ school_profile.payment_instructions|linebreaksbr }}</div>
        </div>
    {% endif %}

    <div class="final-message-section">
        <p>For any inquiries regarding this invoice, please contact the school accounts office.</p>
        {% if invoice.due_date and invoice.balance_due > 0 %}
            <p class="due-date-emphasis">Kindly settle the outstanding balance by {{ invoice.due_date|date:"F d, Y" }}.</p>
        {% endif %}
        <p style="margin-top: 7mm; font-weight: bold;">Thank you!</p>
    </div>
{% endblock pdf_main_content %}

{% block pdf_footer_content %}
    {# Override footer from _pdf_base.html for invoice-specific footer #}
    <p>{{ school_profile.school_name_on_reports|default:tenant.name }} - Generated: {% now "F d, Y H:i" %}</p>
    <p class="page-number"></p> {# CSS in _pdf_base.html handles 'Page X of Y' via ::after #}
    {% if school_profile.website %}<p style="font-size:7pt;">{{ school_profile.website }}</p>{% endif %}
{% endblock pdf_footer_content %}









































{% comment %} {# D:\school_fees_saas_v2\apps\fees\templates\fees\pdf\invoice_pdf_template.html #}
{% extends "reporting/pdf/_pdf_base.html" %}
{% load static humanize fees_tags core_tags math_filters %}

{% block pdf_title %}Invoice #{{ invoice.invoice_number_display|default:invoice.pk }} - {{ school_profile.school_name_on_reports|default:tenant.name }}{% endblock %}

{# This block customizes the content within the header_frame defined in _pdf_base.html #}
{% block pdf_header_content %}
    {# Call block.super to include the default header structure from _pdf_base.html if you want to modify parts of it #}
    {# OR, redefine the entire header table structure here if you want full control for invoices #}
    {# For this example, let's redefine to match the "premium" style more closely #}
    <table style="width: 100%; border-collapse: collapse; vertical-align: top;">
        <tr>
            <td style="width: 55%; padding-right: 10mm; vertical-align: top;"> {# School Info #}
                {% if school_profile.logo and school_profile.logo.path %}
                    <img src="{{ school_profile.logo.path }}" style="max-height: 16mm; max-width: 45mm; margin-bottom: 3mm;" alt="School Logo">
                {% endif %}
                <h2 style="margin: 0 0 2mm 0; font-size: 14pt; color: #2c3e50; font-weight:bold;">
                    {{ school_profile.school_name_on_reports|default:school_profile.school_name_override|default:tenant.name }}
                </h2>
                {% if school_profile.school_motto %}
                    <p style="margin: 0 0 2mm 0; font-style: italic; color: #555; font-size: 8pt;">
                        "{{ school_profile.school_motto }}"
                    </p>
                {% endif %}
                <div style="font-size: 8pt; color: #444; line-height: 1.25;">
                    {% if school_profile.address_line_1 %}{{ school_profile.address_line_1 }}<br>{% endif %}
                    {% if school_profile.address_line_2 %}{{ school_profile.address_line_2 }}<br>{% endif %}
                    {% if school_profile.city %}{{ school_profile.city }}{% endif %}{% if school_profile.city and school_profile.state_province %}, {% endif %}{% if school_profile.state_province %}{{ school_profile.state_province }}{% endif %} {% if school_profile.postal_code %}{{ school_profile.postal_code }}{% endif %}
                    {% if school_profile.country_name %}<br>{{ school_profile.country_name }}{% endif %}
                    {% if school_profile.phone_number %}<br><strong>P:</strong> {{ school_profile.phone_number }}{% endif %}
                    {% if school_profile.school_email %}<br><strong>E:</strong> {{ school_profile.school_email }}{% endif %}
                </div>
                {% block school_extra_header_info %}{% endblock school_extra_header_info %} {# Allows further customization if needed #}
            </td>
            <td style="width: 45%; text-align: right; vertical-align: top;"> {# Invoice Info #}
                <h1 style="margin:0 0 8px 0; font-size:22pt; color: #0056b3; text-transform: uppercase; letter-spacing: 0.5px;">INVOICE</h1>
                <div style="background-color: #f0f7ff; padding: 2mm 3mm; border-left: 1mm solid #007bff; font-size: 9pt; display: inline-block; text-align: left;">
                    <p style="margin:0.5mm 0; font-weight: bold;">Invoice No: {{ invoice.invoice_number_display|default:invoice.pk }}</p>
                    <p style="margin:0.5mm 0;">Status: <strong style="text-transform: capitalize;">{% invoice_status_badge invoice.status plain_text=True %}</strong></p>
                    <p style="margin:0.5mm 0;">Issue Date: {{ invoice.issue_date|date:"F d, Y" }}</p>
                    {% if invoice.due_date %}<p style="margin:0.5mm 0;">Due Date: <strong style="color: #c0392b;">{{ invoice.due_date|date:"F d, Y" }}</strong></p>{% endif %}
                </div>
            </td>
        </tr>
    </table>
    <hr style="margin-top: 1.5mm; margin-bottom: 1.5mm; border: none; border-top: 0.5px solid #333;">
{% endblock pdf_header_content %}


{% block pdf_extra_styles %}
    {{ block.super }}
    <style type="text/css">
        .address-box { padding: 3mm; font-size: 9pt; line-height: 1.4; vertical-align: top; }
        .bill-to-box { background-color: #f8f9fa; border: 0.5px solid #dee2e6; border-left: 2px solid #007bff; }
        .bill-to-box h4, .period-info-box h4 {
            margin: 0 0 2mm 0; color: #0056b3; font-size: 10pt; font-weight: bold;
            padding-bottom: 1mm; border-bottom: 0.5px solid #cce5ff;
        }
        .period-info-box { text-align: right; }
        .period-info-box p { margin: 1mm 0; }

        .items-table thead th { /* Override base for invoice-specific header */
            background-color: #007bff; color: white; font-size: 9pt; font-weight: bold;
            padding: 2mm; border: 0.5px solid #0056b3; text-align: left;
        }
        .items-table th.text-right, .items-table td.text-right { text-align: right !important; }
        .items-table th.text-center, .items-table td.text-center { text-align: center !important; }

        .items-table tbody td { padding: 2mm; border: 0.5px solid #e0e0e0; font-size: 8.5pt; }
        .items-table tbody tr:nth-child(even) td { background-color: #f7faff; }
        .items-table .item-description strong { color: #2c3e50; }
        .items-table .item-description small { color: #7f8c8d; font-style: italic; font-size: 7.5pt; }
        .items-table .discount-row td { color: #096dd9; font-style: italic; }
        .items-table .total-amount-cell { font-weight: 500; }

        .summary-table { width: 48%; margin-left: 52%; border: 1px solid #007bff; margin-top: 7mm; font-size: 9pt; border-collapse: collapse; }
        .summary-table td { padding: 2mm 2.5mm; border-top: 0.5px solid #cce5ff; }
        .summary-table .label { font-weight: 600; text-align: right; background-color: #f0f7ff; color: #004085; width: 60%; }
        .summary-table .value { text-align: right; width: 40%; font-family: 'Menlo', 'Consolas', 'Courier New', monospace; }
        .summary-table tr.grand-total td { background-color: #0056b3; color: white; font-weight: bold; font-size: 10.5pt; padding: 2.5mm; }
        .summary-table tr.net-billable td { border-top: 0.75px solid #7f8c8d; font-weight: bold; }
        .summary-table .paid-amount td { color: #228b22; font-weight: 500; }
        .summary-table .discount-amount td { color: #007bff; }

        .notes-section-box { margin-top: 8mm; padding: 3mm; background-color: #f9f9f9; border: 0.5px solid #e0e0e0; border-left: 2px solid #17a2b8; font-size: 8pt; line-height: 1.3; }
        .notes-section-box h4 { margin: 0 0 2mm 0; color: #117a8b; font-size: 9.5pt; font-weight:bold; }
        .notes-section-box div p, .notes-section-box p { margin-bottom: 1mm; }
        
        .final-message-section { margin-top: 8mm; padding-top: 4mm; border-top: 0.75px solid #007bff; text-align: center; font-size: 8pt; color: #495057; }
        .final-message-section p { margin: 1mm 0; }
        .final-message-section .due-date-emphasis { font-weight: bold; color: #c0392b; }
    </style>
{% endblock %}


{% block pdf_main_content %}
    <!-- Bill To & Academic Period Table -->
    <table class="no-border" style="margin-bottom: 7mm; width: 100%;">
        <tr>
            <td style="width: 55%;" class="address-box bill-to-box">
                <h4>Bill To</h4>
                {% if invoice.student %}
                    <p style="font-weight: bold; font-size: 10pt; color: #2c3e50;">{{ invoice.student.get_full_name }}</p>
                    {% if invoice.student.admission_number %}<p>Adm. No: {{ invoice.student.admission_number }}</p>{% endif %}
                    {% if invoice.student.current_class %}
                        <p>Class: {{ invoice.student.current_class.name }}{% if invoice.student.current_section %} - {{ invoice.student.current_section.name }}{% endif %}</p>
                    {% endif %}
                    {% with primary_parent=invoice.student.get_primary_parent %}
                        {% if primary_parent %}
                            <div style="margin-top: 2mm; padding-top: 1.5mm; border-top: 0.5px dotted #bdc3c7;">
                                <p style="color: #7f8c8d; margin-bottom:1mm; font-size:8pt;">Parent/Guardian:</p>
                                <p><strong>{{ primary_parent.get_full_name }}</strong></p>
                                {% if primary_parent.phone_number %}<p>Phone: {{ primary_parent.phone_number }}</p>{% endif %}
                            </div>
                        {% endif %}
                    {% endwith %}
                {% else %}
                    <p style="color: #c0392b;"><em>Student information missing.</em></p>
                {% endif %}
            </td>
            <td style="width: 45%; vertical-align: top;" class="address-box period-info-box">
                 <h4>Period Details</h4>
                {% if invoice.academic_year %}<p><strong>Academic Year:</strong> {{ invoice.academic_year.name }}</p>{% endif %}
                {% if invoice.term %}<p><strong>Term:</strong> {{ invoice.term.name }}</p>{% endif %}
                {% if invoice.fee_structure_used %}<p><strong>Fee Structure:</strong> {{ invoice.fee_structure_used.name }}</p>{% endif %}
            </td>
        </tr>
    </table>

    <!-- Invoice Items Table -->
    <h3 class="section-title">Invoice Breakdown</h3>
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 45%;">Item / Description</th>
                <th class="text-center" style="width: 10%;">Qty</th>
                <th class="text-right" style="width: 20%;">Unit Price</th>
                <th class="text-right" style="width: 25%;">Total</th>
            </tr>
        </thead>
        <tbody>
            {% for item in charge_items %}
            <tr>
                <td class="item-description">
                    <strong>
                        {% if item.fee_head %}{{ item.fee_head.name }}{% else %}{{ item.description }}{% endif %}
                    </strong>
                    {% if item.description and item.fee_head and item.description|lower != item.fee_head.name|lower %}
                        <br><small>{{ item.description }}</small>
                    {% elif not item.fee_head and item.description %}
                    {% endif %}
                </td>
                <td class="text-center">{{ item.quantity|floatformat:"-2"|default:"1" }}</td>
                <td class="text-right">{{ school_profile.currency_symbol|default:'$' }}{{ item.unit_price|floatformat:2|intcomma }}</td>
                <td class="text-right total-amount-cell">{{ school_profile.currency_symbol|default:'$' }}{{ item.amount|floatformat:2|intcomma }}</td>
            </tr>
            {% endfor %}

            {% for item in concession_lines %}
            <tr class="discount-row">
                <td class="item-description">
                    <strong>
                        {% if item.concession_type %}{{ item.concession_type.name }}{% else %}{{ item.description }}{% endif %}
                    </strong>
                     <em>(Discount)</em>
                    {% if item.description and item.concession_type and item.description|lower != item.concession_type.name|lower %}
                        <br><small>{{ item.description }}</small>
                    {% endif %}
                </td>
                <td class="text-center">-</td>
                <td class="text-right"></td> {# Unit price for overall discount usually not shown per item #}
                <td class="text-right total-amount-cell">
                    ({{ school_profile.currency_symbol|default:'$' }}{{ item.amount|abs|floatformat:2|intcomma }})
                </td>
            </tr>
            {% endfor %}

            {% if not charge_items and not concession_lines %}
                <tr>
                    <td colspan="4" class="text-center" style="padding: 15px; color: #7f8c8d;">
                        <em>No items found for this invoice.</em>
                    </td>
                </tr>
            {% endif %}
        </tbody>
    </table>

    <!-- Summary Table -->
    <table class="summary-table">
        <tr>
            <td class="label">Subtotal:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_subtotal|floatformat:2|intcomma }}</td>
        </tr>
        {% if display_total_concessions > 0 %}
        <tr class="discount-amount">
            <td class="label">Total Discounts:</td>
            <td class="value">- {{ school_profile.currency_symbol|default:'$' }}{{ display_total_concessions|floatformat:2|intcomma }}</td>
        </tr>
        <tr class="net-billable">
            <td class="label">Net Billable Amount:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_net_billable|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}
        {% if display_amount_paid > 0 %}
        <tr class="paid-amount">
            <td class="label">Amount Paid:</td>
            <td class="value">- {{ school_profile.currency_symbol|default:'$' }}{{ display_amount_paid|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}
        <tr class="grand-total">
            <td class="label">BALANCE DUE:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_balance_due|floatformat:2|intcomma }}</td>
        </tr>
    </table>

    <!-- Notes & Payment Instructions -->
    {% if invoice.notes_to_parent %}
        <div class="notes-section-box">
            <h4>Important Notes</h4>
            <div>{{ invoice.notes_to_parent|linebreaksbr }}</div>
        </div>
    {% endif %}

    {% if school_profile.payment_instructions %}
        <div class="notes-section-box" style="border-left-color: #f39c12; margin-top:5mm;">
            <h4 style="color: #d35400;">Payment Instructions</h4>
            <div>{{ school_profile.payment_instructions|linebreaksbr }}</div>
        </div>
    {% endif %}

    <!-- Footer Message within Main Content -->
    <div class="final-message-section">
        <p>For any inquiries regarding this invoice, please contact the school accounts office.</p>
        {% if invoice.due_date and invoice.balance_due > 0 %}
            <p class="due-date-emphasis">Kindly settle the outstanding balance by {{ invoice.due_date|date:"F d, Y" }}.</p>
        {% endif %}
        <p style="margin-top: 7mm; font-weight: bold;">Thank you!</p>
    </div>
{% endblock pdf_main_content %}

{% block pdf_footer_content %}
    {# This overrides the block in _pdf_base.html to provide specific footer content if needed #}
    {# If the default footer from _pdf_base.html is good, you can omit this block #}
    {# Or call {{ block.super }} and add to it #}
    <p>{{ school_profile.school_name_on_reports|default:tenant.name }} | Invoice Generated: {% now "F d, Y, H:i" %}</p>
    <p class="page-number"></p>
{% endblock pdf_footer_content %}

 {% endcomment %}



















{% comment %} {# D:\school_fees_saas_v2\apps\fees\templates\fees\pdf\invoice_pdf_template.html #}
{% extends "reporting/pdf/_pdf_base.html" %}

{% load static humanize fees_tags core_tags math_filters %}

{% block pdf_title %}Invoice #{{ invoice.invoice_number_display|default:invoice.pk }} - {{ school_profile.school_name_on_reports|default:tenant.name }}{% endblock %}

{# This block customizes the content within the header_frame defined in _pdf_base.html #}
{% block pdf_header_content %}
    <table style="width: 100%; border-collapse: collapse; vertical-align: top;">
        <tr>
            <td style="width: 60%; padding-right: 20px;"> {# School Info #}
                {% if school_profile.logo and school_profile.logo.path %}
                    <img src="{{ school_profile.logo.path }}" style="max-height: 60px; max-width: 180px; margin-bottom: 10px;" alt="School Logo">
                {% endif %}
                <h1 style="margin: 0 0 5px 0; font-size: 16pt; color: #2c3e50;">
                    {{ school_profile.school_name_on_reports|default:school_profile.school_name_override|default:tenant.name }}
                </h1>
                {% if school_profile.school_motto %}
                    <p style="margin: 0 0 8px 0; font-style: italic; color: #555; font-size: 8pt;">
                        "{{ school_profile.school_motto }}"
                    </p>
                {% endif %}
                <div style="font-size: 8.5pt; color: #444; line-height: 1.3;">
                    {% if school_profile.address_line_1 %}{{ school_profile.address_line_1 }}<br>{% endif %}
                    {% if school_profile.address_line_2 %}{{ school_profile.address_line_2 }}<br>{% endif %}
                    {% if school_profile.city %}{{ school_profile.city }}{% endif %}{% if school_profile.city and school_profile.state_province %}, {% endif %}{% if school_profile.state_province %}{{ school_profile.state_province }}{% endif %} {% if school_profile.postal_code %}{{ school_profile.postal_code }}{% endif %}
                    {% if school_profile.country_name %}<br>{{ school_profile.country_name }}{% endif %}
                    {% if school_profile.phone_number %}<br><strong>P:</strong> {{ school_profile.phone_number }}{% endif %}
                    {% if school_profile.school_email %}<br><strong>E:</strong> {{ school_profile.school_email }}{% endif %}
                </div>
            </td>
            <td style="width: 40%; text-align: right; vertical-align: top;"> {# Invoice Info #}
                <h1 style="margin:0 0 10px 0; font-size:24pt; color: #0056b3; text-transform: uppercase; letter-spacing: 1px;">INVOICE</h1>
                <div style="background-color: #e7f3fe; padding: 8px 10px; border-left: 3px solid #007bff; font-size: 9pt;">
                    <p style="margin:0; font-weight: bold;">Invoice No: {{ invoice.invoice_number_display|default:invoice.pk }}</p>
                    <p style="margin:2px 0;">Status: <strong style="text-transform: capitalize;">{% invoice_status_badge invoice.status plain_text=True %}</strong></p>
                    <p style="margin:2px 0;">Issue Date: {{ invoice.issue_date|date:"F d, Y" }}</p>
                    {% if invoice.due_date %}<p style="margin:2px 0;">Due Date: <strong style="color: #c0392b;">{{ invoice.due_date|date:"F d, Y" }}</strong></p>{% endif %}
                </div>
            </td>
        </tr>
    </table>
{% endblock pdf_header_content %}


{% block pdf_extra_styles %}
    {{ block.super }}
    <style type="text/css">
        /* Using styles defined in _pdf_base.html as much as possible */
        /* Overrides or additions specific to invoice content */
        .bill-to-section {
            margin-top: 20px;
            margin-bottom: 25px;
        }
        .bill-to-box {
            background-color: #f8f9fa;
            padding: 12px 15px;
            border: 1px solid #e0e0e0;
            border-left: 3px solid #007bff; /* Accent color */
            font-size: 9pt;
        }
        .bill-to-box h3 {
            margin: 0 0 8px 0;
            color: #0056b3; /* Darker primary */
            font-size: 11pt;
            padding-bottom: 4px;
            border-bottom: 1px solid #cce5ff;
        }
        .bill-to-box p { margin: 2px 0; }

        .period-info {
            text-align: right;
            font-size: 9pt;
        }
        .period-info p { margin: 2px 0; }
        
        /* Items Table enhancements */
        .items-table th { /* From _pdf_base.html, with primary color */
            background-color: #007bff; 
            color: white;
            font-size: 9.5pt;
            padding: 8px;
            border: 1px solid #0056b3;
        }
        .items-table td {
            padding: 7px 8px;
            border: 1px solid #dee2e6;
            font-size: 9pt;
            vertical-align: top;
        }
        .items-table tr:nth-child(even) td { /* Subtle striping for items */
            background-color: #fbfcfe;
        }
        .items-table .item-description strong { color: #34495e; }
        .items-table .item-description small { color: #7f8c8d; font-style: italic; }
        .items-table .discount-row td { background-color: #e6f7ff; color: #096dd9; }

        /* Summary Table enhancements */
        .summary-table {
            width: 45%; /* Slightly narrower */
            margin-left: 55%;
            border: 1px solid #bdc3c7;
            margin-top: 20px;
        }
        .summary-table td {
            padding: 8px 10px;
            font-size: 9.5pt;
            border-top: 1px solid #ecf0f1; /* Lighter internal borders */
        }
        .summary-table .label {
            font-weight: 600; /* Slightly less bold */
            text-align: right;
            background-color: #f9fafb;
            color: #34495e;
            width: 60%;
        }
        .summary-table .value {
            text-align: right;
            width: 40%;
            font-family: 'Menlo', 'Consolas', 'Courier New', monospace; /* Monospaced for numbers */
        }
        .summary-table tr.grand-total td {
            background-color: #34495e; /* Darker, elegant total */
            color: white;
            font-weight: bold;
            font-size: 11pt;
            padding: 10px;
        }
        .summary-table tr.net-billable td {
            border-top: 1px solid #7f8c8d;
            font-weight: bold;
        }
        .summary-table .paid-amount td {
            color: #27ae60; /* Green for paid */
        }
        .summary-table .discount-amount td {
            color: #2980b9; /* Blue for discount */
        }


        .notes-box {
            margin-top: 25px;
            padding: 12px 15px;
            background-color: #fdfdfe;
            border: 1px solid #ecf0f1;
            border-left: 3px solid #1abc9c; /* Teal accent */
            font-size: 8.5pt;
            line-height: 1.4;
        }
        .notes-box h4 {
            margin: 0 0 8px 0; 
            color: #16a085; /* Darker teal */
            font-size: 10pt;
        }

        .footer-message {
            margin-top: 30px; 
            padding-top: 15px; 
            border-top: 1px solid #007bff; 
            text-align: center; 
            font-size: 8.5pt; 
            color: #555;
        }
    </style>
{% endblock %}


{% block pdf_main_content %}
    <!-- Bill To & Academic Period Table -->
    <table class="no-border bill-to-section">
        <tr>
            <td style="width: 55%;" class="bill-to-box">
                <h3>Bill To</h3>
                {% if invoice.student %}
                    <p style="font-weight: bold; font-size: 10.5pt; color: #2c3e50;">{{ invoice.student.get_full_name }}</p>
                    {% if invoice.student.admission_number %}<p>Adm. No: {{ invoice.student.admission_number }}</p>{% endif %}
                    {% if invoice.student.current_class %}
                        <p>Class: {{ invoice.student.current_class.name }}{% if invoice.student.current_section %} - {{ invoice.student.current_section.name }}{% endif %}</p>
                    {% endif %}
                    {% with primary_parent=invoice.student.get_primary_parent %}
                        {% if primary_parent %}
                            <div style="margin-top: 8px; padding-top: 6px; border-top: 1px dotted #ccc;">
                                <p style="color: #7f8c8d; margin-bottom:3px;">Parent/Guardian:</p>
                                <p><strong>{{ primary_parent.get_full_name }}</strong></p>
                                {% if primary_parent.phone_number %}<p>Phone: {{ primary_parent.phone_number }}</p>{% endif %}
                            </div>
                        {% endif %}
                    {% endwith %}
                {% else %}
                    <p style="color: #c0392b;"><em>Student information missing.</em></p>
                {% endif %}
            </td>
            <td style="width: 45%; vertical-align: top;" class="period-info">
                {% if invoice.academic_year %}<p><strong>Academic Year:</strong> {{ invoice.academic_year.name }}</p>{% endif %}
                {% if invoice.term %}<p><strong>Term:</strong> {{ invoice.term.name }}</p>{% endif %}
                {% if invoice.fee_structure_used %}<p><strong>Fee Structure:</strong> {{ invoice.fee_structure_used.name }}</p>{% endif %}
            </td>
        </tr>
    </table>

    <!-- Invoice Items Table -->
    <h3 style="color: #34495e; margin-bottom: 8px;">Invoice Breakdown</h3>
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 45%;">Item / Description</th>
                <th class="text-center" style="width: 10%;">Qty</th>
                <th class="text-end" style="width: 20%;">Unit Price</th>
                <th class="text-end" style="width: 25%;">Total</th>
            </tr>
        </thead>
        <tbody>
            {% for item in charge_items %}
            <tr>
                <td class="item-description">
                    <strong>
                        {% if item.fee_head %}{{ item.fee_head.name }}{% else %}{{ item.description }}{% endif %}
                    </strong>
                    {% if item.description and item.fee_head and item.description|lower != item.fee_head.name|lower %}
                        <br><small>{{ item.description }}</small>
                    {% elif not item.fee_head and item.description %}
                        {# Already shown as strong if no fee_head #}
                    {% endif %}
                </td>
                <td class="text-center">{{ item.quantity|floatformat:"-2"|default:"1" }}</td>
                <td class="text-end">{{ school_profile.currency_symbol|default:'$' }}{{ item.unit_price|floatformat:2|intcomma }}</td>
                <td class="text-end fw-bold">{{ school_profile.currency_symbol|default:'$' }}{{ item.amount|floatformat:2|intcomma }}</td>
            </tr>
            {% endfor %}

            {% for item in concession_lines %}
            <tr class="discount-row">
                <td class="item-description">
                    <strong>
                        {% if item.concession_type %}{{ item.concession_type.name }}{% else %}{{ item.description }}{% endif %}
                    </strong>
                    <em>(Discount)</em>
                    {% if item.description and item.concession_type and item.description|lower != item.concession_type.name|lower %}
                        <br><small>{{ item.description }}</small>
                    {% endif %}
                </td>
                <td class="text-center">-</td>
                <td class="text-end"></td>
                <td class="text-end fw-bold">
                    ({{ school_profile.currency_symbol|default:'$' }}{{ item.amount|abs|floatformat:2|intcomma }})
                </td>
            </tr>
            {% endfor %}

            {% if not charge_items and not concession_lines %}
                <tr>
                    <td colspan="4" class="text-center" style="padding: 15px; color: #7f8c8d;">
                        <em>No items found for this invoice.</em>
                    </td>
                </tr>
            {% endif %}
        </tbody>
    </table>

    <!-- Summary Table -->
    <table class="summary-table">
        <tr>
            <td class="label">Subtotal:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_subtotal|floatformat:2|intcomma }}</td>
        </tr>
        {% if display_total_concessions > 0 %}
        <tr class="discount-amount">
            <td class="label">Total Discounts:</td>
            <td class="value">- {{ school_profile.currency_symbol|default:'$' }}{{ display_total_concessions|floatformat:2|intcomma }}</td>
        </tr>
        <tr class="net-billable">
            <td class="label">Net Billable Amount:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_net_billable|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}
        {% if display_amount_paid > 0 %}
        <tr class="paid-amount">
            <td class="label">Amount Paid:</td>
            <td class="value">- {{ school_profile.currency_symbol|default:'$' }}{{ display_amount_paid|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}
        <tr class="grand-total">
            <td class="label">BALANCE DUE:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_balance_due|floatformat:2|intcomma }}</td>
        </tr>
    </table>

    <!-- Notes Section -->
    {% if invoice.notes_to_parent %}
        <div class="notes-box">
            <h4>Important Notes</h4>
            <div>{{ invoice.notes_to_parent|linebreaksbr }}</div>
        </div>
    {% endif %}

    <!-- Payment Instructions -->
    {% if school_profile.payment_instructions %}
        <div class="notes-box" style="border-left-color: #ffc107; margin-top:15px;"> {# Yellow accent #}
            <h4 style="color: #856404;">Payment Instructions</h4>
            <div>{{ school_profile.payment_instructions|linebreaksbr }}</div>
        </div>
    {% endif %}

    <!-- Footer Message within Main Content (Not part of repeating footer_frame) -->
    <div class="footer-message">
        <p>
            For any queries regarding this invoice, please contact the school office.
        </p>
        {% if invoice.due_date and invoice.balance_due > 0 %}
            <p style="font-weight: bold; color: #c0392b;">
                Kindly settle the outstanding balance by the due date: {{ invoice.due_date|date:"F d, Y" }}.
            </p>
        {% endif %}
        <p style="margin-top: 10px;">
            <strong>{{ school_profile.school_name_on_reports|default:school_profile.school_name_override|default:tenant.name }}</strong>
        </p>
    </div>

{% endblock pdf_main_content %}
 {% endcomment %}





























{% comment %} {% extends "reporting/pdf/_pdf_base.html" %}

{% load static humanize fees_tags core_tags math_filters %}

{% block pdf_title %}Invoice #{{ invoice.invoice_number_display }} - {{ school_profile.school_name_on_reports|default:tenant.name }}{% endblock %}

{% block document_name_header %}INVOICE{% endblock %}

{% block document_specific_header_info %}
    <!-- Invoice Details Box -->
    <div style="background-color: #f0f8ff; padding: 10px; border: 2px solid #007bff; margin-top: 5px;">
        <p style="margin:0; font-size:12pt; font-weight: bold; color: #007bff;">Invoice No: {{ invoice.invoice_number_display }}</p>
        <p style="margin:3px 0 0 0; font-size:10pt;">Status: <strong>{{ invoice.get_status_display }}</strong></p>
        <p style="margin:3px 0 0 0; font-size:10pt;">Issue Date: <strong>{{ invoice.issue_date|date:"F d, Y" }}</strong></p>
        {% if invoice.due_date %}<p style="margin:3px 0 0 0; font-size:10pt;">Due Date: <strong style="color: #dc3545;">{{ invoice.due_date|date:"F d, Y" }}</strong></p>{% endif %}
    </div>
{% endblock %}

{% block pdf_extra_styles %}
    {{ block.super }}
    <style type="text/css">
        .invoice-info {
            background-color: #f8f9fa;
            padding: 10px;
            border: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        .invoice-info table {
            width: 100%;
            border-collapse: collapse;
        }
        .invoice-info td {
            padding: 3px 0;
            font-size: 10pt;
        }
        .bill-to {
            background-color: #f8f9fa;
            padding: 15px;
            border: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        .bill-to h3 {
            margin: 0 0 10px 0;
            color: #007bff;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border: 2px solid #007bff;
        }
        .items-table th {
            background-color: #007bff;
            color: white;
            padding: 10px 8px;
            text-align: left;
            border: 1px solid #007bff;
            font-size: 10pt;
        }
        .items-table td {
            padding: 8px;
            border: 1px solid #dee2e6;
            font-size: 9pt;
            vertical-align: top;
        }
        .items-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .summary-table {
            width: 50%;
            margin-left: 50%;
            border-collapse: collapse;
            margin-top: 20px;
            border: 1px solid #dee2e6;
        }
        .summary-table td {
            padding: 10px 12px;
            border: 1px solid #dee2e6;
            font-size: 10pt;
        }
        .summary-table .label {
            font-weight: bold;
            text-align: right;
            background-color: #f8f9fa;
            width: 60%;
        }
        .summary-table .value {
            text-align: right;
            width: 40%;
            font-family: 'Courier New', monospace;
        }
        .grand-total {
            background-color: #28a745;
            color: white;
            font-weight: bold;
            font-size: 12pt;
        }
        .grand-total td {
            padding: 12px;
        }
        .notes {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #17a2b8;
        }
    </style>
{% endblock %}

{% block pdf_main_content %}



    <!-- School Information Section -->
    <table style="width: 100%; margin-bottom: 20px; border-collapse: collapse;">
        <tr>
            <td style="width: 60%; vertical-align: top;">
                {% if school_profile.logo %}
                    <img src="{{ school_profile.logo.url }}" style="max-height: 60px; margin-bottom: 10px;" alt="School Logo">
                {% endif %}
                <h2 style="margin: 0 0 5px 0; font-size: 14pt; color: #007bff;">
                    {{ school_profile.school_name_on_reports|default:school_profile.school_name_override|default:tenant.name }}
                </h2>
                {% if school_profile.school_motto %}
                    <p style="margin: 0 0 8px 0; font-style: italic; color: #666; font-size: 9pt;">
                        "{{ school_profile.school_motto }}"
                    </p>
                {% endif %}
                <div style="font-size: 9pt; color: #333; line-height: 1.3;">
                    {% if school_profile.address_line1 %}{{ school_profile.address_line1 }}<br>{% endif %}
                    {% if school_profile.address_line2 %}{{ school_profile.address_line2 }}<br>{% endif %}
                    {% if school_profile.city %}{{ school_profile.city }}{% endif %}{% if school_profile.city and school_profile.state_province %}, {% endif %}{% if school_profile.state_province %}{{ school_profile.state_province }}{% endif %} {% if school_profile.postal_code %}{{ school_profile.postal_code }}{% endif %}<br>
                    {% if school_profile.country_name %}{{ school_profile.country_name }}<br>{% endif %}
                    {% if school_profile.phone_number %}<strong>Phone:</strong> {{ school_profile.phone_number }}<br>{% endif %}
                    {% if school_profile.school_email %}<strong>Email:</strong> {{ school_profile.school_email }}{% endif %}
                </div>
            </td>
            <td style="width: 40%; text-align: right; vertical-align: top;">
                <!-- Academic Period Info -->
                {% if invoice.academic_year or invoice.term or invoice.fee_structure %}
                <div style="background-color: #fff3cd; padding: 10px; border: 1px solid #ffc107;">
                    <h4 style="margin: 0 0 8px 0; color: #856404; font-size: 10pt;">Academic Period</h4>
                    {% if invoice.academic_year %}<p style="margin: 2px 0; font-size: 9pt;"><strong>Year:</strong> {{ invoice.academic_year.name }}</p>{% endif %}
                    {% if invoice.term %}<p style="margin: 2px 0; font-size: 9pt;"><strong>Term:</strong> {{ invoice.term.name }}</p>{% endif %}
                    {% if invoice.fee_structure %}<p style="margin: 2px 0; font-size: 9pt;"><strong>Structure:</strong> {{ invoice.fee_structure.name }}</p>{% endif %}
                </div>
                {% endif %}
            </td>
        </tr>
    </table>

    <!-- Bill To Section -->
    <div style="background-color: #f8f9fa; padding: 15px; border: 1px solid #007bff; margin-bottom: 20px;">
        <h3 style="margin: 0 0 10px 0; color: #007bff; font-size: 12pt; border-bottom: 2px solid #007bff; padding-bottom: 5px;">Bill To</h3>
        {% if invoice.student %}
            <p style="font-weight: bold; font-size: 11pt; color: #007bff; margin: 0 0 5px 0;">{{ invoice.student.get_full_name }}</p>
            {% if invoice.student.admission_number %}
                <p style="margin: 3px 0; font-size: 9pt;"><strong>Admission No:</strong> {{ invoice.student.admission_number }}</p>
            {% endif %}
            {% if invoice.student.current_class %}
                <p style="margin: 3px 0; font-size: 9pt;"><strong>Class:</strong> {{ invoice.student.current_class.name }}{% if invoice.student.current_section %} - {{ invoice.student.current_section.name }}{% endif %}</p>
            {% endif %}

            {% with primary_parent=invoice.student.get_primary_parent %}
                {% if primary_parent %}
                    <div style="margin-top: 10px; padding-top: 8px; border-top: 1px solid #dee2e6;">
                        <p style="margin: 0 0 5px 0; font-size: 10pt; color: #666; font-weight: bold;">Parent/Guardian:</p>
                        <p style="margin: 3px 0; font-size: 9pt;"><strong>Name:</strong> {{ primary_parent.get_full_name }}</p>
                        {% if primary_parent.phone_number %}
                            <p style="margin: 3px 0; font-size: 9pt;"><strong>Phone:</strong> {{ primary_parent.phone_number }}</p>
                        {% endif %}
                        {% if primary_parent.email %}
                            <p style="margin: 3px 0; font-size: 9pt;"><strong>Email:</strong> {{ primary_parent.email }}</p>
                        {% endif %}
                    </div>
                {% endif %}
            {% endwith %}
        {% else %}
            <p style="color: #dc3545; font-style: italic;">Student information not available</p>
        {% endif %}
    </div>

    <!-- Invoice Items Table -->
    <h3 style="color: #495057; margin-bottom: 10px; font-size: 12pt;">Invoice Items</h3>
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 45%;">Description</th>
                <th class="text-center" style="width: 12%;">Qty</th>
                <th class="text-right" style="width: 18%;">Unit Price</th>
                <th class="text-right" style="width: 25%;">Line Total</th>
            </tr>
        </thead>
        <tbody>
            {% for item in charge_items %}
            <tr>
                <td>
                    <strong>
                        {% if item.fee_head %}
                            {{ item.fee_head.name }}
                        {% else %}
                            {{ item.description }}
                        {% endif %}
                    </strong>
                    {% if item.description and item.fee_head and item.description|lower != item.fee_head.name|lower %}
                        <br><small style="color: #6c757d; font-style: italic;">{{ item.description }}</small>
                    {% endif %}
                </td>
                <td class="text-center">{{ item.quantity|floatformat:"-2"|default:"1" }}</td>
                <td class="text-right">{{ school_profile.currency_symbol|default:'$' }}{{ item.unit_price|floatformat:2|intcomma }}</td>
                <td class="text-right" style="font-weight: bold;">{{ school_profile.currency_symbol|default:'$' }}{{ item.amount|floatformat:2|intcomma }}</td>
            </tr>
            {% endfor %}

            {% if concession_lines %}
                {% for item in concession_lines %}
                <tr style="background-color: #f0f9ff; color: #0c5460;">
                    <td>
                        <strong>
                            {% if item.concession_type %}
                                {{ item.concession_type.name }}
                            {% else %}
                                {{ item.description }}
                            {% endif %}
                        </strong>
                        <em> (Discount)</em>
                        {% if item.description and item.concession_type and item.description|lower != item.concession_type.name|lower %}
                            <br><small style="font-style: italic;">{{ item.description }}</small>
                        {% endif %}
                    </td>
                    <td class="text-center">-</td>
                    <td class="text-right"></td>
                    <td class="text-right" style="font-weight: bold;">
                        ({{ school_profile.currency_symbol|default:'$' }}{{ item.amount|floatformat:2|intcomma }})
                    </td>
                </tr>
                {% endfor %}
            {% endif %}

            {% if not charge_items and not concession_lines %}
                <tr>
                    <td colspan="4" class="text-center" style="padding: 20px; color: #6c757d; font-style: italic;">
                        No billable items or concessions on this invoice.
                    </td>
                </tr>
            {% endif %}
        </tbody>
    </table>

    </table>

    <!-- Summary Table -->
    <table class="summary-table">
        <tr>
            <td class="label">Subtotal:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_subtotal|floatformat:2|intcomma }}</td>
        </tr>
        {% if display_total_concessions > 0 %}
        <tr>
            <td class="label">Total Discounts:</td>
            <td class="value" style="color: #17a2b8;">- {{ school_profile.currency_symbol|default:'$' }}{{ display_total_concessions|floatformat:2|intcomma }}</td>
        </tr>
        <tr>
            <td class="label">Net Billable Amount:</td>
            <td class="value" style="font-weight: bold;">{{ school_profile.currency_symbol|default:'$' }}{{ display_net_billable|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}
        {% if display_amount_paid > 0 %}
        <tr style="background-color: #d4edda;">
            <td class="label">Amount Paid:</td>
            <td class="value">- {{ school_profile.currency_symbol|default:'$' }}{{ display_amount_paid|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}
        <tr class="grand-total">
            <td class="label">BALANCE DUE:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_balance_due|floatformat:2|intcomma }}</td>
        </tr>
    </table>

    <!-- Notes Section -->
    {% if invoice.notes_to_parent %}
        <div class="notes">
            <h4 style="margin: 0 0 8px 0; color: #17a2b8;">Important Notes</h4>
            <div style="line-height: 1.5;">
                {{ invoice.notes_to_parent|linebreaksbr }}
            </div>
        </div>
    {% endif %}

    <!-- Payment Instructions -->
    {% if school_profile.payment_instructions %}
        <div style="margin-top: 20px; padding: 15px; background-color: #fff3cd; border: 1px solid #ffeaa7;">
            <h4 style="margin: 0 0 8px 0; color: #856404;">Payment Instructions</h4>
            <div style="line-height: 1.5;">
                {{ school_profile.payment_instructions|linebreaksbr }}
            </div>
        </div>
    {% endif %}

    <!-- Footer -->
    <div style="margin-top: 30px; padding-top: 15px; border-top: 2px solid #007bff; text-align: center; font-size: 8pt; color: #6c757d;">
        <p style="margin: 0;">
            <strong>{{ school_profile.school_name_on_reports|default:school_profile.school_name_override|default:tenant.name }}</strong>
        </p>
        <p style="margin: 5px 0 0 0;">
            Thank you for your partnership in your child's education.
        </p>
        {% if invoice.due_date %}
            <p style="margin: 5px 0 0 0; font-weight: bold; color: #dc3545;">
                Please ensure payment is made by {{ invoice.due_date|date:"F d, Y" }}
            </p>
        {% endif %}
    </div>

{% endblock pdf_main_content %} {% endcomment %}
