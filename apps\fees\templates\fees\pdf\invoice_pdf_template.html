{% extends "reporting/pdf/_pdf_base.html" %}

{% load static humanize fees_tags core_tags math_filters %}

{% block pdf_title %}Invoice #{{ invoice.invoice_number_display }} - {{ school_profile.school_name_on_reports|default:tenant.name }}{% endblock %}

{% block document_name_header %}INVOICE{% endblock %}

{% block document_specific_header_info %}
    <div style="background-color: #f0f8ff; padding: 10px; border: 2px solid #007bff; margin-top: 5px;">
        <p style="margin:0; font-size:12pt; font-weight: bold; color: #007bff;">Invoice No: {{ invoice.invoice_number_display }}</p>
        <p style="margin:3px 0 0 0; font-size:10pt;">Status: <strong>{{ invoice.get_status_display }}</strong></p>
        <p style="margin:3px 0 0 0; font-size:10pt;">Issue Date: <strong>{{ invoice.issue_date|date:"F d, Y" }}</strong></p>
        {% if invoice.due_date %}<p style="margin:3px 0 0 0; font-size:10pt;">Due Date: <strong style="color: #dc3545;">{{ invoice.due_date|date:"F d, Y" }}</strong></p>{% endif %}
    </div>
{% endblock %}

{% block pdf_extra_styles %}
    {{ block.super }}
    <style type="text/css">
        .bill-to-section {
            background-color: #f8f9fa;
            padding: 15px;
            border: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        
        .bill-to-section h4 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 11pt;
            border-bottom: 2px solid #007bff;
            padding-bottom: 4px;
        }
        
        .period-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            margin-bottom: 20px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            border: 2px solid #007bff;
        }
        
        .items-table th {
            background-color: #007bff;
            color: white;
            padding: 10px 8px;
            font-size: 10pt;
            border: 1px solid #007bff;
        }
        
        .items-table td {
            padding: 8px;
            border: 1px solid #dee2e6;
            font-size: 9pt;
        }
        
        .summary-table {
            width: 50%;
            margin-left: 50%;
            margin-top: 20px;
            border-collapse: collapse;
            border: 1px solid #dee2e6;
        }
        
        .summary-table td {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
        }
        
        .summary-table .label {
            font-weight: bold;
            text-align: right;
            background-color: #f8f9fa;
        }
        
        .summary-table .value {
            text-align: right;
        }
        
        .grand-total td {
            background-color: #28a745;
            color: white;
            font-weight: bold;
            font-size: 12pt;
        }
    </style>
{% endblock %}

{% block pdf_main_content %}
    {# Bill To Section #}
    <table class="no-border" style="width: 100%; margin-bottom: 20px;">
        <tr>
            <td style="width: 60%; vertical-align: top;" class="no-border">
                <div class="bill-to-section">
                    <h4>Bill To</h4>
                    {% if invoice.student %}
                        <p style="font-weight: bold; font-size: 11pt; color: #007bff; margin: 0 0 5px 0;">{{ invoice.student.get_full_name }}</p>
                        {% if invoice.student.admission_number %}
                            <p style="margin: 3px 0; font-size: 9pt;"><strong>Admission No:</strong> {{ invoice.student.admission_number }}</p>
                        {% endif %}
                        {% if invoice.student.current_class %}
                            <p style="margin: 3px 0; font-size: 9pt;"><strong>Class:</strong> {{ invoice.student.current_class.name }}{% if invoice.student.current_section %} - {{ invoice.student.current_section.name }}{% endif %}</p>
                        {% endif %}
                    {% else %}
                        <p style="color: #dc3545; margin: 0;">Student information not available</p>
                    {% endif %}
                </div>
            </td>
            <td style="width: 40%; vertical-align: top;" class="no-border">
                <div class="period-info">
                    <h4 style="margin: 0 0 8px 0; color: #856404; font-size: 10pt;">Invoice Period</h4>
                    {% if invoice.academic_year %}
                        <p style="margin: 2px 0; font-size: 9pt;"><strong>Academic Year:</strong> {{ invoice.academic_year.name }}</p>
                    {% endif %}
                    {% if invoice.term %}
                        <p style="margin: 2px 0; font-size: 9pt;"><strong>Term:</strong> {{ invoice.term.name }}</p>
                    {% endif %}
                </div>
            </td>
        </tr>
    </table>

    {# Invoice Items Table #}
    <h3 style="font-size: 12pt; margin-bottom: 10px; color: #495057;">Invoice Items</h3>
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 45%;">Description</th>
                <th style="width: 12%; text-align: center;">Quantity</th>
                <th style="width: 18%; text-align: right;">Unit Price</th>
                <th style="width: 25%; text-align: right;">Line Total</th>
            </tr>
        </thead>
        <tbody>
            {% for item in charge_items %}
            <tr>
                <td>
                    <strong>
                        {% if item.fee_head %}
                            {{ item.fee_head.name }}
                        {% else %}
                            {{ item.description }}
                        {% endif %}
                    </strong>
                </td>
                <td style="text-align: center;">{{ item.quantity|floatformat:"-2"|default:"1" }}</td>
                <td style="text-align: right;">{{ school_profile.currency_symbol|default:'$' }}{{ item.unit_price|floatformat:2|intcomma }}</td>
                <td style="text-align: right; font-weight: bold;">{{ school_profile.currency_symbol|default:'$' }}{{ item.amount|floatformat:2|intcomma }}</td>
            </tr>
            {% endfor %}

            {% if concession_lines %}
                {% for item in concession_lines %}
                <tr style="background-color: #f0f9ff;">
                    <td>
                        <strong>
                            {% if item.concession_type %}
                                {{ item.concession_type.name }}
                            {% else %}
                                {{ item.description }}
                            {% endif %}
                        </strong>
                        <em> (Discount)</em>
                    </td>
                    <td style="text-align: center;">-</td>
                    <td style="text-align: right;"></td>
                    <td style="text-align: right; font-weight: bold;">
                        ({{ school_profile.currency_symbol|default:'$' }}{{ item.amount|abs|floatformat:2|intcomma }})
                    </td>
                </tr>
                {% endfor %}
            {% endif %}

            {% if not charge_items and not concession_lines %}
                <tr>
                    <td colspan="4" style="text-align: center; padding: 20px; color: #6c757d; font-style: italic;">
                        No billable items or concessions on this invoice.
                    </td>
                </tr>
            {% endif %}
        </tbody>
    </table>

    {# Summary Table #}
    <table class="summary-table">
        <tr>
            <td class="label">Subtotal:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_subtotal|floatformat:2|intcomma }}</td>
        </tr>
        {% if display_total_concessions > 0 %}
        <tr>
            <td class="label">Total Discounts:</td>
            <td class="value" style="color: #17a2b8;">- {{ school_profile.currency_symbol|default:'$' }}{{ display_total_concessions|floatformat:2|intcomma }}</td>
        </tr>
        <tr>
            <td class="label">Net Billable Amount:</td>
            <td class="value" style="font-weight: bold;">{{ school_profile.currency_symbol|default:'$' }}{{ display_net_billable|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}
        {% if display_amount_paid > 0 %}
        <tr style="background-color: #d4edda;">
            <td class="label">Amount Paid:</td>
            <td class="value">- {{ school_profile.currency_symbol|default:'$' }}{{ display_amount_paid|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}
        <tr class="grand-total">
            <td class="label">BALANCE DUE:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_balance_due|floatformat:2|intcomma }}</td>
        </tr>
    </table>

    {# Notes Section #}
    {% if invoice.notes_to_parent %}
        <div style="margin-top: 25px; padding: 15px; background-color: #f8f9fa; border-left: 4px solid #17a2b8;">
            <h4 style="margin: 0 0 8px 0; color: #17a2b8; font-size: 11pt;">Important Notes</h4>
            <div style="line-height: 1.5; font-size: 9pt;">
                {{ invoice.notes_to_parent|linebreaksbr }}
            </div>
        </div>
    {% endif %}

    {# Payment Instructions #}
    {% if school_profile.payment_instructions %}
        <div style="margin-top: 20px; padding: 15px; background-color: #fff3cd; border: 1px solid #ffeaa7;">
            <h4 style="margin: 0 0 8px 0; color: #856404; font-size: 11pt;">Payment Instructions</h4>
            <div style="line-height: 1.5; font-size: 9pt;">
                {{ school_profile.payment_instructions|linebreaksbr }}
            </div>
        </div>
    {% endif %}

    {# Footer Information #}
    <div style="margin-top: 30px; padding-top: 15px; border-top: 2px solid #007bff; text-align: center; font-size: 8pt; color: #6c757d;">
        <p style="margin: 0;">
            <strong>{{ school_profile.school_name_on_reports|default:tenant.name }}</strong>
            {% if school_profile.website %} | {{ school_profile.website }}{% endif %}
        </p>
        <p style="margin: 5px 0 0 0;">
            Thank you for your partnership in your child's education.
        </p>
        {% if invoice.due_date %}
            <p style="margin: 5px 0 0 0; font-weight: bold; color: #dc3545;">
                Please ensure payment is made by {{ invoice.due_date|date:"F d, Y" }}
            </p>
        {% endif %}
    </div>
{% endblock pdf_main_content %}
