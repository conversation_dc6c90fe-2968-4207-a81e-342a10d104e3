{% extends "reporting/pdf/_pdf_base.html" %}

{% load static humanize fees_tags core_tags math_filters %}

{% block pdf_title %}Invoice #{{ invoice.invoice_number_display }} - {{ school_profile.school_name_on_reports|default:tenant.name }}{% endblock %}

{% block document_name_header %}INVOICE{% endblock %}

{% block pdf_header_content %}
    <table style="width: 100%; border-collapse: collapse;">
        <tr>
            <td style="width: 60%; vertical-align: top; padding: 0;">
                <!-- School Logo and Information -->
                <div style="margin-bottom: 10px;">
                    {% if school_profile.logo %}
                        <img src="{{ school_profile.logo.path }}" style="max-height: 80px; max-width: 250px; margin-bottom: 10px;" alt="School Logo">
                    {% endif %}

                    <div style="font-size: 11pt; line-height: 1.3;">
                        <h2 style="margin: 0 0 5px 0; font-size: 16pt; color: #007bff;">
                            {{ school_profile.school_name_on_reports|default:school_profile.school_name_override|default:tenant.name }}
                        </h2>

                        {% if school_profile.school_motto %}
                            <p style="margin: 0 0 8px 0; font-style: italic; color: #6c757d; font-size: 9pt;">
                                "{{ school_profile.school_motto }}"
                            </p>
                        {% endif %}

                        <!-- School Address -->
                        {% if school_profile.address_line1 or school_profile.city %}
                            <div style="font-size: 9pt; color: #495057; line-height: 1.4;">
                                {% if school_profile.address_line1 %}
                                    <div>{{ school_profile.address_line1 }}</div>
                                {% endif %}
                                {% if school_profile.address_line2 %}
                                    <div>{{ school_profile.address_line2 }}</div>
                                {% endif %}
                                {% if school_profile.city or school_profile.state_province or school_profile.postal_code %}
                                    <div>
                                        {% if school_profile.city %}{{ school_profile.city }}{% endif %}{% if school_profile.city and school_profile.state_province %}, {% endif %}{% if school_profile.state_province %}{{ school_profile.state_province }}{% endif %} {% if school_profile.postal_code %}{{ school_profile.postal_code }}{% endif %}
                                    </div>
                                {% endif %}
                                {% if school_profile.country_name %}
                                    <div>{{ school_profile.country_name }}</div>
                                {% endif %}
                            </div>
                        {% endif %}

                        <!-- Contact Information -->
                        {% if school_profile.phone_number or school_profile.school_email %}
                            <div style="font-size: 9pt; color: #495057; margin-top: 5px; line-height: 1.4;">
                                {% if school_profile.phone_number %}
                                    <div><strong>Phone:</strong> {{ school_profile.phone_number }}</div>
                                {% endif %}
                                {% if school_profile.school_email %}
                                    <div><strong>Email:</strong> {{ school_profile.school_email }}</div>
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </td>
            <td style="width: 40%; text-align: right; vertical-align: top; padding: 0;">
                <!-- Invoice Header -->
                <h1 style="margin: 0 0 15px 0; font-size: 28pt; color: #007bff; text-transform: uppercase;">INVOICE</h1>

                <!-- Invoice Details Box -->
                <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 15px; border: 2px solid #007bff; border-radius: 8px; margin-bottom: 10px;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="font-size: 10pt; padding: 2px 0; color: #495057;"><strong>Invoice No:</strong></td>
                            <td style="font-size: 11pt; padding: 2px 0; text-align: right; color: #007bff; font-weight: bold;">{{ invoice.invoice_number_display }}</td>
                        </tr>
                        <tr>
                            <td style="font-size: 10pt; padding: 2px 0; color: #495057;"><strong>Status:</strong></td>
                            <td style="font-size: 10pt; padding: 2px 0; text-align: right; font-weight: bold; {% if invoice.status == 'PAID' %}color: #28a745;{% elif invoice.status == 'OVERDUE' %}color: #dc3545;{% elif invoice.status == 'SENT' %}color: #007bff;{% else %}color: #6c757d;{% endif %}">{{ invoice.get_status_display }}</td>
                        </tr>
                        <tr>
                            <td style="font-size: 10pt; padding: 2px 0; color: #495057;"><strong>Issue Date:</strong></td>
                            <td style="font-size: 10pt; padding: 2px 0; text-align: right;">{{ invoice.issue_date|date:"F d, Y" }}</td>
                        </tr>
                        {% if invoice.due_date %}
                        <tr>
                            <td style="font-size: 10pt; padding: 2px 0; color: #495057;"><strong>Due Date:</strong></td>
                            <td style="font-size: 10pt; padding: 2px 0; text-align: right; color: #dc3545; font-weight: bold;">{{ invoice.due_date|date:"F d, Y" }}</td>
                        </tr>
                        {% endif %}
                    </table>
                </div>
            </td>
        </tr>
    </table>
    <hr style="margin-top: 10px; margin-bottom: 2px; border: none; border-top: 2px solid #007bff;">
{% endblock %}

{% block pdf_extra_styles %}
    {{ block.super }}
    <style type="text/css">
        .invoice-info {
            background-color: #f8f9fa;
            padding: 10px;
            border: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        .invoice-info table {
            width: 100%;
            border-collapse: collapse;
        }
        .invoice-info td {
            padding: 3px 0;
            font-size: 10pt;
        }
        .bill-to {
            background-color: #f8f9fa;
            padding: 15px;
            border: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        .bill-to h3 {
            margin: 0 0 10px 0;
            color: #007bff;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border: 2px solid #007bff;
        }
        .items-table th {
            background-color: #007bff;
            color: white;
            padding: 10px 8px;
            text-align: left;
            border: 1px solid #007bff;
        }
        .items-table td {
            padding: 8px;
            border: 1px solid #dee2e6;
        }
        .items-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .summary-table {
            width: 50%;
            margin-left: 50%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .summary-table td {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
        }
        .summary-table .label {
            font-weight: bold;
            text-align: right;
            background-color: #f8f9fa;
        }
        .summary-table .value {
            text-align: right;
        }
        .grand-total {
            background-color: #28a745;
            color: white;
            font-weight: bold;
            font-size: 12pt;
        }
        .notes {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #17a2b8;
        }
    </style>
{% endblock %}

{% block pdf_main_content %}



    <!-- Bill To and Invoice Period Section -->
    <table style="width: 100%; margin-bottom: 25px; border-collapse: collapse;">
        <tr>
            <td style="width: 60%; vertical-align: top; padding-right: 20px;">
                <!-- Bill To Section -->
                <div class="bill-to" style="background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); padding: 20px; border: 2px solid #007bff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,123,255,0.1);">
                    <h3 style="margin: 0 0 15px 0; color: #007bff; font-size: 14pt; border-bottom: 2px solid #007bff; padding-bottom: 8px; display: flex; align-items: center;">
                        <span style="margin-right: 8px;">👤</span> Bill To
                    </h3>
                    {% if invoice.student %}
                        <div style="margin-bottom: 12px;">
                            <p style="font-weight: bold; font-size: 13pt; color: #007bff; margin: 0 0 5px 0; text-transform: uppercase;">{{ invoice.student.get_full_name }}</p>
                            {% if invoice.student.admission_number %}
                                <p style="margin: 3px 0; font-size: 10pt;"><strong>Admission No:</strong> <span style="color: #495057;">{{ invoice.student.admission_number }}</span></p>
                            {% endif %}
                            {% if invoice.student.current_class %}
                                <p style="margin: 3px 0; font-size: 10pt;"><strong>Class:</strong> <span style="color: #495057;">{{ invoice.student.current_class.name }}{% if invoice.student.current_section %} - {{ invoice.student.current_section.name }}{% endif %}</span></p>
                            {% endif %}
                        </div>

                        {% with primary_parent=invoice.student.get_primary_parent %}
                            {% if primary_parent %}
                                <div style="margin-top: 15px; padding-top: 12px; border-top: 1px solid #dee2e6;">
                                    <p style="margin: 0 0 8px 0; font-size: 11pt; color: #6c757d; font-weight: bold;">Parent/Guardian Information</p>
                                    <p style="margin: 3px 0; font-size: 10pt;"><strong>Name:</strong> <span style="color: #495057;">{{ primary_parent.get_full_name }}</span></p>
                                    {% if primary_parent.phone_number %}
                                        <p style="margin: 3px 0; font-size: 10pt;"><strong>Phone:</strong> <span style="color: #495057;">{{ primary_parent.phone_number }}</span></p>
                                    {% endif %}
                                    {% if primary_parent.email %}
                                        <p style="margin: 3px 0; font-size: 10pt;"><strong>Email:</strong> <span style="color: #495057;">{{ primary_parent.email }}</span></p>
                                    {% endif %}
                                </div>
                            {% endif %}
                        {% endwith %}
                    {% else %}
                        <p style="color: #dc3545; font-style: italic;">Student information not available</p>
                    {% endif %}
                </div>
            </td>
            <td style="width: 40%; vertical-align: top;">
                <!-- Invoice Period Information -->
                <div style="background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%); padding: 20px; border: 2px solid #ffc107; border-radius: 8px; box-shadow: 0 2px 4px rgba(255,193,7,0.1);">
                    <h3 style="margin: 0 0 15px 0; color: #856404; font-size: 14pt; border-bottom: 2px solid #ffc107; padding-bottom: 8px; display: flex; align-items: center;">
                        <span style="margin-right: 8px;">📅</span> Invoice Period
                    </h3>
                    {% if invoice.academic_year %}
                        <p style="margin: 8px 0; font-size: 10pt;"><strong>Academic Year:</strong> <span style="color: #495057;">{{ invoice.academic_year.name }}</span></p>
                    {% endif %}
                    {% if invoice.term %}
                        <p style="margin: 8px 0; font-size: 10pt;"><strong>Term:</strong> <span style="color: #495057;">{{ invoice.term.name }}</span></p>
                    {% endif %}
                    {% if invoice.fee_structure %}
                        <p style="margin: 8px 0; font-size: 10pt;"><strong>Fee Structure:</strong> <span style="color: #495057;">{{ invoice.fee_structure.name }}</span></p>
                    {% endif %}
                    {% if not invoice.academic_year and not invoice.term and not invoice.fee_structure %}
                        <p style="color: #6c757d; font-style: italic; font-size: 9pt;">Period information not specified</p>
                    {% endif %}
                </div>
            </td>
        </tr>
    </table>

    <!-- Invoice Items Section -->
    <div style="margin-bottom: 25px;">
        <h3 style="color: #495057; margin-bottom: 15px; font-size: 16pt; display: flex; align-items: center;">
            <span style="margin-right: 10px;">💰</span> Invoice Items & Charges
        </h3>

        <table class="items-table" style="box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-radius: 8px; overflow: hidden;">
            <thead>
                <tr style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);">
                    <th style="width: 45%; color: white; font-weight: bold; padding: 12px 10px; font-size: 11pt;">Description</th>
                    <th class="text-center" style="width: 12%; color: white; font-weight: bold; padding: 12px 8px; font-size: 11pt;">Qty</th>
                    <th class="text-right" style="width: 18%; color: white; font-weight: bold; padding: 12px 10px; font-size: 11pt;">Unit Price</th>
                    <th class="text-right" style="width: 25%; color: white; font-weight: bold; padding: 12px 10px; font-size: 11pt;">Line Total</th>
                </tr>
            </thead>
            <tbody>
                {% for item in charge_items %}
                <tr style="{% cycle 'background-color: #ffffff;' 'background-color: #f8f9fa;' %} border-bottom: 1px solid #dee2e6;">
                    <td style="padding: 12px 10px; vertical-align: top;">
                        <div style="font-weight: bold; color: #495057; font-size: 10pt;">
                            {% if item.fee_head %}
                                {{ item.fee_head.name }}
                            {% else %}
                                {{ item.description }}
                            {% endif %}
                        </div>
                        {% if item.description and item.fee_head and item.description|lower != item.fee_head.name|lower %}
                            <div style="font-size: 8pt; color: #6c757d; margin-top: 3px; font-style: italic; line-height: 1.3;">
                                {{ item.description }}
                            </div>
                        {% endif %}
                    </td>
                    <td class="text-center" style="padding: 12px 8px; font-size: 10pt;">{{ item.quantity|floatformat:"-2"|default:"1" }}</td>
                    <td class="text-right" style="padding: 12px 10px; font-size: 10pt; font-family: 'Courier New', monospace;">{{ school_profile.currency_symbol|default:'$' }}{{ item.unit_price|floatformat:2|intcomma }}</td>
                    <td class="text-right" style="padding: 12px 10px; font-weight: bold; font-size: 10pt; color: #28a745; font-family: 'Courier New', monospace;">{{ school_profile.currency_symbol|default:'$' }}{{ item.amount|floatformat:2|intcomma }}</td>
                </tr>
                {% endfor %}

                {% if concession_lines %}
                    {% for item in concession_lines %}
                    <tr style="background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); border-bottom: 1px solid #b8daff; color: #0c5460;">
                        <td style="padding: 12px 10px; vertical-align: top;">
                            <div style="font-weight: bold; color: #0c5460; font-size: 10pt; display: flex; align-items: center;">
                                <span style="margin-right: 6px;">🎯</span>
                                {% if item.concession_type %}
                                    {{ item.concession_type.name }}
                                {% else %}
                                    {{ item.description }}
                                {% endif %}
                                <span style="margin-left: 8px; font-size: 8pt; background-color: #17a2b8; color: white; padding: 2px 6px; border-radius: 10px;">DISCOUNT</span>
                            </div>
                            {% if item.description and item.concession_type and item.description|lower != item.concession_type.name|lower %}
                                <div style="font-size: 8pt; color: #6c757d; margin-top: 3px; font-style: italic; line-height: 1.3;">
                                    {{ item.description }}
                                </div>
                            {% endif %}
                        </td>
                        <td class="text-center" style="padding: 12px 8px; font-size: 10pt; color: #6c757d;">-</td>
                        <td class="text-right" style="padding: 12px 10px; font-size: 10pt; color: #6c757d;">-</td>
                        <td class="text-right" style="padding: 12px 10px; font-weight: bold; font-size: 10pt; color: #dc3545; font-family: 'Courier New', monospace;">
                            ({{ school_profile.currency_symbol|default:'$' }}{{ item.amount|floatformat:2|intcomma }})
                        </td>
                    </tr>
                    {% endfor %}
                {% endif %}

            {% if not charge_items and not concession_lines %}
                <tr>
                    <td colspan="4" class="text-center" style="padding: 20px; color: #6c757d; font-style: italic;">
                        No billable items or concessions on this invoice.
                    </td>
                </tr>
            {% endif %}
        </tbody>
    </table>

        </table>
    </div>

    <!-- Summary Section -->
    <div style="margin-top: 30px;">
        <table class="summary-table" style="box-shadow: 0 4px 8px rgba(0,0,0,0.1); border-radius: 8px; overflow: hidden;">
            <tr style="background-color: #f8f9fa; border-bottom: 1px solid #dee2e6;">
                <td class="label" style="padding: 12px 15px; font-weight: bold; font-size: 11pt; color: #495057;">Subtotal:</td>
                <td class="value" style="padding: 12px 15px; font-size: 11pt; font-family: 'Courier New', monospace; color: #495057;">{{ school_profile.currency_symbol|default:'$' }}{{ display_subtotal|floatformat:2|intcomma }}</td>
            </tr>
            {% if display_total_concessions > 0 %}
            <tr style="background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); border-bottom: 1px solid #b8daff;">
                <td class="label" style="padding: 12px 15px; font-weight: bold; font-size: 11pt; color: #0c5460;">Total Discounts:</td>
                <td class="value" style="padding: 12px 15px; font-size: 11pt; color: #17a2b8; font-weight: bold; font-family: 'Courier New', monospace;">- {{ school_profile.currency_symbol|default:'$' }}{{ display_total_concessions|floatformat:2|intcomma }}</td>
            </tr>
            <tr style="background-color: #f8f9fa; border-bottom: 1px solid #dee2e6;">
                <td class="label" style="padding: 12px 15px; font-weight: bold; font-size: 11pt; color: #495057;">Net Billable Amount:</td>
                <td class="value" style="padding: 12px 15px; font-weight: bold; font-size: 11pt; font-family: 'Courier New', monospace; color: #495057;">{{ school_profile.currency_symbol|default:'$' }}{{ display_net_billable|floatformat:2|intcomma }}</td>
            </tr>
            {% endif %}
            {% if display_amount_paid > 0 %}
            <tr style="background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); border-bottom: 1px solid #b8e6c1;">
                <td class="label" style="padding: 12px 15px; font-weight: bold; font-size: 11pt; color: #155724;">Amount Paid:</td>
                <td class="value" style="padding: 12px 15px; font-size: 11pt; color: #28a745; font-weight: bold; font-family: 'Courier New', monospace;">- {{ school_profile.currency_symbol|default:'$' }}{{ display_amount_paid|floatformat:2|intcomma }}</td>
            </tr>
            {% endif %}
            <tr class="grand-total" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
                <td class="label" style="padding: 15px; font-weight: bold; font-size: 14pt; text-transform: uppercase; letter-spacing: 1px;">💰 Balance Due:</td>
                <td class="value" style="padding: 15px; font-weight: bold; font-size: 16pt; font-family: 'Courier New', monospace; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">{{ school_profile.currency_symbol|default:'$' }}{{ display_balance_due|floatformat:2|intcomma }}</td>
            </tr>
        </table>
    </div>

    <!-- Notes and Instructions Section -->
    {% if invoice.notes_to_parent or school_profile.payment_instructions %}
    <div style="margin-top: 30px;">
        {% if invoice.notes_to_parent %}
            <div class="notes" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-left: 5px solid #17a2b8; border-radius: 0 8px 8px 0; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h4 style="margin: 0 0 12px 0; color: #17a2b8; font-size: 13pt; display: flex; align-items: center;">
                    <span style="margin-right: 8px;">📝</span> Important Notes
                </h4>
                <div style="line-height: 1.6; font-size: 10pt; color: #495057;">
                    {{ invoice.notes_to_parent|linebreaksbr }}
                </div>
            </div>
        {% endif %}

        {% if school_profile.payment_instructions %}
            <div style="background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); padding: 20px; border-left: 5px solid #ffc107; border-radius: 0 8px 8px 0; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h4 style="margin: 0 0 12px 0; color: #856404; font-size: 13pt; display: flex; align-items: center;">
                    <span style="margin-right: 8px;">💳</span> Payment Instructions
                </h4>
                <div style="line-height: 1.6; font-size: 10pt; color: #495057;">
                    {{ school_profile.payment_instructions|linebreaksbr }}
                </div>
            </div>
        {% endif %}
    </div>
    {% endif %}

    <!-- Professional Footer -->
    <div style="margin-top: 40px; padding: 20px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-top: 3px solid #007bff; border-radius: 8px; text-align: center; box-shadow: 0 -2px 4px rgba(0,0,0,0.1);">
        <div style="margin-bottom: 15px;">
            <h4 style="margin: 0 0 8px 0; color: #007bff; font-size: 12pt;">
                {{ school_profile.school_name_on_reports|default:school_profile.school_name_override|default:tenant.name }}
            </h4>
            {% if school_profile.school_motto %}
                <p style="margin: 0 0 8px 0; font-style: italic; color: #6c757d; font-size: 9pt;">
                    "{{ school_profile.school_motto }}"
                </p>
            {% endif %}
        </div>

        <div style="border-top: 1px solid #dee2e6; padding-top: 15px;">
            <p style="margin: 0 0 8px 0; font-size: 10pt; color: #495057; font-weight: 500;">
                🎓 Thank you for your partnership in your child's education! 🎓
            </p>
            {% if invoice.due_date %}
                <p style="margin: 0; font-weight: bold; color: #dc3545; font-size: 10pt; background-color: #f8d7da; padding: 8px 12px; border-radius: 20px; display: inline-block;">
                    ⏰ Payment Due: {{ invoice.due_date|date:"F d, Y" }}
                </p>
            {% endif %}
        </div>
    </div>

{% endblock pdf_main_content %}
