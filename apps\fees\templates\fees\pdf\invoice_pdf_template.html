{# D:\school_fees_saas_v2\apps\fees\templates\fees\pdf\invoice_pdf_template.html #}
{% extends "reporting/pdf/_pdf_base.html" %}
{% load static humanize mptt_tags core_tags math_filters %} {# Ensure 'humanize' is loaded #}

{% block pdf_title %}Invoice #{{ invoice.invoice_number }} - {% if school_profile.school_name_on_reports %}{{ school_profile.school_name_on_reports }}{% elif school_profile.school_name_override %}{{ school_profile.school_name_override }}{% else %}{{ tenant.name }}{% endif %}{% endblock %}

{% block document_name_header %}INVOICE{% endblock %}

{% block document_specific_header_info %}
    {# This part usually contains Invoice No, Issue Date, Due Date from _pdf_base.html context #}
    {# Or you can override it here if needed for a different layout #}
    <p class="mb-0">Invoice No: <strong>{{ invoice.invoice_number }}</strong></p>
    <p class="mb-0">Issue Date: {{ invoice.issue_date|date:"d M Y" }}</p>
    {% if invoice.due_date %}<p class="mb-0">Due Date: <strong>{{ invoice.due_date|date:"d M Y" }}</strong></p>{% endif %}
{% endblock %}

{% block pdf_main_content %}
    {# Bill To Section (Assuming this is handled above or in _pdf_base.html, or add it here if needed) #}
    {# Example Bill To (if not in base): #}
    <table class="no-border" style="margin-bottom: 20px; width: 100%;">
        <tr>
            <td style="width: 50%; vertical-align: top;" class="no-border">
                <h4 class="fw-bold" style="margin-bottom: 5px;">Bill To:</h4>
                {% if invoice.student %}
                    <p class="mb-0"><strong>{{ invoice.student.full_name }}</strong></p>
                    {% if invoice.student.admission_number %}<p class="mb-0">Admission No: {{ invoice.student.admission_number }}</p>{% endif %}
                    {% if invoice.student.current_class %}
                        <p class="mb-0">Class: {{ invoice.student.current_class.name }}
                            {% if invoice.student.current_section %}- {{ invoice.student.current_section.name }}{% endif %}
                        </p>
                    {% endif %}
                    {% with parent_user=invoice.student.get_primary_parent %} {# Assuming a helper method #}
                        {% if parent_user %}
                            <p class="mb-0">Parent: {{ parent_user.full_name }}</p>
                            {% if parent_user.phone_number %}<p class="mb-0">Phone: {{ parent_user.phone_number }}</p>{% endif %}
                        {% endif %}
                    {% endwith %}
                {% else %}
                    <p>N/A</p>
                {% endif %}
            </td>
            <td style="width: 50%; text-align: right; vertical-align: top;" class="no-border">
                {# Optionally, School Address here if not in header_frame, or payment QR code, etc. #}
            </td>
        </tr>
    </table>

    {# Invoice Header Info (like Period, Fee Structure) - From your snippet #}
    <h4 class="fw-bold" style="margin-top: 15px; margin-bottom: 5px;">Invoice Details:</h4>
    {% if invoice.period_description %}<p class="mb-1">Period: {{ invoice.period_description }}</p>{% endif %}
    {% if invoice.fee_structure %}<p class="mb-1">Fee Structure: {{ invoice.fee_structure.name }}</p>{% endif %}
    {% if invoice.term %}
        <p class="mb-1">Term: {{ invoice.term.name }} ({{ invoice.academic_year.name }})</p>
    {% elif invoice.academic_year %}
        <p class="mb-1">Academic Year: {{ invoice.academic_year.name }}</p>
    {% endif %}

    {# Invoice Items Table - From your snippet #}
    <table style="margin-top: 10px; width: 100%;" class="table-items"> {# Added class for potential specific styling #}
        <thead style="background-color: #f2f2f2;">
            <tr>
                <th style="width: 55%; padding: 8px;">Description</th>
                <th class="text-end" style="width: 15%; padding: 8px;">Quantity</th>
                <th class="text-end" style="width: 15%; padding: 8px;">Unit Price</th>
                <th class="text-end" style="width: 15%; padding: 8px;">Line Total</th>
            </tr>
        </thead>
        <tbody>
            {# Billing Items (Non-Concession) #}
            {% for item in invoice.details.all %}
            {% if item.line_type == 'FEE_ITEM' %}
            <tr>
                <td style="padding: 6px;">
                    {% if item.fee_head %}
                        {{ item.fee_head.name }}
                        {% if item.description != item.fee_head.name %}<br><small>{{ item.description }}</small>{% endif %}
                    {% else %}
                        {{ item.description }}
                    {% endif %}
                </td>
                <td class="text-end" style="padding: 6px;">{{ item.quantity|floatformat:"-2"|intcomma|default:"1" }}</td>
                <td class="text-end" style="padding: 6px;">{{ item.unit_price|floatformat:2|intcomma }}</td>
                <td class="text-end" style="padding: 6px;">{{ item.amount|floatformat:2|intcomma }}</td>
            </tr>
            {% endif %}
            {% empty %}
                {# Show message if no line items at all #}
                <tr><td colspan="4" style="padding: 6px; text-align: center;">No billable items.</td></tr>
                {% endif %}
            {% endfor %}

            {# Subtotal (Sum of positive line_totals) #}
            {% if invoice.get_billing_items %}
            <tr class="fw-bold no-border-bottom">
                <td colspan="3" class="text-end no-border" style="padding: 6px;">Subtotal Before Discounts:</td>
                <td class="text-end no-border" style="padding: 6px;">{{ school_profile.currency_symbol|default:'$' }}{{ invoice.subtotal_before_discounts|floatformat:2|intcomma }}</td>
            </tr>
            {% endif %}

            {# Concession/Discount Items #}
            {% for item in invoice.get_concession_items %} {# Assuming a helper method on Invoice model #}
            <tr>
                <td style="padding: 6px;">{{ item.description|default:item.concession_type_applied.name|default:"Discount Applied" }}</td>
                <td class="text-end" style="padding: 6px;"></td> {# Usually no Qty/Price for overall discount line #}
                <td class="text-end" style="padding: 6px;"></td>
                <td class="text-end" style="padding: 6px;">({{ item.line_total|abs|floatformat:2|intcomma }})</td>
            </tr>
            {% endfor %}

            {# Total Discount Applied (if you have an aggregate property for this) #}
            {% if invoice.total_discount_amount > 0 %} {# Assuming invoice.total_discount_amount property #}
            <tr class="fw-bold no-border-bottom">
                <td colspan="3" class="text-end no-border" style="padding: 6px;">Total Discounts:</td>
                <td class="text-end no-border" style="padding: 6px;">({{ invoice.total_discount_amount|floatformat:2|intcomma }})</td>
            </tr>
            {% endif %}

            {# Net Amount Payable (if calculated distinct from total_amount) #}
            {# If your invoice.total_amount already IS the net amount after discounts, this isn't needed. #}
            {% if invoice.total_discount_amount > 0 %}
            <tr class="fw-bold" style="border-top: 1px solid #aaa;">
                <td colspan="3" class="text-end no-border" style="padding: 8px 6px;">Net Amount Payable:</td>
                <td class="text-end no-border" style="padding: 8px 6px;">{{ school_profile.currency_symbol|default:'$' }}{{ invoice.net_payable_after_discounts|floatformat:2|intcomma }}</td>
            </tr>
            {% endif %}

            {# Amount Paid #}
            {% if invoice.amount_paid > 0 %}
            <tr class="fw-bold">
                <td colspan="3" class="text-end no-border" style="padding: 6px;">Amount Paid:</td>
                <td class="text-end no-border text-success" style="padding: 6px;">{{ school_profile.currency_symbol|default:'$' }}{{ invoice.amount_paid|floatformat:2|intcomma }}</td>
            </tr>
            {% endif %}

            {# Amount Due - This is the final amount #}
            <tr class="fw-bold" style="background-color: #ddeeff; font-size: 1.1em; border-top: 2px solid #333;">
                <td colspan="3" class="text-end" style="padding: 8px 6px;">AMOUNT DUE:</td>
                <td class="text-end" style="padding: 8px 6px;">{{ school_profile.currency_symbol|default:'$' }}{{ invoice.balance_due|floatformat:2|intcomma }}</td>
            </tr>
        </tbody>
    </table>

    {% if invoice.notes %}
        <div style="margin-top: 20px; border-top: 1px dashed #ccc; padding-top: 10px;">
            <h4 class="fw-bold">Notes:</h4>
            <p>{{ invoice.notes|linebreaksbr }}</p>
        </div>
    {% endif %}

    {% if school_profile.payment_instructions %}
        <div style="margin-top: 20px; font-size: 8pt; border-top: 1px dashed #ccc; padding-top: 10px;">
            <h4 class="fw-bold">Payment Instructions:</h4>
            <p>{{ school_profile.payment_instructions|linebreaksbr }}</p>
        </div>
    {% endif %}
{% endblock pdf_main_content %}




























{% comment %} {# Invoice Items #}

{% extends "reporting/pdf/_pdf_base.html" %}

{% load static humanize mptt_tags core_tags math_filters %}

<h4 class="fw-bold">Invoice Details:</h4>
<p class="mb-1">Period: {{ invoice.period_description|default:"N/A" }}</p>
{% if invoice.fee_structure %}
    <p class="mb-1">Fee Structure: {{ invoice.fee_structure.name }}</p>
{% endif %}
{% if invoice.term %}
    <p class="mb-1">Term: {{ invoice.term.name }} ({{ invoice.academic_year.name }})</p>
{% elif invoice.academic_year %}
    <p class="mb-1">Academic Year: {{ invoice.academic_year.name }}</p>
{% endif %}


<table style="margin-top: 10px;">
    <thead style="background-color: #f2f2f2;">
        <tr>
            <th style="width: 60%;">Description</th>
            <th class="text-end" style="width: 20%;">Quantity</th>
            <th class="text-end" style="width: 20%;">Amount</th>
        </tr>
    </thead>
    <tbody>
        {# Billing Items #}
        {% for item in invoice.details.all %} {# Optimized in view with prefetch #}
            {% if not item.is_concession %}
            <tr>
                <td>{{ item.description|default:item.fee_head.name }}</td>
                <td class="text-end">{{ item.quantity|floatformat:0|intcomma|default:"1" }}</td>
                <td class="text-end">{{ item.line_total|floatformat:2|intcomma }}</td>
            </tr>
            {% endif %}
        {% endfor %}

        {# Subtotal before discounts #}
        <tr class="fw-bold">
            <td colspan="2" class="text-end no-border">Subtotal:</td>
            <td class="text-end no-border">{{ invoice.total_amount|floatformat:2|intcomma }}</td>
        </tr>

        {# Concession/Discount Items #}
        {% for item in invoice.details.all %}
            {% if item.is_concession %}
            <tr>
                <td>{{ item.description|default:item.concession_type_applied.name }}</td>
                <td class="text-end"></td> {# Concessions don't usually have quantity in this context #}
                <td class="text-end">({{ item.amount|abs|floatformat:2|intcomma }})</td> {# Show as negative/deduction #}
            </tr>
            {% endif %}
        {% endfor %}

        {% if invoice.discount_applied > 0 %}
        <tr class="fw-bold">
            <td colspan="2" class="text-end no-border">Total Discount Applied:</td>
            <td class="text-end no-border">({{ invoice.discount_applied|floatformat:2|intcomma }})</td>
        </tr>
        {% endif %}

        {# Net Amount After Discount (if different from Amount Due initially) #}
        {% if invoice.discount_applied > 0 %}
        <tr class="fw-bold" style="border-top: 1px solid #333;">
            <td colspan="2" class="text-end no-border">Net Amount Payable:</td>
            <td class="text-end no-border">{{ invoice.net_amount_payable|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}

        {# Amount Paid #}
        {% if invoice.amount_paid > 0 %}
        <tr class="fw-bold">
            <td colspan="2" class="text-end no-border">Amount Paid:</td>
            <td class="text-end no-border text-success">{{ invoice.amount_paid|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}

        {# Amount Due #}
        <tr class="fw-bold" style="background-color: #ddeeff; font-size: 1.1em; border-top: 2px solid #333;">
            <td colspan="2" class="text-end">AMOUNT DUE:</td>
            <td class="text-end">{{ school_profile.currency_symbol|default:'$' }}{{ invoice.amount_due|floatformat:2|intcomma }}</td>
        </tr>
    </tbody>
</table>

{% if invoice.notes %}
    <div style="margin-top: 20px; border-top: 1px dashed #ccc; padding-top: 10px;">
        <h4 class="fw-bold">Notes:</h4>
        <p>{{ invoice.notes|linebreaksbr }}</p>
    </div>
{% endif %}

{# Payment Instructions from SchoolProfile later #}
{% if school_profile.payment_instructions %}
    <div style="margin-top: 20px; font-size: 8pt;">
        <p class="fw-bold">Payment Instructions:</p>
        <p>{{ school_profile.payment_instructions|linebreaksbr }}</p>
    </div>
{% endif %}

{% endblock %} {% endcomment %}