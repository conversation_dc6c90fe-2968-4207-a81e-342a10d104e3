{# D:\school_fees_saas_v2\apps\fees\templates\fees\pdf\invoice_pdf_template.html #}
{% extends "reporting/pdf/_pdf_base.html" %}

{% load static humanize fees_tags core_tags math_filters %}

{% block pdf_title %}Invoice #{{ invoice.invoice_number|default:invoice.pk }} - {{ school_profile.school_name_on_reports|default:tenant.name }}{% endblock %}

{% block document_name_header %}INVOICE{% endblock %}

{% block document_specific_header_info %}
    <p style="margin:0; font-size:10pt;">Invoice No: <strong>{{ invoice.invoice_number|default:invoice.pk }}</strong></p>
    <p style="margin:0; font-size:10pt;">Status: {% invoice_status_badge invoice.status plain_text=True %}</p>
    <p style="margin:0; font-size:10pt;">Issue Date: {{ invoice.issue_date|date:"F d, Y" }}</p>
    {% if invoice.due_date %}<p style="margin:0; font-size:10pt;">Due Date: <strong style="color: #D9534F;">{{ invoice.due_date|date:"F d, Y" }}</strong></p>{% endif %}
{% endblock %}

{% block pdf_extra_styles %}
    {{ block.super }}
    <style type="text/css">
        /* Invoice specific styles can go here if not already in _pdf_base.html */
        .address-block p { margin: 1px 0; font-size:9pt; }
        .summary-table { width: 50%; margin-left: 50%; margin-top: 15px; border-collapse: collapse; font-size:9pt;}
        .summary-table td { padding: 5px 7px; border: 0.5px solid #cccccc; }
        .summary-table td.label { font-weight: bold; text-align: right; background-color: #F7F7F7; }
        .summary-table td.value { text-align: right; }
        .summary-table tr.grand-total td { font-weight: bold; font-size: 10pt; border-top: 1px solid #333333; }
        .notes-section { margin-top: 20px; padding-top:10px; border-top: 0.5px solid #eee; font-size:8pt; }
    </style>
{% endblock %}

{% block pdf_main_content %}
    {# Bill To / Term Info Table #}
    <table class="no-border" style="margin-bottom: 20px; width: 100%;">
        <tr>
            <td style="width: 55%; vertical-align: top;" class="no-border address-block">
                <h4 style="margin-bottom: 3px; font-size:10pt;">Bill To:</h4>
                {% if invoice.student %}
                    <p style="margin:0; font-weight: bold;">{{ invoice.student.get_full_name }}</p>
                    {% if invoice.student.admission_number %}<p style="margin:0;">Adm. No: {{ invoice.student.admission_number }}</p>{% endif %}
                    {% if invoice.student.current_class %}
                        <p style="margin:0;">Class: {{ invoice.student.current_class.name }}{% if invoice.student.current_section %} - {{ invoice.student.current_section.name }}{% endif %}</p>
                    {% endif %}
                    {% with primary_parent=invoice.student.get_primary_parent %}
                        {% if primary_parent %}
                            <p style="margin-top:3px; margin-bottom:0;">Attn: {{ primary_parent.get_full_name }}</p>
                            {% if primary_parent.phone_number %}<p style="margin:0;">Phone: {{ primary_parent.phone_number }}</p>{% endif %}
                        {% endif %}
                    {% endwith %}
                {% else %}
                    <p>N/A</p>
                {% endif %}
            </td>
            <td style="width: 45%; text-align: right; vertical-align: top;" class="no-border address-block">
                {% if invoice.term %}
                    <p style="margin:0;"><strong>Term:</strong> {{ invoice.term.name }}</p>
                {% endif %}
                {% if invoice.academic_year %}
                    <p style="margin:0;"><strong>Academic Year:</strong> {{ invoice.academic_year.name }}</p>
                {% endif %}
                {% if invoice.fee_structure %}
                    <p style="margin:0;"><strong>Fee Structure:</strong> {{ invoice.fee_structure.name }}</p>
                {% endif %}
            </td>
        </tr>
    </table>

    {# Invoice Items Table - using charge_items and concession_lines from view context #}
    <h3 style="font-size:11pt; margin-bottom:5px;">Invoice Items</h3>
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 50%;">Description</th>
                <th class="text-center" style="width: 10%;">Qty</th>
                <th class="text-end" style="width: 20%;">Unit Price</th>
                <th class="text-end" style="width: 20%;">Line Total</th>
            </tr>
        </thead>
        <tbody>
            {% for item in charge_items %}
            <tr>
                <td>
                    {% if item.fee_head %}
                        {{ item.fee_head.name }}
                        {% if item.description and item.description|lower != item.fee_head.name|lower %}<br><small style="color:#555;"><em>{{ item.description }}</em></small>{% endif %}
                    {% else %}
                        {{ item.description }}
                    {% endif %}
                </td>
                <td class="text-center">{{ item.quantity|floatformat:"-2"|default:"1" }}</td>
                <td class="text-end">{{ school_profile.currency_symbol|default:'$' }}{{ item.unit_price|floatformat:2|intcomma }}</td>
                <td class="text-end">{{ school_profile.currency_symbol|default:'$' }}{{ item.amount|floatformat:2|intcomma }}</td>
            </tr>
            {% endfor %}

            {% if concession_lines %}
                <tr><td colspan="4" class="no-border" style="padding:8px 0 3px 0; font-weight:bold;">Discounts / Concessions Applied:</td></tr>
                {% for item in concession_lines %}
                <tr>
                    <td>
                        {% if item.concession_type %}
                            {{ item.concession_type.name }}
                            {% if item.description and item.description|lower != item.concession_type.name|lower %}<br><small style="color:#555;"><em>{{ item.description }}</em></small>{% endif %}
                        {% else %}
                            {{ item.description }}
                        {% endif %}
                    </td>
                    <td class="text-center">-</td>
                    <td class="text-end"></td>
                    <td class="text-end">({{ school_profile.currency_symbol|default:'$' }}{{ item.amount|abs|floatformat:2|intcomma }})</td>
                </tr>
                {% endfor %}
            {% endif %}

            {% if not charge_items and not concession_lines %}
                <tr><td colspan="4" style="text-align: center; padding: 10px;">No billable items or concessions on this invoice.</td></tr>
            {% endif %}
        </tbody>
    </table>

    {# Summary Table - using display_ variables from context #}
    <table class="summary-table">
        <tr>
            <td class="label">Subtotal:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_subtotal|floatformat:2|intcomma }}</td>
        </tr>
        {% if display_total_concessions > 0 %}
        <tr>
            <td class="label">Total Discounts:</td>
            <td class="value">- {{ school_profile.currency_symbol|default:'$' }}{{ display_total_concessions|floatformat:2|intcomma }}</td>
        </tr>
        <tr style="border-top: 0.5px solid #999;">
            <td class="label" style="font-weight:bold;">Net Billable Amount:</td>
            <td class="value" style="font-weight:bold;">{{ school_profile.currency_symbol|default:'$' }}{{ display_net_billable|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}
        {% if display_amount_paid > 0 %}
        <tr class="no-border-bottom">
            <td class="label" style="color: #198754;">Amount Paid:</td>
            <td class="value" style="color: #198754;">- {{ school_profile.currency_symbol|default:'$' }}{{ display_amount_paid|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}
        <tr class="grand-total">
            <td class="label">BALANCE DUE:</td>
            <td class="value">{{ school_profile.currency_symbol|default:'$' }}{{ display_balance_due|floatformat:2|intcomma }}</td>
        </tr>
    </table>

    {% if invoice.notes_to_parent %}
        <div class="notes-section">
            <strong>Notes:</strong>
            <p>{{ invoice.notes_to_parent|linebreaksbr }}</p>
        </div>
    {% endif %}

    {% if school_profile.payment_instructions %}
        <div class="notes-section">
            <strong>Payment Instructions:</strong>
            <p>{{ school_profile.payment_instructions|linebreaksbr }}</p>
        </div>
    {% endif %}
{% endblock pdf_main_content %}

























{% comment %} {# D:\school_fees_saas_v2\apps\fees\templates\fees\pdf\invoice_pdf_template.html #}
{% extends "reporting/pdf/_pdf_base.html" %}

{% load static humanize core_tags math_filters fees_tags %} {# Ensure fees_tags contains invoice_status_badge #}

{% block pdf_title %}
    Invoice #{{ invoice.invoice_number|default:invoice.pk }} - {% if school_profile %}{{ school_profile.school_name_on_reports|default:tenant.name }}{% else %}{{ tenant.name }}{% endif %}
{% endblock pdf_title %}

{# Override document_name_header if _pdf_base.html uses it, or define it within the header frame content #}
{% block document_name_header %}INVOICE{% endblock %}

{# This block can be used within the header_frame if _pdf_base.html is set up for it #}
{% block pdf_header_content %}
    <table style="width: 100%; border-collapse: collapse;">
        <tr>
            <td style="width: 60%; vertical-align: top;">
                {% if school_profile.logo and school_profile.logo.path %}
                    {# For xhtml2pdf, using the file system path is most reliable for local images #}
                    <img src="{{ school_profile.logo.path }}" style="max-height: 70px; max-width: 200px;" alt="School Logo">
                {% elif school_profile %}
                    <h1 style="margin:0; font-size:20pt;">{{ school_profile.school_name_on_reports|default:tenant.name }}</h1>
                {% else %}
                    <h1 style="margin:0; font-size:20pt;">{{ tenant.name }}</h1>
                {% endif %}
                
                {# School Address from SchoolProfile #}
                <div style="font-size: 9pt; margin-top: 5px;">
                    {% if school_profile %}
                        {% if school_profile.address_line_1 %}{{ school_profile.address_line_1 }}<br>{% endif %}
                        {% if school_profile.address_line_2 %}{{ school_profile.address_line_2 }}<br>{% endif %}
                        {% if school_profile.city %}{{ school_profile.city }}{% endif %}{% if school_profile.city and school_profile.state_province %}, {% endif %}{% if school_profile.state_province %}{{ school_profile.state_province }}{% endif %} {% if school_profile.postal_code %}{{ school_profile.postal_code }}{% endif %}
                        {% if school_profile.country %}<br>{{ school_profile.country }}{% endif %}
                        {% if school_profile.phone_number %}<br>Phone: {{ school_profile.phone_number }}{% endif %}
                        {% if school_profile.school_email %}<br>Email: {{ school_profile.school_email }}{% endif %}
                    {% endif %}
                </div>
            </td>
            <td style="width: 40%; text-align: right; vertical-align: top;">
                <h1 style="margin:0 0 10px 0; font-size:28pt; text-transform: uppercase;">INVOICE</h1>
                <p style="margin:0; font-size:10pt;">Invoice No: <strong>{{ invoice.invoice_number|default:invoice.pk }}</strong></p>
                <p style="margin:0; font-size:10pt;">Status: {% invoice_status_badge invoice.status plain_text=True %}</p>
                <p style="margin:0; font-size:10pt;">Issue Date: {{ invoice.issue_date|date:"F d, Y" }}</p>
                {% if invoice.due_date %}<p style="margin:0; font-size:10pt;">Due Date: <strong style="color: #D9534F;">{{ invoice.due_date|date:"F d, Y" }}</strong></p>{% endif %}
            </td>
        </tr>
    </table>
{% endblock pdf_header_content %}


{% block pdf_main_content %}
    <table class="no-border" style="margin-top: 20px; margin-bottom: 25px; width: 100%;">
        <tr>
            <td style="width: 50%; vertical-align: top;" class="no-border address-block">
                <h3 style="margin-bottom: 5px; font-size:11pt; border-bottom: 1px solid #ccc; padding-bottom:2px;">Bill To:</h3>
                {% if invoice.student %}
                    <p style="margin:0; font-weight: bold;">{{ invoice.student.get_full_name }}</p>
                    {% if invoice.student.admission_number %}<p style="margin:0;">Adm. No: {{ invoice.student.admission_number }}</p>{% endif %}
                    {% if invoice.student.current_class %}
                        <p style="margin:0;">Class: {{ invoice.student.current_class.name }}{% if invoice.student.current_section %} - {{ invoice.student.current_section.name }}{% endif %}</p>
                    {% endif %}
                    
                    {# Attempt to get primary parent details #}
                    {% with primary_parent=invoice.student.get_primary_parent %} {# Assumes this method exists #}
                        {% if primary_parent %}
                            <p style="margin-top:5px; margin-bottom:0;">Attn: {{ primary_parent.get_full_name }}</p>
                            {% if primary_parent.phone_number %}<p style="margin:0;">Phone: {{ primary_parent.phone_number }}</p>{% endif %}
                            {% if primary_parent.email %}<p style="margin:0;">Email: {{ primary_parent.email }}</p>{% endif %}
                        {% endif %}
                    {% endwith %}
                {% else %}
                    <p>N/A</p>
                {% endif %}
            </td>
            <td style="width: 50%; text-align: right; vertical-align: top;" class="no-border">
                {% if invoice.term %}
                    <p style="margin:0;"><strong>Term:</strong> {{ invoice.term.name }}</p>
                {% endif %}
                {% if invoice.academic_year %}
                    <p style="margin:0;"><strong>Academic Year:</strong> {{ invoice.academic_year.name }}</p>
                {% endif %}
            </td>
        </tr>
    </table>

    <h3 style="font-size:12pt; margin-bottom:5px; border-bottom:1px solid #ccc; padding-bottom:2px;">Invoice Items</h3>
    <table style="width: 100%; border-collapse: collapse;" class="items-table">
        <thead style="background-color: #EFEFEF;">
            <tr>
                <th style="width: 50%; padding: 8px; text-align:left; border: 1px solid #ddd;">Description</th>
                <th style="width: 15%; padding: 8px; text-align:center; border: 1px solid #ddd;">Qty</th>
                <th style="width: 15%; padding: 8px; text-align:right; border: 1px solid #ddd;">Unit Price</th>
                <th style="width: 20%; padding: 8px; text-align:right; border: 1px solid #ddd;">Line Total</th>
            </tr>
        </thead>
        <tbody>
            {# Use charge_items and concession_lines from view context #}
            {% for item in charge_items %}
            <tr>
                <td style="padding: 6px; border: 1px solid #ddd;">
                    {% if item.fee_head %}
                        {{ item.fee_head.name }}
                        {% if item.description and item.description|lower != item.fee_head.name|lower %}<br><small style="color:#555;"><em>{{ item.description }}</em></small>{% endif %}
                    {% else %}
                        {{ item.description }}
                    {% endif %}
                </td>
                <td style="text-align:center; padding: 6px; border: 1px solid #ddd;">{{ item.quantity|floatformat:"-2"|default:"1" }}</td>
                <td style="text-align:right; padding: 6px; border: 1px solid #ddd;">{{ school_profile.currency_symbol|default:'$' }}{{ item.unit_price|floatformat:2|intcomma }}</td>
                <td style="text-align:right; padding: 6px; border: 1px solid #ddd;">{{ school_profile.currency_symbol|default:'$' }}{{ item.amount|floatformat:2|intcomma }}</td>
            </tr>
            {% endfor %}

            {% if not charge_items and not concession_lines %}
                <tr><td colspan="4" style="padding: 10px; text-align: center; border: 1px solid #ddd;">No items on this invoice.</td></tr>
            {% endif %}
        </tbody>
    </table>

    {# Summary Table - using display_ variables from context #}
    <table style="width: 50%; margin-left: 50%; margin-top: 15px; border-collapse: collapse; font-size:10pt;" class="summary-table">
        <tr>
            <td style="text-align:right; padding: 5px; font-weight:bold;">Subtotal:</td>
            <td style="text-align:right; padding: 5px;">{{ school_profile.currency_symbol|default:'$' }}{{ display_subtotal|floatformat:2|intcomma }}</td>
        </tr>
        {% if display_total_concessions > 0 %}
        <tr>
            <td style="text-align:right; padding: 5px;">Total Discounts:</td>
            <td style="text-align:right; padding: 5px;">- {{ school_profile.currency_symbol|default:'$' }}{{ display_total_concessions|floatformat:2|intcomma }}</td>
        </tr>
        <tr style="border-top: 1px solid #999;">
            <td style="text-align:right; padding: 5px; font-weight:bold;">Net Billable:</td>
            <td style="text-align:right; padding: 5px; font-weight:bold;">{{ school_profile.currency_symbol|default:'$' }}{{ display_net_billable|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}
        {% if display_amount_paid > 0 %}
        <tr>
            <td style="text-align:right; padding: 5px; color: #28a745;">Amount Paid:</td>
            <td style="text-align:right; padding: 5px; color: #28a745;">- {{ school_profile.currency_symbol|default:'$' }}{{ display_amount_paid|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}
        <tr style="background-color: #EFEFEF; font-weight: bold; font-size: 1.1em; border-top: 2px solid #333;">
            <td style="text-align:right; padding: 8px;">BALANCE DUE:</td>
            <td style="text-align:right; padding: 8px;">{{ school_profile.currency_symbol|default:'$' }}{{ display_balance_due|floatformat:2|intcomma }}</td>
        </tr>
    </table>

    {# Displaying Concession Items Separately if needed (Optional) #}
    {% if concession_lines %}
    <h3 style="font-size:11pt; margin-top:20px; margin-bottom:5px; border-bottom:1px solid #ccc; padding-bottom:2px;">Applied Discounts/Concessions</h3>
    <table style="width: 100%; border-collapse: collapse; font-size:9pt;" class="items-table">
        <thead>
            <tr>
                <th style="width: 80%; padding: 6px; text-align:left; border: 1px solid #ddd;">Description</th>
                <th style="width: 20%; padding: 6px; text-align:right; border: 1px solid #ddd;">Amount</th>
            </tr>
        </thead>
        <tbody>
            {% for item in concession_lines %}
            <tr>
                <td style="padding: 5px; border: 1px solid #ddd;">
                    {% if item.concession_type %}
                        {{ item.concession_type.name }}
                        {% if item.description and item.description|lower != item.concession_type.name|lower %}<br><small style="color:#555;"><em>{{ item.description }}</em></small>{% endif %}
                    {% else %}
                        {{ item.description }}
                    {% endif %}
                </td>
                <td style="text-align:right; padding: 5px; border: 1px solid #ddd;">
                    {{ school_profile.currency_symbol|default:'$' }}{{ item.amount|floatformat:2|intcomma }} {# Should be negative #}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% endif %}


    {% if invoice.notes_to_parent %}
        <div style="margin-top: 25px; padding-top:10px; border-top: 1px solid #eee; font-size:9pt;">
            <strong>Notes:</strong><br>
            <p style="white-space: pre-wrap;">{{ invoice.notes_to_parent }}</p>
        </div>
    {% endif %}

    {% if school_profile.payment_instructions %}
        <div style="margin-top: 20px; padding-top:10px; border-top: 1px solid #eee; font-size:9pt;">
            <strong>Payment Instructions:</strong>
            <p style="white-space: pre-wrap;">{{ school_profile.payment_instructions|linebreaksbr }}</p>
        </div>
    {% endif %}
{% endblock pdf_main_content %}

{% block pdf_footer_content %}
    <p style="text-align:center; font-size:8pt; color:#666;">
        {{ school_profile.school_name_on_reports|default:tenant.name }} - Thank you for your partnership.
        {% if school_profile.website %}| {{ school_profile.website }} {% endif %}
    </p>
    <p style="text-align:center; font-size:8pt; color:#666;">
        Page <pdf:pagenumber /> of <pdf:pagecount />
    </p>
{% endblock pdf_footer_content %}


 {% endcomment %}















{% comment %} {# D:\school_fees_saas_v2\apps\fees\templates\fees\pdf\invoice_pdf_template.html #}
{% extends "reporting/pdf/_pdf_base.html" %}
{% load static humanize mptt_tags core_tags math_filters %} {# Ensure 'humanize' is loaded #}

{% block pdf_title %}Invoice #{{ invoice.invoice_number }} - {% if school_profile.school_name_on_reports %}{{ school_profile.school_name_on_reports }}{% elif school_profile.school_name_override %}{{ school_profile.school_name_override }}{% else %}{{ tenant.name }}{% endif %}{% endblock %}

{% block document_name_header %}INVOICE{% endblock %}

{% block document_specific_header_info %}
    {# This part usually contains Invoice No, Issue Date, Due Date from _pdf_base.html context #}
    {# Or you can override it here if needed for a different layout #}
    <p class="mb-0">Invoice No: <strong>{{ invoice.invoice_number }}</strong></p>
    <p class="mb-0">Issue Date: {{ invoice.issue_date|date:"d M Y" }}</p>
    {% if invoice.due_date %}<p class="mb-0">Due Date: <strong>{{ invoice.due_date|date:"d M Y" }}</strong></p>{% endif %}
{% endblock %}

{% block pdf_main_content %}
    {# Bill To Section (Assuming this is handled above or in _pdf_base.html, or add it here if needed) #}
    {# Example Bill To (if not in base): #}
    <table class="no-border" style="margin-bottom: 20px; width: 100%;">
        <tr>
            <td style="width: 50%; vertical-align: top;" class="no-border">
                <h4 class="fw-bold" style="margin-bottom: 5px;">Bill To:</h4>
                {% if invoice.student %}
                    <p class="mb-0"><strong>{{ invoice.student.full_name }}</strong></p>
                    {% if invoice.student.admission_number %}<p class="mb-0">Admission No: {{ invoice.student.admission_number }}</p>{% endif %}
                    {% if invoice.student.current_class %}
                        <p class="mb-0">Class: {{ invoice.student.current_class.name }}
                            {% if invoice.student.current_section %}- {{ invoice.student.current_section.name }}{% endif %}
                        </p>
                    {% endif %}
                    {% with parent_user=invoice.student.get_primary_parent %} {# Assuming a helper method #}
                        {% if parent_user %}
                            <p class="mb-0">Parent: {{ parent_user.full_name }}</p>
                            {% if parent_user.phone_number %}<p class="mb-0">Phone: {{ parent_user.phone_number }}</p>{% endif %}
                        {% endif %}
                    {% endwith %}
                {% else %}
                    <p>N/A</p>
                {% endif %}
            </td>
            <td style="width: 50%; text-align: right; vertical-align: top;" class="no-border">
                {# Optionally, School Address here if not in header_frame, or payment QR code, etc. #}
            </td>
        </tr>
    </table>

    {# Invoice Header Info (like Period, Fee Structure) - From your snippet #}
    <h4 class="fw-bold" style="margin-top: 15px; margin-bottom: 5px;">Invoice Details:</h4>
    {% if invoice.period_description %}<p class="mb-1">Period: {{ invoice.period_description }}</p>{% endif %}
    {% if invoice.fee_structure %}<p class="mb-1">Fee Structure: {{ invoice.fee_structure.name }}</p>{% endif %}
    {% if invoice.term %}
        <p class="mb-1">Term: {{ invoice.term.name }} ({{ invoice.academic_year.name }})</p>
    {% elif invoice.academic_year %}
        <p class="mb-1">Academic Year: {{ invoice.academic_year.name }}</p>
    {% endif %}

    {# Invoice Items Table - From your snippet #}
    <table style="margin-top: 10px; width: 100%;" class="table-items"> {# Added class for potential specific styling #}
        <thead style="background-color: #f2f2f2;">
            <tr>
                <th style="width: 55%; padding: 8px;">Description</th>
                <th class="text-end" style="width: 15%; padding: 8px;">Quantity</th>
                <th class="text-end" style="width: 15%; padding: 8px;">Unit Price</th>
                <th class="text-end" style="width: 15%; padding: 8px;">Line Total</th>
            </tr>
        </thead>
        <tbody>
            {# Billing Items (Non-Concession) #}
            {% for item in invoice.details.all %}
            {% if item.line_type == 'FEE_ITEM' %}
            <tr>
                <td style="padding: 6px;">
                    {% if item.fee_head %}
                        {{ item.fee_head.name }}
                        {% if item.description != item.fee_head.name %}<br><small>{{ item.description }}</small>{% endif %}
                    {% else %}
                        {{ item.description }}
                    {% endif %}
                </td>
                <td class="text-end" style="padding: 6px;">{{ item.quantity|floatformat:"-2"|intcomma|default:"1" }}</td>
                <td class="text-end" style="padding: 6px;">{{ item.unit_price|floatformat:2|intcomma }}</td>
                <td class="text-end" style="padding: 6px;">{{ item.amount|floatformat:2|intcomma }}</td>
            </tr>
            {% endif %}
            {% empty %}
                {# Show message if no line items at all #}
                <tr><td colspan="4" style="padding: 6px; text-align: center;">No billable items.</td></tr>
                {% endif %}
            {% endfor %}

            {# Subtotal (Sum of positive line_totals) #}
            {% if invoice.get_billing_items %}
            <tr class="fw-bold no-border-bottom">
                <td colspan="3" class="text-end no-border" style="padding: 6px;">Subtotal Before Discounts:</td>
                <td class="text-end no-border" style="padding: 6px;">{{ school_profile.currency_symbol|default:'$' }}{{ invoice.subtotal_before_discounts|floatformat:2|intcomma }}</td>
            </tr>
            {% endif %}

            {# Concession/Discount Items #}
            {% for item in invoice.get_concession_items %} {# Assuming a helper method on Invoice model #}
            <tr>
                <td style="padding: 6px;">{{ item.description|default:item.concession_type_applied.name|default:"Discount Applied" }}</td>
                <td class="text-end" style="padding: 6px;"></td> {# Usually no Qty/Price for overall discount line #}
                <td class="text-end" style="padding: 6px;"></td>
                <td class="text-end" style="padding: 6px;">({{ item.line_total|abs|floatformat:2|intcomma }})</td>
            </tr>
            {% endfor %}

            {# Total Discount Applied (if you have an aggregate property for this) #}
            {% if invoice.total_discount_amount > 0 %} {# Assuming invoice.total_discount_amount property #}
            <tr class="fw-bold no-border-bottom">
                <td colspan="3" class="text-end no-border" style="padding: 6px;">Total Discounts:</td>
                <td class="text-end no-border" style="padding: 6px;">({{ invoice.total_discount_amount|floatformat:2|intcomma }})</td>
            </tr>
            {% endif %}

            {# Net Amount Payable (if calculated distinct from total_amount) #}
            {# If your invoice.total_amount already IS the net amount after discounts, this isn't needed. #}
            {% if invoice.total_discount_amount > 0 %}
            <tr class="fw-bold" style="border-top: 1px solid #aaa;">
                <td colspan="3" class="text-end no-border" style="padding: 8px 6px;">Net Amount Payable:</td>
                <td class="text-end no-border" style="padding: 8px 6px;">{{ school_profile.currency_symbol|default:'$' }}{{ invoice.net_payable_after_discounts|floatformat:2|intcomma }}</td>
            </tr>
            {% endif %}

            {# Amount Paid #}
            {% if invoice.amount_paid > 0 %}
            <tr class="fw-bold">
                <td colspan="3" class="text-end no-border" style="padding: 6px;">Amount Paid:</td>
                <td class="text-end no-border text-success" style="padding: 6px;">{{ school_profile.currency_symbol|default:'$' }}{{ invoice.amount_paid|floatformat:2|intcomma }}</td>
            </tr>
            {% endif %}

            {# Amount Due - This is the final amount #}
            <tr class="fw-bold" style="background-color: #ddeeff; font-size: 1.1em; border-top: 2px solid #333;">
                <td colspan="3" class="text-end" style="padding: 8px 6px;">AMOUNT DUE:</td>
                <td class="text-end" style="padding: 8px 6px;">{{ school_profile.currency_symbol|default:'$' }}{{ invoice.balance_due|floatformat:2|intcomma }}</td>
            </tr>
        </tbody>
    </table>

    {% if invoice.notes %}
        <div style="margin-top: 20px; border-top: 1px dashed #ccc; padding-top: 10px;">
            <h4 class="fw-bold">Notes:</h4>
            <p>{{ invoice.notes|linebreaksbr }}</p>
        </div>
    {% endif %}

    {% if school_profile.payment_instructions %}
        <div style="margin-top: 20px; font-size: 8pt; border-top: 1px dashed #ccc; padding-top: 10px;">
            <h4 class="fw-bold">Payment Instructions:</h4>
            <p>{{ school_profile.payment_instructions|linebreaksbr }}</p>
        </div>
    {% endif %}
{% endblock pdf_main_content %}


 {% endcomment %}

























{% comment %} {# Invoice Items #}

{% extends "reporting/pdf/_pdf_base.html" %}

{% load static humanize mptt_tags core_tags math_filters %}

<h4 class="fw-bold">Invoice Details:</h4>
<p class="mb-1">Period: {{ invoice.period_description|default:"N/A" }}</p>
{% if invoice.fee_structure %}
    <p class="mb-1">Fee Structure: {{ invoice.fee_structure.name }}</p>
{% endif %}
{% if invoice.term %}
    <p class="mb-1">Term: {{ invoice.term.name }} ({{ invoice.academic_year.name }})</p>
{% elif invoice.academic_year %}
    <p class="mb-1">Academic Year: {{ invoice.academic_year.name }}</p>
{% endif %}


<table style="margin-top: 10px;">
    <thead style="background-color: #f2f2f2;">
        <tr>
            <th style="width: 60%;">Description</th>
            <th class="text-end" style="width: 20%;">Quantity</th>
            <th class="text-end" style="width: 20%;">Amount</th>
        </tr>
    </thead>
    <tbody>
        {# Billing Items #}
        {% for item in invoice.details.all %} {# Optimized in view with prefetch #}
            {% if not item.is_concession %}
            <tr>
                <td>{{ item.description|default:item.fee_head.name }}</td>
                <td class="text-end">{{ item.quantity|floatformat:0|intcomma|default:"1" }}</td>
                <td class="text-end">{{ item.line_total|floatformat:2|intcomma }}</td>
            </tr>
            {% endif %}
        {% endfor %}

        {# Subtotal before discounts #}
        <tr class="fw-bold">
            <td colspan="2" class="text-end no-border">Subtotal:</td>
            <td class="text-end no-border">{{ invoice.total_amount|floatformat:2|intcomma }}</td>
        </tr>

        {# Concession/Discount Items #}
        {% for item in invoice.details.all %}
            {% if item.is_concession %}
            <tr>
                <td>{{ item.description|default:item.concession_type_applied.name }}</td>
                <td class="text-end"></td> {# Concessions don't usually have quantity in this context #}
                <td class="text-end">({{ item.amount|abs|floatformat:2|intcomma }})</td> {# Show as negative/deduction #}
            </tr>
            {% endif %}
        {% endfor %}

        {% if invoice.discount_applied > 0 %}
        <tr class="fw-bold">
            <td colspan="2" class="text-end no-border">Total Discount Applied:</td>
            <td class="text-end no-border">({{ invoice.discount_applied|floatformat:2|intcomma }})</td>
        </tr>
        {% endif %}

        {# Net Amount After Discount (if different from Amount Due initially) #}
        {% if invoice.discount_applied > 0 %}
        <tr class="fw-bold" style="border-top: 1px solid #333;">
            <td colspan="2" class="text-end no-border">Net Amount Payable:</td>
            <td class="text-end no-border">{{ invoice.net_amount_payable|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}

        {# Amount Paid #}
        {% if invoice.amount_paid > 0 %}
        <tr class="fw-bold">
            <td colspan="2" class="text-end no-border">Amount Paid:</td>
            <td class="text-end no-border text-success">{{ invoice.amount_paid|floatformat:2|intcomma }}</td>
        </tr>
        {% endif %}

        {# Amount Due #}
        <tr class="fw-bold" style="background-color: #ddeeff; font-size: 1.1em; border-top: 2px solid #333;">
            <td colspan="2" class="text-end">AMOUNT DUE:</td>
            <td class="text-end">{{ school_profile.currency_symbol|default:'$' }}{{ invoice.amount_due|floatformat:2|intcomma }}</td>
        </tr>
    </tbody>
</table>

{% if invoice.notes %}
    <div style="margin-top: 20px; border-top: 1px dashed #ccc; padding-top: 10px;">
        <h4 class="fw-bold">Notes:</h4>
        <p>{{ invoice.notes|linebreaksbr }}</p>
    </div>
{% endif %}

{# Payment Instructions from SchoolProfile later #}
{% if school_profile.payment_instructions %}
    <div style="margin-top: 20px; font-size: 8pt;">
        <p class="fw-bold">Payment Instructions:</p>
        <p>{{ school_profile.payment_instructions|linebreaksbr }}</p>
    </div>
{% endif %}

{% endblock %} {% endcomment %}