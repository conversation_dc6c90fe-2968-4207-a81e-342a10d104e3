{# Professional Invoice PDF Template #}
{% extends "reporting/pdf/_pdf_base.html" %}

{% load static humanize fees_tags core_tags math_filters %}

{% block pdf_title %}Invoice #{{ invoice.invoice_number_display }} - {{ school_profile.school_name_on_reports|default:tenant.name }}{% endblock %}

{% block document_name_header %}INVOICE{% endblock %}

{% block document_specific_header_info %}
    <div style="background-color: #f0f8ff; padding: 10px; border: 2px solid #007bff; margin-top: 5px;">
        <p style="margin:0; font-size:12pt; font-weight: bold; color: #007bff;">Invoice No: {{ invoice.invoice_number_display }}</p>
        <p style="margin:3px 0 0 0; font-size:10pt;">Status: <strong style="{% if invoice.status == 'PAID' %}color: #28a745;{% elif invoice.status == 'OVERDUE' %}color: #dc3545;{% elif invoice.status == 'SENT' %}color: #007bff;{% else %}color: #6c757d;{% endif %}">{{ invoice.get_status_display }}</strong></p>
        <p style="margin:3px 0 0 0; font-size:10pt;">Issue Date: <strong>{{ invoice.issue_date|date:"F d, Y" }}</strong></p>
        {% if invoice.due_date %}<p style="margin:3px 0 0 0; font-size:10pt;">Due Date: <strong style="color: #dc3545;">{{ invoice.due_date|date:"F d, Y" }}</strong></p>{% endif %}
    </div>
{% endblock %}

{% block pdf_extra_styles %}
    {{ block.super }}
    <style type="text/css">
        /* Professional Invoice Styling */
        .invoice-section {
            margin-bottom: 20px;
        }
        
        .section-header {
            background-color: #f8f9fa;
            padding: 8px 12px;
            margin-bottom: 10px;
            border-left: 4px solid #007bff;
            font-weight: bold;
            font-size: 11pt;
            color: #495057;
        }
        
        .bill-to-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        
        .bill-to-section h4 {
            margin: 0 0 8px 0;
            color: #495057;
            font-size: 11pt;
            border-bottom: 2px solid #007bff;
            padding-bottom: 4px;
        }
        
        .bill-to-section p {
            margin: 3px 0;
            font-size: 9pt;
            line-height: 1.4;
        }
        
        .period-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            font-size: 9pt;
        }
        
        .period-info p {
            margin: 2px 0;
        }
        
        /* Enhanced Items Table - Override base styles */
        table.items-table {
            border: 2px solid #007bff !important;
            border-collapse: collapse !important;
            width: 100% !important;
            margin-top: 10px !important;
            margin-bottom: 20px !important;
        }

        table.items-table thead th {
            background-color: #007bff !important;
            color: white !important;
            font-weight: bold !important;
            padding: 10px 8px !important;
            font-size: 10pt !important;
            border: 1px solid #007bff !important;
        }

        table.items-table tbody td {
            padding: 8px !important;
            border: 1px solid #dee2e6 !important;
            font-size: 9pt !important;
            vertical-align: top !important;
        }

        table.items-table tbody tr:nth-child(even) {
            background-color: #f8f9fa !important;
        }
        
        .concession-header {
            background-color: #d1ecf1;
            color: #0c5460;
            font-weight: bold;
            font-style: italic;
        }
        
        .concession-row td {
            background-color: #f0f9ff;
            color: #0c5460;
        }
        
        /* Professional Summary Table */
        .summary-table {
            width: 55%;
            margin-left: 45%;
            margin-top: 20px;
            border-collapse: collapse;
            font-size: 10pt;
            border: 1px solid #dee2e6;
        }
        
        .summary-table td {
            padding: 8px 12px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .summary-table td.label {
            font-weight: bold;
            text-align: right;
            background-color: #f8f9fa;
            color: #495057;
            width: 60%;
        }
        
        .summary-table td.value {
            text-align: right;
            background-color: white;
            font-weight: 500;
            width: 40%;
        }
        
        .summary-table tr.subtotal td {
            border-top: 2px solid #007bff;
        }
        
        .summary-table tr.grand-total td {
            background-color: #28a745;
            color: white;
            font-weight: bold;
            font-size: 12pt;
            border: 1px solid #28a745;
        }
        
        .summary-table tr.paid-amount td {
            background-color: #d4edda;
            color: #155724;
            font-weight: bold;
        }
        
        /* Notes and Instructions */
        .notes-section {
            margin-top: 25px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #17a2b8;
            border-radius: 0 4px 4px 0;
            font-size: 9pt;
        }
        
        .notes-section h4 {
            margin: 0 0 8px 0;
            color: #17a2b8;
            font-size: 11pt;
        }
        
        .payment-instructions {
            margin-top: 20px;
            padding: 15px;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            font-size: 9pt;
        }
        
        .payment-instructions h4 {
            margin: 0 0 8px 0;
            color: #856404;
            font-size: 11pt;
        }
        
        /* Utility Classes */
        .highlight-amount {
            color: #28a745;
            font-weight: bold;
        }
        
        .overdue-amount {
            color: #dc3545;
            font-weight: bold;
        }
        
        .currency {
            font-family: "Courier New", monospace;
        }
    </style>
{% endblock %}

{% block pdf_main_content %}
    {# Bill To Section #}
    <div class="invoice-section">
        <table class="no-border" style="width: 100%;">
            <tr>
                <td style="width: 60%; vertical-align: top;" class="no-border">
                    <div style="background-color: #f8f9fa; padding: 15px; border: 1px solid #dee2e6;">
                        <h4 style="margin: 0 0 10px 0; color: #495057; font-size: 11pt; border-bottom: 2px solid #007bff; padding-bottom: 4px;">Bill To</h4>
                        {% if invoice.student %}
                            <p style="font-weight: bold; font-size: 11pt; color: #007bff; margin: 0 0 5px 0;">{{ invoice.student.get_full_name }}</p>
                            {% if invoice.student.admission_number %}
                                <p style="margin: 3px 0; font-size: 9pt;"><strong>Admission No:</strong> {{ invoice.student.admission_number }}</p>
                            {% endif %}
                            {% if invoice.student.current_class %}
                                <p style="margin: 3px 0; font-size: 9pt;"><strong>Class:</strong> {{ invoice.student.current_class.name }}{% if invoice.student.current_section %} - {{ invoice.student.current_section.name }}{% endif %}</p>
                            {% endif %}
                            {% with primary_parent=invoice.student.get_primary_parent %}
                                {% if primary_parent %}
                                    <p style="margin: 8px 0 3px 0; padding-top: 8px; border-top: 1px solid #dee2e6; font-size: 9pt;">
                                        <strong>Parent/Guardian:</strong> {{ primary_parent.get_full_name }}
                                    </p>
                                    {% if primary_parent.phone_number %}
                                        <p style="margin: 3px 0; font-size: 9pt;"><strong>Phone:</strong> {{ primary_parent.phone_number }}</p>
                                    {% endif %}
                                    {% if primary_parent.email %}
                                        <p style="margin: 3px 0; font-size: 9pt;"><strong>Email:</strong> {{ primary_parent.email }}</p>
                                    {% endif %}
                                {% endif %}
                            {% endwith %}
                        {% else %}
                            <p style="color: #dc3545; margin: 0;">Student information not available</p>
                        {% endif %}
                    </div>
                </td>
                <td style="width: 40%; vertical-align: top;" class="no-border">
                    <div class="period-info">
                        <h4 style="margin: 0 0 8px 0; color: #856404; font-size: 10pt;">📅 Invoice Period</h4>
                        {% if invoice.academic_year %}
                            <p><strong>Academic Year:</strong> {{ invoice.academic_year.name }}</p>
                        {% endif %}
                        {% if invoice.term %}
                            <p><strong>Term:</strong> {{ invoice.term.name }}</p>
                        {% endif %}
                        {% if invoice.fee_structure %}
                            <p><strong>Fee Structure:</strong> {{ invoice.fee_structure.name }}</p>
                        {% endif %}
                        {% if not invoice.academic_year and not invoice.term %}
                            <p style="color: #6c757d; font-style: italic;">Period information not specified</p>
                        {% endif %}
                    </div>
                </td>
            </tr>
        </table>
    </div>

    {# Invoice Items Section #}
    <div class="invoice-section">
        <div class="section-header">
            💰 Invoice Items & Charges
        </div>
        
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 45%;">Description</th>
                    <th class="text-center" style="width: 12%;">Quantity</th>
                    <th class="text-end" style="width: 18%;">Unit Price</th>
                    <th class="text-end" style="width: 25%;">Line Total</th>
                </tr>
            </thead>
            <tbody>
                {% for item in charge_items %}
                <tr>
                    <td>
                        <div style="font-weight: bold; color: #495057;">
                            {% if item.fee_head %}
                                {{ item.fee_head.name }}
                            {% else %}
                                {{ item.description }}
                            {% endif %}
                        </div>
                        {% if item.description and item.fee_head and item.description|lower != item.fee_head.name|lower %}
                            <div style="font-size: 8pt; color: #6c757d; margin-top: 2px; font-style: italic;">
                                {{ item.description }}
                            </div>
                        {% endif %}
                    </td>
                    <td class="text-center">{{ item.quantity|floatformat:"-2"|default:"1" }}</td>
                    <td class="text-end currency">{{ school_profile.currency_symbol|default:'$' }}{{ item.unit_price|floatformat:2|intcomma }}</td>
                    <td class="text-end currency" style="font-weight: bold;">{{ school_profile.currency_symbol|default:'$' }}{{ item.amount|floatformat:2|intcomma }}</td>
                </tr>
                {% endfor %}

                {% if concession_lines %}
                    <tr class="concession-header">
                        <td colspan="4" style="padding: 8px; font-size: 10pt;">
                            🎯 Applied Discounts & Concessions
                        </td>
                    </tr>
                    {% for item in concession_lines %}
                    <tr class="concession-row">
                        <td>
                            <div style="font-weight: bold;">
                                {% if item.concession_type %}
                                    {{ item.concession_type.name }}
                                {% else %}
                                    {{ item.description }}
                                {% endif %}
                            </div>
                            {% if item.description and item.concession_type and item.description|lower != item.concession_type.name|lower %}
                                <div style="font-size: 8pt; color: #0c5460; margin-top: 2px; font-style: italic;">
                                    {{ item.description }}
                                </div>
                            {% endif %}
                        </td>
                        <td class="text-center">-</td>
                        <td class="text-end"></td>
                        <td class="text-end currency" style="font-weight: bold;">
                            ({{ school_profile.currency_symbol|default:'$' }}{{ item.amount|abs|floatformat:2|intcomma }})
                        </td>
                    </tr>
                    {% endfor %}
                {% endif %}

                {% if not charge_items and not concession_lines %}
                    <tr>
                        <td colspan="4" style="text-align: center; padding: 20px; color: #6c757d; font-style: italic;">
                            No billable items or concessions on this invoice.
                        </td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>

    {# Professional Summary Table #}
    <div class="invoice-section">
        <table class="summary-table">
            <tr class="subtotal">
                <td class="label">Subtotal:</td>
                <td class="value currency">{{ school_profile.currency_symbol|default:'$' }}{{ display_subtotal|floatformat:2|intcomma }}</td>
            </tr>
            {% if display_total_concessions > 0 %}
            <tr>
                <td class="label">Total Discounts:</td>
                <td class="value currency" style="color: #17a2b8;">- {{ school_profile.currency_symbol|default:'$' }}{{ display_total_concessions|floatformat:2|intcomma }}</td>
            </tr>
            <tr style="border-top: 2px solid #007bff;">
                <td class="label">Net Billable Amount:</td>
                <td class="value currency" style="font-weight: bold;">{{ school_profile.currency_symbol|default:'$' }}{{ display_net_billable|floatformat:2|intcomma }}</td>
            </tr>
            {% endif %}
            {% if display_amount_paid > 0 %}
            <tr class="paid-amount">
                <td class="label">Amount Paid:</td>
                <td class="value currency">- {{ school_profile.currency_symbol|default:'$' }}{{ display_amount_paid|floatformat:2|intcomma }}</td>
            </tr>
            {% endif %}
            <tr class="grand-total">
                <td class="label">💳 BALANCE DUE:</td>
                <td class="value currency">{{ school_profile.currency_symbol|default:'$' }}{{ display_balance_due|floatformat:2|intcomma }}</td>
            </tr>
        </table>
    </div>

    {# Notes Section #}
    {% if invoice.notes_to_parent %}
        <div class="notes-section">
            <h4>📝 Important Notes</h4>
            <div style="line-height: 1.5;">
                {{ invoice.notes_to_parent|linebreaksbr }}
            </div>
        </div>
    {% endif %}

    {# Payment Instructions #}
    {% if school_profile.payment_instructions %}
        <div class="payment-instructions">
            <h4>💳 Payment Instructions</h4>
            <div style="line-height: 1.5;">
                {{ school_profile.payment_instructions|linebreaksbr }}
            </div>
        </div>
    {% endif %}

    {# Footer Information #}
    <div style="margin-top: 30px; padding-top: 15px; border-top: 2px solid #007bff; text-align: center; font-size: 8pt; color: #6c757d;">
        <p style="margin: 0;">
            <strong>{{ school_profile.school_name_on_reports|default:tenant.name }}</strong>
            {% if school_profile.website %} | {{ school_profile.website }}{% endif %}
        </p>
        <p style="margin: 5px 0 0 0;">
            Thank you for your partnership in your child's education.
        </p>
        {% if invoice.due_date %}
            <p style="margin: 5px 0 0 0; font-weight: bold; color: #dc3545;">
                Please ensure payment is made by {{ invoice.due_date|date:"F d, Y" }}
            </p>
        {% endif %}
    </div>
{% endblock pdf_main_content %}
