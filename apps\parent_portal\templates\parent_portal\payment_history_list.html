{# D:\school_fees_saas_v2\apps\parent_portal\templates\parent_portal\payment_history_list.html #}
{% extends "parent_portal/parent_portal_base.html" %}
{% load static i18n humanize %}

{% block parent_portal_page_title %}{{ view_title|default:_("Payment History") }}{% endblock parent_portal_page_title %}

{% block extra_parent_portal_css %}
    {{ block.super }}
    <style>
        .payment-history-table th, .payment-history-table td {
            vertical-align: middle;
            font-size: 0.9rem; /* Slightly smaller for dense tables */
        }
        .payment-history-table .text-end { /* Ensure text-align:right for currency */
            text-align: right;
        }
        .payment-history-table th {
            white-space: nowrap;
        }
        .status-badge { /* Standardize badge appearance if needed */
            font-size: 0.8em;
            padding: .35em .65em;
            font-weight: 500;
        }
    </style>
{% endblock extra_parent_portal_css %}

{% block parent_portal_main_content %}
<div class="container mt-4 mb-5">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="mb-0">{{ view_title }}</h1>
        {% if school_profile and school_profile.logo %}
            <img src="{{ school_profile.logo.url }}" alt="{{ request.tenant.name }} Logo" style="max-height: 50px;" class="rounded d-none d-sm-block">
        {% endif %}
    </div>
    <p class="lead text-muted">{% trans "Parent" %}: {{ parent.get_full_name|default:parent.email }}</p>

    {# Optional: Add Filter Form Here if implementing filtering later #}
    {# <form method="get" class="mb-3"> ... </form> #}

    {% if payments %} {# 'payments' is the context_object_name if using ListView, or what you pass from TemplateView #}
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="bi bi-list-ol me-2"></i>{% trans "Your Payments" %}</h5>
            </div>
            <div class="table-responsive">
                <table class="table table-hover table-striped payment-history-table mb-0"> {# Added table-striped #}
                    <thead>
                        <tr>
                            <th>{% trans "Date" %}</th>
                            <th class="text-end">{% trans "Amount" %}</th>
                            <th>{% trans "Method" %}</th>
                            <th>{% trans "Transaction ID / Reference" %}</th>
                            <th>{% trans "Status" %}</th>
                            <th>{% trans "Invoice(s) Paid" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in payments %} {# Loop through 'payments' (or 'page_obj' if paginated by ListView default) #}
                            <tr>
                                <td data-label="Date">{{ payment.payment_date|date:"d M Y H:i" }}</td>
                                <td data-label="Amount" class="text-end">{{ school_profile.currency_symbol|default:"$" }}{{ payment.amount|floatformat:2|intcomma }}</td>
                                <td data-label="Method">{{ payment.get_payment_method_display|default:payment.payment_method|default:"N/A" }}</td>
                                <td data-label="Reference">{{ payment.transaction_id|default:"N/A" }}</td>
                                <td data-label="Status">
                                    <span class="badge status-badge 
                                        {% if payment.status == 'COMPLETED' %}bg-success
                                        {% elif payment.status == 'PENDING' %}bg-warning text-dark
                                        {% elif payment.status == 'FAILED' %}bg-danger
                                        {% elif payment.status == 'REFUNDED' %}bg-info text-dark
                                        {% else %}bg-secondary{% endif %}">
                                        {{ payment.get_status_display|default:payment.status }}
                                    </span>
                                </td>
                                <td data-label="Invoice(s)">
                                    {% for inv in payment.invoices_paid.all %} {# Assuming M2M 'invoices_paid' on Payment model #}
                                        <a href="{% url 'parent_portal:student_fees' student_pk=inv.student.pk %}#invoice-{{inv.pk}}" title="View invoice {{ inv.invoice_number }}">{{ inv.invoice_number }}</a>{% if not forloop.last %}, {% endif %}
                                    {% empty %}
                                        {% trans "N/A" %}
                                    {% endfor %}
                                </td>
                            </tr>
                        {% empty %}
                            <tr>
                                <td colspan="6" class="text-center text-muted py-4">
                                    <i class="bi bi-info-circle fs-3 d-block mb-2"></i>
                                    {% trans "You have not made any payments yet." %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% if is_paginated %} {# Add this block if using ListView and pagination #}
            <div class="card-footer bg-light">
                <nav aria-label="Payment history navigation">
                    <ul class="pagination justify-content-center mb-0">
                        {% if page_obj.has_previous %}
                            <li class="page-item"><a class="page-link" href="?page=1">« {% trans "first" %}</a></li>
                            <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a></li>
                        {% else %}
                            <li class="page-item disabled"><span class="page-link">« {% trans "first" %}</span></li>
                            <li class="page-item disabled"><span class="page-link">{% trans "Previous" %}</span></li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active" aria-current="page"><span class="page-link">{{ num }}</span></li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item"><a class="page-link" href="?page={{ num }}">{{ num }}</a></li>
                            {% elif num == page_obj.number|add:'-3' or num == page_obj.number|add:'3' %}
                                <li class="page-item disabled"><span class="page-link">...</span></li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a></li>
                            <li class="page-item"><a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">{% trans "last" %} »</a></li>
                        {% else %}
                            <li class="page-item disabled"><span class="page-link">{% trans "Next" %}</span></li>
                            <li class="page-item disabled"><span class="page-link">{% trans "last" %} »</span></li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
        </div>
    {% else %} {# Fallback if 'payments' context variable is empty or not provided #}
        <div class="alert alert-info text-center py-4">
            <i class="bi bi-info-circle fs-3 d-block mb-2"></i>
            {% trans "No payment history found." %}
        </div>
    {% endif %}

    <div class="mt-4 text-center">
        <a href="{% url 'parent_portal:dashboard' %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left-circle me-1"></i>{% trans "Back to Dashboard" %}
        </a>
    </div>
</div>
{% endblock parent_portal_main_content %}


























{% comment %} {# templates/parent_portal/payment_history_list.html #}
{% extends "parent_portal/parent_portal_base.html" %}
{% load static humanize core_tags %}

{% block parent_portal_page_title %}{{ view_title|default:"Full Payment History" }}{% endblock %}

{% block parent_portal_main_content %}
<div class="container mt-md-4">
    <div class="page-header mb-4">
        <h1 class="h2 mb-1">{{ view_title|default:"Full Payment History" }}</h1>
        <p class="text-muted">Showing all recorded payments for your children.</p>
    </div>

    <div class="mb-3">
        <a href="{% url 'parent_portal:dashboard' %}" class="btn btn-outline-secondary btn-sm"><i class="bi bi-arrow-left me-1"></i>Back to Dashboard</a>
    </div>

    {% include "partials/_messages.html" %}

    <div class="card shadow-sm">
        <div class="card-header bg-light border-bottom">
            <i class="bi bi-list-ul me-2"></i>All Payments
        </div>
        <div class="card-body p-0">
            {% if payments %}
            <div class="table-responsive">
                <table class="table table-striped table-hover table-sm mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Date</th>
                            <th>Student</th>
                            <th class="text-end">Amount</th>
                            <th>Method</th>
                            <th>Reference</th>
                            <th>Status</th>
                            <th>For Invoice #</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in payments %}
                        <tr>
                            <td>{{ payment.payment_date|date:"d M Y, H:i" }}</td>
                            <td>
                                {% if payment.student %}
                                    {{ payment.student.get_full_name|default:"N/A" }}
                                {% elif payment.invoice.student %}
                                    {{ payment.invoice.student.get_full_name|default:"N/A" }}
                                {% else %}
                                    N/A
                                {% endif %}
                            </td>
                            <td class="text-end">{{ payment.amount|currency:school_profile.currency_symbol }}</td>
                            <td>{{ payment.payment_method.name|default:"N/A" }}</td>
                            <td>{{ payment.reference_number|default:"-" }}</td>
                            <td>
                                <span class="badge bg-{% if payment.status == payment.PaymentStatus.COMPLETED %}success{% elif payment.status == payment.PaymentStatus.FAILED %}danger{% elif payment.status == payment.PaymentStatus.PENDING %}warning text-dark{% else %}secondary{% endif %}">
                                    {{ payment.get_status_display|capfirst }}
                                </span>
                            </td>
                            <td>
                                {% if payment.invoice %}
                                    <a href="{% if payment.student %}{% url 'parent_portal:student_fee_details' student_pk=payment.student.pk %}{% else %}#{% endif %}#invoice-{{ payment.invoice.pk }}">
                                        {{ payment.invoice.invoice_number|default:payment.invoice.pk }}
                                    </a>
                                {% else %}
                                    General Payment
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <a href="{% url 'payments:payment_receipt_pdf_public' pk=payment.pk %}" class="btn btn-outline-secondary btn-sm" target="_blank" title="View Receipt">
                                    <i class="bi bi-receipt"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="card-body text-center py-5">
                <p class="text-muted mb-0 fs-5">No payment records found.</p>
            </div>
            {% endif %}
        </div>
        {% if is_paginated %}
            <div class="card-footer bg-light py-2">
                {% include "partials/_pagination.html" with page_obj=payments filter_params=request.GET.urlencode %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock parent_portal_main_content %}
 {% endcomment %}




























{% comment %} {% extends "tenant_base.html" %} {# Or your parent_base.html #}
{% load static humanize %}

{% block title %}{{ view_title|default:"Student Payment History" }}{% endblock title %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'parent_portal:dashboard' %}">Parent Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{% url 'parent_portal:student_detail' student_pk=student.pk %}">{{ student.full_name }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">Payment History</li>
        </ol>
    </nav>

    <h1 class="mb-4">{{ view_title }}</h1>

    {% if student %}
    <div class="card">
        <div class="card-header bg-secondary text-white">
            Recorded Payments for {{ student.full_name }}
        </div>
        <div class="card-body p-0"> {# For table to be flush #}
            {% if student_payments %}
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Amount Paid</th>
                            <th>Payment Method</th>
                            <th>Reference</th>
                            <th>Status</th>
                            <th>Invoice(s)</th> {# Optional: if payment is linked to invoices #}
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in student_payments %}
                        <tr>
                            <td>{{ payment.payment_date|date:"d M Y" }}</td>
                            <td class="text-end">{{ school_profile.currency_symbol|default:"$" }}{{ payment.amount_paid|floatformat:2|intcomma }}</td>
                            <td>{{ payment.get_payment_method_display|default:payment.payment_method }}</td>
                            <td>{{ payment.reference_number|default:"N/A" }}</td>
                            <td>
                                <span class="badge bg-{{ payment.get_status_badge_class|default:'secondary' }}">
                                    {{ payment.get_status_display|default:payment.status }}
                                </span>
                            </td>
                            <td>
                                {# If payment links to multiple invoices, or one #}
                                {% if payment.invoice_set.all %} {# Assuming related_name is 'invoice_set' or default 'invoice' #}
                                    {% for inv in payment.invoice_set.all %}
                                        <a href="{% url 'parent_portal:student_fees' student_pk=student.pk %}#invoice-{{ inv.pk }}">{{ inv.invoice_number }}</a>{% if not forloop.last %}, {% endif %}
                                    {% endfor %}
                                {% elif payment.invoice %} {# If direct FK to one invoice #}
                                     <a href="{% url 'parent_portal:student_fees' student_pk=student.pk %}#invoice-{{ payment.invoice.pk }}">{{ payment.invoice.invoice_number }}</a>
                                {% else %}
                                    N/A
                                {% endif %}
                            </td>
                            <td>
                                <a href="#" class="btn btn-sm btn-outline-primary disabled" title="View Receipt (Coming Soon)"><i class="bi bi-file-earmark-text"></i></a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="card-body">
                <p class="text-center text-muted p-3">No payment records found for {{ student.full_name }}.</p>
            </div>
            {% endif %}
        </div>
    </div>
    {% else %}
        <p>Student payment history could not be loaded.</p>
    {% endif %}

    <div class="mt-3">
        <a href="{% url 'parent_portal:dashboard' %}" class="btn btn-secondary"><i class="bi bi-arrow-left-circle"></i> Back to Dashboard</a>
        <a href="{% url 'parent_portal:student_detail' student_pk=student.pk %}" class="btn btn-info"><i class="bi bi-person-lines-fill"></i> {{ student.first_name }}'s Details</a>
        <a href="{% url 'parent_portal:student_fees' student_pk=student.pk %}" class="btn btn-warning"><i class="bi bi-receipt"></i> {{ student.first_name }}'s Fees</a>
    </div>
</div>
{% endblock content %}

 {% endcomment %}


