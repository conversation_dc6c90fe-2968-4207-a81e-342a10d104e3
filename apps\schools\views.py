# apps/schools/views.py
# --- Django Core Imports ---
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login as auth_login, logout as auth_logout
from django.contrib import messages
from django.conf import settings
from django.http import Http404
from django.urls import reverse, reverse_lazy, NoReverseMatch
from django.utils.http import url_has_allowed_host_and_scheme
from django.db import connection
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView, FormView
from django.contrib.messages.views import SuccessMessageMixin
from django.http import HttpResponse, Http404
from django.contrib.auth.forms import AuthenticationForm

from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.contrib.auth.decorators import login_required

from django.utils.translation import gettext_lazy as _

# Models
from .models import SchoolProfile, SchoolClass, Section, StaffUser
from apps.tenants.models import School
from apps.hr.models import EmployeeProfile
from django.contrib.auth.models import Group

# Forms
from .forms import (
    SchoolProfileForm,
    StaffLoginForm, 
    StaffUserCreationForm, 
    StaffUserChangeForm,   
    StaffRoleAssignForm,
    SchoolClassForm,
    SectionForm
)
from apps.hr.forms import StaffAndProfileCreationForm, StaffAndProfileChangeForm

# Backends
from .backends import TenantStaffBackend

# --- Other Imports ---
from django.utils import timezone
from decimal import Decimal 
from django.http import JsonResponse
from .forms import ParentLoginForm

from django.views.generic import UpdateView

from apps.portal_admin.utils import create_admin_log_entry

from apps.hr.forms import StaffAndProfileCreationForm, StaffAndProfileChangeForm 

# ========================================
#          STAFF LOGIN VIEW
# ========================================
# D:\school_fees_saas_v2\apps\schools\views.py

# apps/schools/views.py

import logging
from django.shortcuts import render, redirect
from django.urls import reverse, reverse_lazy, NoReverseMatch
from django.contrib.auth import authenticate as django_authenticate, login as auth_login
from django.contrib import messages
from django.db import connection
from django.middleware.csrf import get_token as get_csrf_token_from_request

from .forms import StaffLoginForm
from .models import StaffUser
# from django.conf import settings # Not strictly needed if url_has_allowed_host_and_scheme is imported directly
from django.utils.http import url_has_allowed_host_and_scheme

logger = logging.getLogger(__name__)


def staff_login_view(request):
    tenant_object = getattr(request, 'tenant', None)
    tenant_name = getattr(tenant_object, 'name', 'School Portal')
    current_schema_name = connection.schema_name

    logger.debug(
        f"STAFF_LOGIN_VIEW: Host='{request.get_host()}', Tenant='{tenant_object}', "
        f"Path='{request.path}', Method='{request.method}', Schema='{current_schema_name}'"
    )
    
    expected_csrf_token_on_request_arrival = get_csrf_token_from_request(request)
    logger.debug(
        f"STAFF_LOGIN_VIEW ({request.method}): CSRF token from request.META (via get_token): {expected_csrf_token_on_request_arrival}"
    )

    if request.method == 'POST':
        submitted_csrf_token = request.POST.get('csrfmiddlewaretoken', 'MISSING_IN_POST_DATA')
        logger.debug(f"STAFF_LOGIN_VIEW (POST): CSRF token submitted in form: {submitted_csrf_token}")
        if submitted_csrf_token != expected_csrf_token_on_request_arrival:
            logger.debug(
                f"STAFF_LOGIN_VIEW (POST): Note: Submitted CSRF token might not match pre-login session CSRF token due to session rotation by auth_login. Final validation by CsrfViewMiddleware."
            )

    if request.user.is_authenticated and isinstance(request.user, StaffUser):
        logger.debug(f"STAFF_LOGIN_VIEW: User '{request.user.email}' already authenticated as StaffUser. Redirecting to dashboard.")
        try:
            return redirect(reverse('schools:dashboard'))
        except NoReverseMatch:
            logger.warning("STAFF_LOGIN_VIEW: NoReverseMatch for 'schools:dashboard', falling back to '/portal/dashboard/'.")
            return redirect('/portal/dashboard/')

    if request.method == 'POST':
        form = StaffLoginForm(request, data=request.POST)
        if form.is_valid():
            email = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            logger.info(f"STAFF_LOGIN_VIEW (POST): Valid form. Attempting auth for '{email}' in schema '{current_schema_name}'.")

            user = django_authenticate(request, username=email, password=password) 
            
            logger.debug(f"STAFF_LOGIN_VIEW (POST): django_authenticate result: {user} (Type: {type(user)})")
            logger.debug(f"STAFF_LOGIN_VIEW (POST): user.backend AFTER django_authenticate and BEFORE auth_login: {getattr(user, 'backend', 'NOT SET')}")

            if user is not None and isinstance(user, StaffUser):
                if user.is_active:
                    logger.info(f"STAFF_LOGIN_VIEW (POST): User '{user.email}' is active StaffUser. Preparing to log in.")
                    
                    logger.debug(f"STAFF_LOGIN_VIEW (POST): Session ID BEFORE auth_login: {request.session.session_key}")
                    logger.debug(f"STAFF_LOGIN_VIEW (POST): CSRF secret in session BEFORE auth_login (Django 4+): {request.session.get('_csrf_token', 'Not Set')}")
                    
                    auth_login(request, user) 
                    
                    logger.info(f"STAFF_LOGIN_VIEW (POST): User '{user.email}' LOGGED IN.")
                    logger.debug(f"STAFF_LOGIN_VIEW (POST): Session ID AFTER auth_login: {request.session.session_key}")
                    logger.debug(f"STAFF_LOGIN_VIEW (POST): CSRF secret in session AFTER auth_login (Django 4+): {request.session.get('_csrf_token', 'Not Set')}")
                    
                    logger.debug(f"STAFF_LOGIN_VIEW (POST): Original 'user' var from authenticate - user.backend AFTER auth_login: {getattr(user, 'backend', 'NOT SET')}")
                    logger.debug(f"STAFF_LOGIN_VIEW (POST): request.user.backend AFTER auth_login: {getattr(request.user, 'backend', 'NOT SET')}")
                    logger.debug(f"STAFF_LOGIN_VIEW (POST): request.user object AFTER auth_login: {request.user}, Type: {type(request.user)}")

                    # --- INSERTED/VERIFIED SESSION KEY CHECKS ---
                    logger.debug(f"STAFF_LOGIN_VIEW (POST): Session _auth_user_id: {request.session.get('_auth_user_id')}")
                    logger.debug(f"STAFF_LOGIN_VIEW (POST): Session _auth_user_backend: {request.session.get('_auth_user_backend')}") # IMPORTANT
                    logger.debug(f"STAFF_LOGIN_VIEW (POST): Session _auth_user_hash: {request.session.get('_auth_user_hash')}")
                    logger.debug(f"STAFF_LOGIN_VIEW (POST): All session keys after login: {list(request.session.keys())}")
                    # --- END OF INSERTED/VERIFIED SESSION KEY CHECKS ---
                    
                    messages.success(request, f"Staff login successful! Welcome {user.get_full_name() or user.email}.")
                    
                    next_url = request.POST.get('next') or request.GET.get('next')
                    safe_url_to_redirect = None
                    if next_url:
                        allowed_hosts_for_next = {request.get_host().split(':')[0]}
                        if url_has_allowed_host_and_scheme(
                            url=next_url,
                            allowed_hosts=allowed_hosts_for_next, 
                            require_https=request.is_secure()
                        ):
                            safe_url_to_redirect = next_url
                        else:
                            logger.warning(f"STAFF_LOGIN_VIEW (POST): Unsafe 'next' URL blocked: {next_url}")
                            messages.warning(request, "The redirect URL was deemed unsafe.")
                            
                    if not safe_url_to_redirect:
                        try: 
                            safe_url_to_redirect = reverse('schools:dashboard')
                        except NoReverseMatch: 
                            logger.error("STAFF_LOGIN_VIEW (POST): NoReverseMatch for 'schools:dashboard' on redirect. Critical error in URL setup.")
                            safe_url_to_redirect = '/portal/' 
                    return redirect(safe_url_to_redirect)
                else:
                    logger.warning(f"STAFF_LOGIN_VIEW (POST): StaffUser '{email}' is inactive.")
                    messages.error(request, "This staff account is inactive.")
            else:
                logger.warning(f"STAFF_LOGIN_VIEW (POST): Authentication failed for '{email}' or user is not StaffUser. User object: {user}")
                messages.error(request, "Invalid staff email or password for this school's staff portal.")
        else: 
            logger.warning(f"STAFF_LOGIN_VIEW (POST): Form is invalid. Errors: {form.errors.as_json()}")
    else: 
        form = StaffLoginForm(request)

    context = {
        'form': form,
        'tenant_name': tenant_name,
        'view_title': "Staff Login",
        'current_tenant': tenant_object,
        'next': request.GET.get('next', '')
    }
    return render(request, 'schools/staff_login.html', context)


# ========================================
#          STAFF LOGOUT VIEW
# ========================================

@login_required(login_url=reverse_lazy('schools:staff_login'))
def staff_logout_view(request):
    current_user_email = request.user.email if request.user.is_authenticated else "User"
    logger.info(f"STAFF LOGOUT VIEW: Logging out staff user: {current_user_email}")

    # Clear the session
    auth_logout(request)
    messages.info(request, "You have been successfully logged out from the staff portal.")

    # Simple redirect to staff login - let Django handle the URL routing
    try:
        return redirect(reverse('schools:staff_login'))
    except Exception as e:
        logger.error(f"STAFF LOGOUT VIEW: Error redirecting to staff login: {e}")
        # Fallback to public homepage if staff login URL can't be resolved
        try:
            return redirect(reverse('public_site:home'))
        except:
            return redirect('/')


# ========================================
#          TENANT DASHBOARD VIEW
# ========================================


from django.contrib.auth.decorators import login_required
from django.urls import reverse_lazy # Use reverse_lazy for login_url in decorator
from django.contrib.auth import logout as auth_logout_view # Alias to avoid conflict
from .models import StaffUser # Ensure StaffUser is imported
from django.shortcuts import render, redirect
from django.contrib import messages
import logging

logger = logging.getLogger(__name__)

from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.urls import reverse_lazy
from .models import StaffUser # Assuming StaffUser is here
# from .utils import calculate_reports_permissions # If you moved it to schools/utils.py
# from apps.common.utils import calculate_reports_permissions # If you moved it to common/utils.py

# Placeholder for checking if subscription models are available
# You should import this from where you define it, e.g., apps.common.context_processors
# Or define it based on try-except imports here if needed.
try:
    from apps.subscriptions.models import Subscription, Feature
    SUBSCRIPTION_MODELS_DEFINED = True
except ImportError:
    SUBSCRIPTION_MODELS_DEFINED = False
    Subscription = None
    Feature = None

# --- Define or Import calculate_reports_permissions function ---
# (Using the version with the get_feature helper from previous discussions)
def calculate_reports_permissions(user, tenant_features_dict):
    def get_feature(key):
        return tenant_features_dict.get(key, False)

    show_outstanding_fees_link = (
        get_feature('REPORTS_BASIC') and 
        user.has_perm('reporting.view_outstanding_fees_report')
    )
    show_collection_report_link = (
        get_feature('REPORTS_BASIC') and 
        user.has_perm('reporting.view_collection_report')
    )
    show_trial_balance_link = (
        get_feature('REPORTS_ADVANCED') and 
        user.has_perm('reporting.view_trial_balance_report')
    )
    show_income_statement_link = (
        get_feature('REPORTS_ADVANCED') and 
        user.has_perm('reporting.view_income_expense_report')
    )
    show_balance_sheet_link = (
        get_feature('REPORTS_ADVANCED') and 
        user.has_perm('reporting.view_balance_sheet_report')
    )
    show_cash_flow_link = (
        get_feature('REPORTS_ADVANCED') and 
        user.has_perm('reporting.view_cash_flow_statement_report')
    )
    show_budget_variance_link = ( # Logic from original template discussion
        get_feature('BUDGETING') and 
        (get_feature('REPORTS_ADVANCED') or user.has_perm('reporting.view_budget_variance_report'))
    )
    
    has_any_basic_report_link_to_show = show_outstanding_fees_link or show_collection_report_link
    has_any_advanced_financial_report_link_to_show = (
        show_trial_balance_link or show_income_statement_link or 
        show_balance_sheet_link or show_cash_flow_link
    )
    can_see_reports_dropdown = (
        has_any_basic_report_link_to_show or 
        has_any_advanced_financial_report_link_to_show or 
        show_budget_variance_link 
    )
    show_basic_advanced_divider = has_any_basic_report_link_to_show and has_any_advanced_financial_report_link_to_show
    show_advanced_budget_divider = (
        (has_any_basic_report_link_to_show or has_any_advanced_financial_report_link_to_show) and 
        show_budget_variance_link
    )

    return {
        'show_outstanding_fees_link': show_outstanding_fees_link,
        'show_collection_report_link': show_collection_report_link,
        'show_trial_balance_link': show_trial_balance_link,
        'show_income_statement_link': show_income_statement_link,
        'show_balance_sheet_link': show_balance_sheet_link,
        'show_cash_flow_link': show_cash_flow_link,
        'show_budget_variance_link': show_budget_variance_link,
        'can_see_reports_dropdown': can_see_reports_dropdown,
        'show_basic_advanced_divider': show_basic_advanced_divider,
        'show_advanced_budget_divider': show_advanced_budget_divider,
    }
# --- End of calculate_reports_permissions function ---

def get_active_features_for_tenant(tenant_instance):
    """
    Placeholder/Example: Retrieves the active feature flags for a given tenant.
    Replace this with your actual logic (e.g., querying Subscription/Plan/Feature models
    or checking boolean fields directly on your School/Tenant model).
    """
    # Default features (all off, or some basic ones on)
    features = {
        'REPORTS_BASIC': True, # Often a default
        'REPORTS_ADVANCED': False,
        'BUDGETING': False,
        'FEE_MANAGEMENT': True, # Often a default
        'STUDENT_MANAGEMENT': True, # Often a default
        'HR_MODULE': False,
        'HR_LEAVE': False,
        'ACCOUNTING': False,
        'EXPENSE_TRACKING': False,
        'PARENT_PORTAL': False,
        'ONLINE_PAYMENTS': False,
        'EMAIL_NOTIFICATIONS': False,
        # Add ALL feature flags your system uses
    }

    if tenant_instance:
        # Example: If features are determined by tenant name for testing
        if tenant_instance.name.lower() == "zharatest":
            features['REPORTS_BASIC'] = True
            features['REPORTS_ADVANCED'] = True
            features['BUDGETING'] = True
            features['HR_MODULE'] = True 
            features['HR_LEAVE'] = True
            features['ACCOUNTING'] = True
            features['EXPENSE_TRACKING'] = True
            # ... set other features to True for zharatest for full testing ...
            return features

        # Example: If using Subscription models (adapt to your actual model fields)
        if SUBSCRIPTION_MODELS_DEFINED and hasattr(tenant_instance, 'subscription'):
            tenant_subscription = getattr(tenant_instance, 'subscription', None)
            if tenant_subscription and tenant_subscription.is_usable and hasattr(tenant_subscription, 'plan'):
                plan = tenant_subscription.plan
                if hasattr(plan, 'features') and hasattr(plan.features, 'all'):
                    plan_db_features = list(plan.features.all().values_list('code', flat=True))
                    for feature_code in features.keys(): # Iterate defined features
                        if feature_code in plan_db_features:
                            features[feature_code] = True
                        # else: it keeps its default (e.g., False, or True if default was True)
    return features


# D:\school_fees_saas_v2\apps\schools\views.py
from django.shortcuts import render, redirect, reverse
from django.urls import reverse_lazy
from django.contrib.auth.decorators import login_required
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.db.models import Q, Sum, F, DecimalField, Count # Ensure Count is imported
from django.db.models.functions import Coalesce
from django.contrib import messages # For messages framework
from decimal import Decimal

# --- Model Imports (ensure these are correct and models exist) ---
StaffUser, SchoolProfile, Student, Invoice, Announcement, AdminActivityLog, Payment = (None,) * 7 # Default to None

try:
    from .models import StaffUser, SchoolProfile
except ImportError as e: logging.getLogger(__name__).critical(f"Failed to import from schools.models: {e}")
try:
    from apps.students.models import Student
except ImportError as e: logging.getLogger(__name__).warning(f"Student model not found: {e}")
try:
    from apps.fees.models import Invoice
except ImportError as e: logging.getLogger(__name__).warning(f"Invoice model not found: {e}")
try:
    from apps.announcements.models import Announcement
except ImportError as e: logging.getLogger(__name__).warning(f"Announcement model not found: {e}")
try:
    from apps.portal_admin.models import AdminActivityLog
except ImportError as e: logging.getLogger(__name__).warning(f"AdminActivityLog model not found: {e}")
try:
    from apps.payments.models import Payment 
except ImportError as e: logging.getLogger(__name__).warning(f"Payment model not found: {e}")

import logging
logger = logging.getLogger(__name__)

@login_required(login_url=reverse_lazy('schools:staff_login'))
def tenant_dashboard(request):
    # Enhanced debug logging
    logger.info(f"DASHBOARD DEBUG: User: {request.user}, Type: {type(request.user)}, Authenticated: {request.user.is_authenticated}")
    logger.info(f"DASHBOARD DEBUG: Session keys: {list(request.session.keys())}")
    logger.info(f"DASHBOARD DEBUG: Session auth_user_id: {request.session.get('_auth_user_id')}")
    logger.info(f"DASHBOARD DEBUG: Session auth_user_backend: {request.session.get('_auth_user_backend')}")
    logger.info(f"DASHBOARD DEBUG: StaffUser class: {StaffUser}")
    logger.info(f"DASHBOARD DEBUG: User class name: {request.user.__class__.__name__}")
    logger.info(f"DASHBOARD DEBUG: User model name: {getattr(request.user._meta, 'model_name', 'NO_META') if hasattr(request.user, '_meta') else 'NO_META'}")

    if StaffUser is None:
        messages.error(request, _("System error: Staff module configuration issue."))
        return redirect(reverse('public_site:home'))

    # Check if user is authenticated and is a StaffUser
    if not request.user.is_authenticated:
        logger.warning(f"tenant_dashboard: User not authenticated. Redirecting to login.")
        return redirect(reverse_lazy('schools:staff_login'))

    # More comprehensive user type checking
    is_staff_user = False
    try:
        # Try multiple ways to check if user is StaffUser
        if hasattr(request.user, '_meta') and request.user._meta.model_name == 'staffuser':
            is_staff_user = True
            logger.info(f"tenant_dashboard: User identified as StaffUser via _meta.model_name")
        elif isinstance(request.user, StaffUser):
            is_staff_user = True
            logger.info(f"tenant_dashboard: User identified as StaffUser via isinstance")
        elif hasattr(request.user, '__class__') and 'StaffUser' in str(request.user.__class__):
            is_staff_user = True
            logger.info(f"tenant_dashboard: User identified as StaffUser via class name")
        elif request.user.__class__.__name__ == 'StaffUser':
            is_staff_user = True
            logger.info(f"tenant_dashboard: User identified as StaffUser via __name__")
        else:
            logger.warning(f"tenant_dashboard: User type check failed. User class: {request.user.__class__}")
    except Exception as e:
        logger.error(f"tenant_dashboard: Error checking user type: {e}")

    if not is_staff_user:
        logger.warning(f"tenant_dashboard: User '{request.user}' (type: {type(request.user)}) is not StaffUser. Redirecting.")
        return redirect(reverse_lazy('schools:staff_login'))

    # Debug logging
    logger.info(f"DASHBOARD: User {request.user} (type: {type(request.user)}) accessing dashboard successfully")

    tenant = request.tenant
    # staff_user = request.user # request.user is already the StaffUser instance

    # Get announcements for staff - temporarily disabled to fix login loop
    staff_announcements = []

    # Initialize context
    context = {
        'view_title': _('Admin Dashboard'), # Matches template's default if not overridden
        'announcements': staff_announcements,
    }

    # --- Dashboard Stats ---
    stats_data = {
        'active_students': 0,
        'due_invoices_count': 0,
        'due_invoices_total': Decimal('0.00'),
        'collections_this_month': Decimal('0.00'),
        'active_staff_count': 0, # As used in your template
    }

    if Student:
        try:
            stats_data['active_students'] = Student.objects.filter(is_active=True).count()
        except Exception as e: 
            logger.error(f"Error fetching student count for {tenant.name}: {e}", exc_info=True)
            stats_data['active_students'] = 'N/A'
    
    if StaffUser:
        try:
            stats_data['active_staff_count'] = StaffUser.objects.filter(is_active=True).count()
        except Exception as e: 
            logger.error(f"Error fetching staff count for {tenant.name}: {e}", exc_info=True)
            stats_data['active_staff_count'] = 'N/A'

    if Invoice:
        try:
            outstanding_statuses = [
                Invoice.InvoiceStatus.SENT, 
                Invoice.InvoiceStatus.PARTIALLY_PAID, 
                Invoice.InvoiceStatus.OVERDUE
            ]
            due_invoices_query = Invoice.objects.filter(status__in=outstanding_statuses)
            stats_data['due_invoices_count'] = due_invoices_query.count()
            
            aggregation = due_invoices_query.aggregate(
                calculated_total_due=Coalesce(Sum(F('total_amount') - F('amount_paid')), Decimal('0.00'), output_field=DecimalField())
            )
            stats_data['due_invoices_total'] = aggregation.get('calculated_total_due', Decimal('0.00'))
        except AttributeError: # If Invoice.InvoiceStatus isn't set up as expected
            logger.error(f"Invoice.InvoiceStatus not defined as expected for {tenant.name}.", exc_info=True)
            stats_data['due_invoices_count'] = 'N/A (Err)'
            stats_data['due_invoices_total'] = Decimal('0.00')
        except Exception as e: 
            logger.error(f"Error fetching invoice stats for {tenant.name}: {e}", exc_info=True)
            stats_data['due_invoices_count'] = 'N/A (Err)'

    if Payment:
        try:
            current_month_start = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            # Ensure your Payment model has 'payment_date', 'status', and 'amount_received' (or 'amount')
            collections_aggregation = Payment.objects.filter(
                payment_date__gte=current_month_start,
                status='COMPLETED' # Example status for successful payments
            ).aggregate(total_collected=Coalesce(Sum('amount_received'), Decimal('0.00'))) # Use your actual amount field
            stats_data['collections_this_month'] = collections_aggregation.get('total_collected', Decimal('0.00'))
        except Exception as e: 
            logger.error(f"Error fetching collections: {e}", exc_info=True)
            stats_data['collections_this_month'] = 'N/A (Err)'
    context['stats'] = stats_data


    # --- Fetch Recent Announcements for Staff ---
    recent_announcements_qs = [] # Default to empty list
    if Announcement and tenant:
        now = timezone.now()
        try:
            relevant_audience_types = ['ALL_STAFF', 'ALL_USERS_IN_TENANT']
            q_tenant_specific = Q(tenant=tenant, is_global=False, target_audience_type__in=relevant_audience_types)
            q_global_to_staff = Q(tenant__isnull=True, is_global=True, target_audience_type__in=['ALL_STAFF', 'PLATFORM_WIDE_TO_ADMINS'])
            
            recent_announcements_qs = Announcement.objects.filter(
                q_tenant_specific | q_global_to_staff
            ).filter(
                is_active=True, publish_date__lte=now
            ).filter(
                Q(expiry_date__gte=now) | Q(expiry_date__isnull=True)
            ).distinct().order_by('-publish_date')[:3] # Fetch top 3
            
        except Exception as e: 
            logger.error(f"Error fetching announcements for {tenant.name}: {e}", exc_info=True)
    context['recent_announcements'] = recent_announcements_qs
    
    # --- Fetch Recent Activities ---
    recent_activities_qs = []
    if AdminActivityLog:
        try:
            recent_activities_qs = AdminActivityLog.objects.order_by('-timestamp')[:5] # Fetches from current tenant schema
        except Exception as e: 
            logger.error(f"Error fetching recent activities for {tenant.name}: {e}", exc_info=True)
    context['recent_activities'] = recent_activities_qs
    
    # --- Fetch Alerts & Notifications (Placeholder - Implement your Notification model logic) ---
    # For now, passing an empty list so the template's {% if alerts %} works.
    context['alerts'] = [
        # Example structure if you populate it:
        # {'level_tag': 'warning', 'icon_class': 'exclamation-triangle-fill', 'message': '<strong>Reminder:</strong> Term reports due soon.', 'timestamp': timezone.now() - timezone.timedelta(days=2)},
    ]

    logger.debug(f"Final context keys for schools/dashboard.html: {list(context.keys())}")
    return render(request, 'schools/dashboard.html', context)

# @login_required(login_url=reverse_lazy('schools:staff_login'))
# def tenant_dashboard(request):
    
#     # --- INSERTED/VERIFIED SESSION KEY CHECKS AT DASHBOARD START ---
#     print("--- tenant_dashboard VIEW - SESSION STATE AT START ---")
#     print(f"TENANT_DASHBOARD (START): Session _auth_user_id: {request.session.get('_auth_user_id')}")
#     print(f"TENANT_DASHBOARD (START): Session _auth_user_backend: {request.session.get('_auth_user_backend')}") # IMPORTANT
#     print(f"TENANT_DASHBOARD (START): Session _auth_user_hash: {request.session.get('_auth_user_hash')}")
#     print(f"TENANT_DASHBOARD (START): All session keys at view start: {list(request.session.keys())}")
#     # --- END OF INSERTED/VERIFIED SESSION KEY CHECKS ---
    
#     if not isinstance(request.user, StaffUser):
#         return redirect(reverse_lazy('schools:staff_login')) 

#     tenant_name = getattr(request.tenant, 'name', 'Your School')
    
#     # 1. Get the dictionary of active features for the current tenant
#     #    This function `get_active_features_for_tenant` needs to be robust.
#     current_tenant_features = get_active_features_for_tenant(request.tenant)
    
#     # 2. Calculate report-specific visibility flags
#     reports_context_vars = {}
#     try:
#         reports_context_vars = calculate_reports_permissions(request.user, current_tenant_features)
#     except Exception as e: # Catch broader errors during calculation for safety
#         print(f"ERROR in tenant_dashboard calling calculate_reports_permissions: {e}")
#         # reports_context_vars will remain empty, so report links might not show, but page won't crash.
    
#     # 3. Prepare the main context for the template
#     context = {
#         'tenant_name': tenant_name,
#         'view_title': 'School Dashboard',
#         # ... add any other specific context variables needed directly by dashboard.html ...
#     }
    
#     # 4. Add the calculated report visibility flags to the context
#     context.update(reports_context_vars)

#     # --- MORE DETAILED DIAGNOSTIC PRINT STATEMENTS ---
#     print("--- tenant_dashboard VIEW - FINAL PRE-RENDER DIAGNOSTICS ---")
#     print(f"Current User Object (request.user): {request.user}")
#     print(f"Current User Type: {type(request.user)}")
#     print(f"Current User is_authenticated: {request.user.is_authenticated}")
#     print(f"Current User's Associated Backend Path: {getattr(request.user, 'backend', 'NOT SET')}")
    
#     if hasattr(request.user, 'email'):
#         print(f"Current User Email: {request.user.email}")
#     if hasattr(request.user, 'pk'):
#         print(f"Current User PK: {request.user.pk}")

#     # Try to get all permissions here and see if it errors or what it returns
#     try:
#         print("Attempting request.user.get_all_permissions() directly in view...")
#         all_perms = request.user.get_all_permissions()
#         print(f"SUCCESS: Current User All Permissions (count directly in view): {len(all_perms)}")
#     except Exception as e_perms_direct:
#         print(f"ERROR trying to get_all_permissions directly in view: {type(e_perms_direct).__name__}: {e_perms_direct}")
#         import traceback
#         traceback.print_exc()

#     return render(request, 'schools/dashboard.html', context)



# apps/schools/views.py
from django.urls import reverse_lazy
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic import UpdateView
from django.contrib.messages.views import SuccessMessageMixin # For success messages
from django.contrib import messages # For manual messages
from django.utils.translation import gettext_lazy as _
from django.http import Http404 # For raising 404
import logging

from .models import SchoolProfile # Your SchoolProfile model
from .forms import SchoolProfileForm # Your SchoolProfileForm

logger = logging.getLogger(__name__) # Good practice: get logger for current module

class SchoolProfileUpdateView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    model = SchoolProfile
    form_class = SchoolProfileForm
    template_name = 'schools/school_profile_form.html' # Ensure this template exists
    
    login_url = reverse_lazy('schools:staff_login') 
    redirect_field_name = 'next' # Standard for LoginRequiredMixin

    # Use the standard Django permission for changing the model.
    # Make sure the "School Administrators" group has 'schools.change_schoolprofile'.
    permission_required = 'schools.change_schoolprofile' # Corrected from your model's Meta
    raise_exception = True # Good for development: raises PermissionDenied instead of redirecting to login

    success_message = _("School profile updated successfully.") # For SuccessMessageMixin
    
    def get_success_url(self):
        # Redirect back to the same update page is common for settings pages.
        return reverse_lazy('schools:profile_update') 
        # Alternative: return reverse_lazy('schools:dashboard')

    def get_object(self, queryset=None):
        """
        Gets or creates the SchoolProfile for the current tenant.
        The SchoolProfile.school field (OneToOneField to Tenant model) is the primary key.
        """
        if not hasattr(self.request, 'tenant') or not self.request.tenant:
            logger.error(
                f"SchoolProfileUpdateView.get_object: request.tenant not found for user "
                f"{self.request.user} accessing {self.request.path}! Ensure django-tenants "
                f"middleware is active and domain is correct."
            )
            raise Http404(_("Tenant context not available. Cannot load school profile."))

        logger.debug(
            f"SchoolProfileUpdateView.get_object: Attempting to get/create profile for "
            f"tenant '{self.request.tenant.schema_name}' (Tenant PK: {self.request.tenant.pk})"
        )
        
        try:
            profile, created = self.model.objects.get_or_create(
                school=self.request.tenant, # Use the current tenant instance as the primary key value
                defaults={
                    # Provide sensible defaults for any fields that are NOT blank=True/null=True
                    # and don't have a model-level default.
                    # Your SchoolProfile model fields seem to be mostly blank/null=True or have defaults.
                    'school_name_on_reports': self.request.tenant.name if hasattr(self.request.tenant, 'name') else f"{self.request.tenant.schema_name} School",
                    # Add other essential defaults if your model requires them on creation.
                    # Example: if currency_symbol was not nullable and had no default in model:
                    # 'currency_symbol': '$', 
                }
            )
            
            if created:
                logger.info(
                    f"CREATED new SchoolProfile for tenant '{self.request.tenant.schema_name}' "
                    f"(SchoolProfile PK is now Tenant PK: {profile.pk})"
                )
                # No need to call profile.save() here if get_or_create created it, unless you modify defaults further.
                # If defaults were complex or needed self.request.tenant again:
                # profile.some_other_field = self.request.tenant.some_attribute 
                # profile.save()
                messages.info(self.request, _("Initial school profile has been set up. Please review and save your settings."))
            else:
                logger.debug(
                    f"FETCHED existing SchoolProfile for tenant '{self.request.tenant.schema_name}' (PK: {profile.pk})"
                )
                
            return profile
            
        except Exception as e: 
            logger.error(
                f"CRITICAL: Unhandled error fetching/creating SchoolProfile for tenant "
                f"{self.request.tenant.schema_name}: {e}", exc_info=True
            )
            # Log the full traceback for unexpected errors
            raise Http404(_("School profile could not be loaded or created due to a system error. Please contact support."))


    def get_form_kwargs(self):
        """Pass 'request' to the form if the form needs it (e.g., for user-specific choices)."""
        kwargs = super().get_form_kwargs()
        kwargs['request'] = self.request 
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Update School Profile")
        if hasattr(self.request, 'tenant') and self.request.tenant and hasattr(self.request.tenant, 'name'):
            context['tenant_name'] = self.request.tenant.name
        else:
            context['tenant_name'] = _("School Profile") # Generic fallback
        return context

    def form_valid(self, form):
        logger.info(
            f"SchoolProfileForm is valid. User: {self.request.user.email}, "
            f"Tenant: {self.request.tenant.schema_name}. Saving profile."
        )
        # SuccessMessageMixin handles the success message defined in self.success_message.
        # Audit log example (ensure create_admin_log_entry handles None for tenant if used in public):
        # from apps.portal_admin.utils import create_admin_log_entry # Adjust import
        # if hasattr(self.request, 'tenant') and self.request.tenant:
        #     create_admin_log_entry(
        #         user=self.request.user, 
        #         tenant=self.request.tenant, 
        #         action_type="SCHOOL_PROFILE_UPDATE", 
        #         obj=self.object, # self.object is set by UpdateView before form_valid
        #         description=f"School profile updated by {self.request.user.email}"
        #     )
        return super().form_valid(form)

    def form_invalid(self, form):
        logger.warning(
            f"SchoolProfileForm is invalid. User: {self.request.user.email}, "
            f"Tenant: {self.request.tenant.schema_name}. "
            f"Errors: {form.errors.as_json()}"
        )
        
        print("--- SchoolProfileForm POST Data ---")
        print(self.request.POST)
        print("--- SchoolProfileForm Initial Data (if any) ---")
        print(form.initial)
        print("--- SchoolProfileForm Bound Data ---")
        print(form.data)
        print("--- SchoolProfileForm Instance BEFORE validation ---")
        print(form.instance) # Should be the profile fetched by get_object
        print(f"  Instance PK (school_id): {form.instance.pk}") # Should be tenant's PK
        print(f"  Instance school attribute: {form.instance.school}")
        
        # The SuccessMessageMixin won't display, so we add a manual error message.
        messages.error(self.request, _("Please correct the errors highlighted below."))
        # For console debugging during development:
        # print("--- SchoolProfileForm ERRORS (from form_invalid method) ---")
        # print(form.errors.as_data()) 
        return super().form_invalid(form)



import logging
from django.urls import reverse_lazy
from django.views.generic import ListView, CreateView, UpdateView, DetailView
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.contrib.messages.views import SuccessMessageMixin
from django.shortcuts import get_object_or_404

from .models import StaffUser
from .forms import StaffUserCreationForm, StaffUserChangeForm # Use the consolidated forms
# ... other imports ...

logger_views = logging.getLogger(__name__) # Use the module's logger

class StaffListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    model = StaffUser
    template_name = 'schools/staff_list.html'
    context_object_name = 'staff_members' # Changed for clarity to match template
    permission_required = 'schools.view_staffuser'
    login_url = reverse_lazy('schools:staff_login')
    paginate_by = 15

    def get_queryset(self):
        logger_views.info("--- StaffListView: get_queryset() called ---")
        qs = super().get_queryset().order_by('last_name', 'first_name')
        logger_views.info(f"StaffListView: Queryset count from DB: {qs.count()}")
        return qs

    def get_context_data(self, **kwargs):
        logger_views.info("--- StaffListView: get_context_data() called ---")
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Manage Staff Members"
        if self.context_object_name in context:
            logger_views.info(f"StaffListView: staff_members in context count: {len(context[self.context_object_name])}")
        else:
            logger_views.warning(f"StaffListView: context_object_name '{self.context_object_name}' not found in context.")
        return context



# StaffCreateView using StaffUserCreationForm
class StaffCreateView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, CreateView):
    model = StaffUser
    form_class = StaffUserCreationForm # Use the consolidated creation form
    template_name = 'schools/staff_form.html'
    permission_required = 'schools.add_staffuser'
    success_message = "Staff member '%(first_name)s %(last_name)s' created successfully."
    login_url = reverse_lazy('schools:staff_login')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Add New Staff Member"
        return context

    def get_success_url(self):
        return reverse_lazy('schools:staff_list')

    def form_valid(self, form):
        logger_views.info(f"StaffCreateView: form_valid called for email {form.cleaned_data.get('email')}")
        # The form's save method handles creating the user.
        # If you need to do something with the user object after creation (e.g., assign default group)
        # self.object = form.save()
        # # Example: assign to a default 'General Staff' group
        # try:
        #     default_group = Group.objects.get(name='General Staff')
        #     self.object.groups.add(default_group)
        # except Group.DoesNotExist:
        #     logger_views.warning("Default 'General Staff' group not found for new staff user.")
        # return super().form_valid(form) # if self.object was set
        return super().form_valid(form)


# StaffUpdateView using StaffUserChangeForm
class StaffUpdateView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    model = StaffUser
    form_class = StaffUserChangeForm # Use the consolidated change form
    template_name = 'schools/staff_form.html'
    permission_required = 'schools.change_staffuser'
    success_message = "Staff member '%(first_name)s %(last_name)s' updated successfully."
    login_url = reverse_lazy('schools:staff_login')
    context_object_name = 'staff_member' # For clarity in template if needed, though form.instance is usually used

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Edit Staff: {self.object.get_full_name()}"
        return context

    def get_success_url(self):
        # return reverse_lazy('schools:staff_detail', kwargs={'pk': self.object.pk})
        return reverse_lazy('schools:staff_list') # Or redirect to list

    def form_valid(self, form):
        logger_views.info(f"StaffUpdateView: form_valid called for user {self.object.email}")
        return super().form_valid(form)


# StaffDetailView (example)
class StaffDetailView(LoginRequiredMixin, PermissionRequiredMixin, DetailView):
    model = StaffUser
    template_name = 'schools/staff_detail.html'
    context_object_name = 'staff_member'
    permission_required = 'schools.view_staffuser'
    login_url = reverse_lazy('schools:staff_login')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Staff Profile: {self.object.get_full_name()}"
        return context
    


class StaffDeleteView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, DeleteView):
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'schools.add_staffuser'
    model = StaffUser
    template_name = 'schools/staff_confirm_delete.html'
    success_url = reverse_lazy('schools:staff_list')
    success_message = "Staff member deleted successfully."
    context_object_name = 'staff_member'



# --- Assign Staff Roles/Groups View ---
class AssignStaffRolesView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, FormView):
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'schools.add_staffuser'
    form_class = StaffRoleAssignForm # Defined in schools/forms.py
    template_name = 'schools/staff_assign_roles.html'

    def setup(self, request, *args, **kwargs):
        super().setup(request, *args, **kwargs)
        staff_user_pk = self.kwargs.get('pk')
        self.staff_user = get_object_or_404(StaffUser, pk=staff_user_pk)

    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        # Queryset for Group model; Group model is in public schema but accessible in tenant context if auth app is in TENANT_APPS
        form.fields['roles'].queryset = Group.objects.all().order_by('name')
        return form

    def get_initial(self):
        initial = super().get_initial()
        if self.staff_user:
            initial['roles'] = self.staff_user.groups.all().values_list('pk', flat=True)
        return initial

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f'Assign Roles/Groups to {self.staff_user.get_full_name()}' # Call it as a method
        context['staff_member'] = self.staff_user
        return context

    def form_valid(self, form):
        assigned_roles = form.cleaned_data['roles'] # 'roles' should be the name of your form field for groups
    
    # self.staff_user should be the StaffUser instance whose roles are being edited.
    # This is typically fetched in the view's setup() or get_object() method.
    # Example: self.staff_user = get_object_or_404(StaffUser, pk=self.kwargs['staff_pk'])
    
        self.staff_user.groups.set(assigned_roles) # This correctly updates the M2M relationship

    # Get the display name for the success message
        staff_display_name = self.staff_user.get_full_name() # Call the method
        if not staff_display_name: # Fallback if first_name and last_name might be empty
                staff_display_name = self.staff_user.email
                
        messages.success(self.request, f"Roles updated for {staff_display_name}.")
            
        return redirect(reverse('schools:staff_detail', kwargs={'pk': self.staff_user.pk}))




# --- SchoolClass (Class/Grade) CRUD Views ---
class SchoolClassListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'schools.view_schoolclass'
    model = SchoolClass
    template_name = 'schools/class_list.html'
    context_object_name = 'classes'
    paginate_by = 20

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Manage Classes/Grades"
        return context



class SchoolClassCreateView(LoginRequiredMixin, PermissionRequiredMixin,SuccessMessageMixin, CreateView):
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'schools.add_schoolclass'
    model = SchoolClass
    form_class = SchoolClassForm
    template_name = 'schools/class_form.html'
    success_url = reverse_lazy('schools:class_list')
    success_message = "Class/Grade '%(name)s' created successfully."

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Add New Class/Grade"
        return context



class SchoolClassUpdateView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'schools.change_schoolclass'
    model = SchoolClass
    form_class = SchoolClassForm
    template_name = 'schools/class_form.html'
    success_url = reverse_lazy('schools:class_list')
    success_message = "Class/Grade '%(name)s' updated successfully."

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Edit Class/Grade: {self.object.name}"
        return context



class SchoolClassDeleteView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, DeleteView):
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'schools.delete_schoolclass'
    model = SchoolClass
    template_name = 'schools/class_confirm_delete.html'
    success_url = reverse_lazy('schools:class_list')
    success_message = "Class/Grade deleted successfully."
    # Add protection: prevent delete if sections or students exist for this class

# --- Section CRUD Views ---
# Sections are often managed IN THE CONTEXT of a specific SchoolClass.
# So, the ListView might show sections for a given class.
# CreateView for a section will need to know which class it belongs to.



class SectionListViewForClass(LoginRequiredMixin, PermissionRequiredMixin,ListView):
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'schools.view_section'
    model = Section
    template_name = 'schools/section_list_for_class.html'
    context_object_name = 'sections'
    paginate_by = 20

    def dispatch(self, request, *args, **kwargs):
        # Get the SchoolClass from URL kwarg
        self.school_class = get_object_or_404(SchoolClass, pk=self.kwargs.get('class_pk'))
        return super().dispatch(request, *args, **kwargs)

    def get_queryset(self):
        # Filter sections by the school_class obtained in dispatch
        return Section.objects.filter(school_class=self.school_class).order_by('name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Manage Sections for {self.school_class.name}"
        context['school_class'] = self.school_class # Pass class to template for display/links
        return context



class SectionCreateViewForClass(LoginRequiredMixin, PermissionRequiredMixin,SuccessMessageMixin, CreateView):
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'schools.add_section'
    model = Section
    form_class = SectionForm
    template_name = 'schools/section_form.html'
    # Success URL will redirect back to the section list for the specific class


    def setup(self, request, *args, **kwargs): # Runs before dispatch and other methods
        super().setup(request, *args, **kwargs)
        # Get the parent SchoolClass from the URL
        self.school_class = get_object_or_404(SchoolClass, pk=self.kwargs['class_pk'])
        print(f"--- SectionCreateView SETUP: self.school_class = {self.school_class} (ID: {self.school_class.pk}) ---") # DEBUG
        
        
    def dispatch(self, request, *args, **kwargs):
        self.school_class = get_object_or_404(SchoolClass, pk=self.kwargs.get('class_pk'))
        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        """Pass school_class to form if needed for pre-selection or validation."""
        kwargs = super().get_form_kwargs()
        # kwargs['school_class_instance'] = self.school_class # If form needs it
        return kwargs

    def get_initial(self):
        """Pre-select the school_class in the form."""
        initial = super().get_initial()
        initial['school_class'] = self.school_class
        return initial

    def form_valid(self, form):
        print(f"--- SectionCreateView FORM_VALID: Entry. self.school_class = {self.school_class} ---") # DEBUG
        # Assign the fetched SchoolClass to the new Section instance
        form.instance.school_class = self.school_class # This is the crucial line
        print(f"--- SectionCreateView FORM_VALID: form.instance.school_class set to: {form.instance.school_class} ---") # DEBUG

        # Save the form (which saves the Section instance)
        # self.object will be set by CreateView's default form_valid if we call super()
        # or by form.save() if we override fully.
        try:
            self.object = form.save() # This should now save with school_class_id populated
            print(f"--- SectionCreateView FORM_VALID: self.object saved. school_class_id on object: {self.object.school_class_id} ---") # DEBUG
            messages.success(self.request, f"Section '{self.object.name}' created successfully for {self.school_class.name}.")
            return redirect(self.get_success_url())
        except IntegrityError as e:
            print(f"--- SectionCreateView FORM_VALID: INTEGRITY ERROR during form.save(): {e} ---") # DEBUG
            # Add the DB error to the form's non-field errors to display it
            form.add_error(None, f"Database integrity error: {e}. This usually means a required link (like School Class) was missing.")
            return self.form_invalid(form)
        except Exception as e:
            print(f"--- SectionCreateView FORM_VALID: UNEXPECTED ERROR during form.save(): {e} ---") # DEBUG
            form.add_error(None, f"An unexpected error occurred: {e}")
            return self.form_invalid(form)


    def get_success_url(self):
        return reverse('schools:section_list_for_class', kwargs={'class_pk': self.school_class.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['school_class'] = self.school_class # Pass to template for display
        context['view_title'] = f'Add New Section to {self.school_class.name}'
        return context

    def get_form_kwargs(self): # Pass school_class to form if needed by form's clean methods
        kwargs = super().get_form_kwargs()
        # kwargs['school_class_instance'] = self.school_class # Example if form needed it
        return kwargs
    


class SectionUpdateView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'schools.change_section'
    model = Section
    form_class = SectionForm
    template_name = 'schools/section_form.html'
    success_message = "Section '%(name)s' updated successfully."

    def get_object(self, queryset=None):
        # Get section by its own PK, but also ensure school_class is in context for breadcrumbs/titles
        obj = super().get_object(queryset)
        self.school_class = obj.school_class # Get related class for context
        return obj

    def get_success_url(self):
        return reverse_lazy('schools:section_list_for_class', kwargs={'class_pk': self.object.school_class.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Edit Section: {self.object.name} (Class: {self.object.school_class.name})"
        context['school_class'] = self.object.school_class # Pass class for breadcrumbs/title
        return context



class SectionDeleteView(LoginRequiredMixin, PermissionRequiredMixin,SuccessMessageMixin, DeleteView):
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'schools.delete_section'
    model = Section
    template_name = 'schools/section_confirm_delete.html'
    success_message = "Section deleted successfully."

    def get_success_url(self):
        # After deleting a section, redirect to the list of sections for its parent class
        return reverse_lazy('schools:section_list_for_class', kwargs={'class_pk': self.object.school_class.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Confirm Delete Section: {self.object.name}"
        context['parent_class_name'] = self.object.school_class.name
        return context



# --- AJAX view to load sections based on selected class ---
# This view should NOT require login if it's just fetching public data based on class_id,
# but if Section data is sensitive, add @login_required.
# For now, let's assume it's okay without login for populating a form dropdown.
def load_sections_for_class(request):
    class_id = request.GET.get('class_id')
    if class_id:
        try:
            # Ensure class_id is an integer
            class_id = int(class_id)
            # Query sections belonging to the specific class_id within the current tenant
            sections = Section.objects.filter(school_class_id=class_id).order_by('name')
            # Convert queryset to a list of dictionaries for JSON response
            data = list(sections.values('id', 'name'))
            return JsonResponse(data, safe=False)
        except ValueError:
            return JsonResponse({'error': 'Invalid class ID format.'}, status=400)
        except Exception as e:
            # Log the exception e for debugging
            print(f"Error in load_sections_for_class: {e}")
            return JsonResponse({'error': 'Error loading sections.'}, status=500)
    else:
        # Return empty list or error if no class_id provided
        return JsonResponse({'error': 'No class_id provided.'}, status=400)
    



# D:\school_fees_saas_v2\apps\schools\views.py

from django.shortcuts import render, redirect, reverse
from django.contrib.auth import login as auth_login, logout as auth_logout, authenticate
from django.contrib import messages
from django.utils.http import url_has_allowed_host_and_scheme # For 'next' URL validation

# Logging setup
import logging
logger = logging.getLogger(__name__) # Standard logger for this view file

# Import ParentUser from its correct location (CRITICAL - VERIFY THIS PATH)
try:
    from apps.students.models import ParentUser
except ImportError:
    ParentUser = None 
    logger.error("CRITICAL: ParentUser model could not be imported in schools.views. Parent login will fail.")
    # Consider raising ImproperlyConfigured if ParentUser is absolutely essential for this app's views.

# Import your ParentLoginForm (CRITICAL - VERIFY THIS PATH)
try:
    from .forms import ParentLoginForm # Assuming it's in apps/schools/forms.py
except ImportError:
    from django.contrib.auth.forms import AuthenticationForm # Basic fallback
    class ParentLoginForm(AuthenticationForm):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.fields['username'].label = "Parent Email" # Since ParentUser uses email for login
            self.fields['username'].widget.attrs.update({'placeholder': '<EMAIL>'})
    logger.warning("ParentLoginForm not found in schools.forms. Using basic AuthenticationForm fallback.")




def parent_login_view(request):
    logger.info(f"[PARENT_LOGIN_VIEW_TEST] Path: {request.path}, Host: {request.get_host()}")
    if hasattr(request, 'tenant') and request.tenant:
        logger.info(f"[PARENT_LOGIN_VIEW_TEST] Tenant found: {request.tenant.schema_name}, DB Schema: {connection.schema_name}")
        return HttpResponse(f"Tenant found: {request.tenant.name} (Schema: {request.tenant.schema_name}). DB connection schema: {connection.schema_name}")
    else:
        logger.error("[PARENT_LOGIN_VIEW_TEST] FATAL: request.tenant NOT FOUND on a tenant domain request!")
        return HttpResponse("Error: Tenant not found on request.", status=500)



# --- Parent Logout View ---
@login_required(login_url=reverse_lazy('schools:parent_login')) # Protects with parent login
def parent_logout_view(request):
    if not ParentUser or not isinstance(request.user, ParentUser):
        # If a non-parent user (or anonymous after a session issue) hits this,
        # just log out anything and redirect to parent login.
        auth_logout(request)
        messages.info(request, "You have been logged out.")
        return redirect(reverse('schools:parent_login'))

    parent_display_name = request.user.get_full_name() or getattr(request.user, request.user.USERNAME_FIELD)
    auth_logout(request)
    messages.info(request, f"Parent {parent_display_name}, you have been successfully logged out.")
    return redirect(reverse('schools:parent_login')) # Redirect back to parent login page


# --- Parent Dashboard View ---
# Defined with the correct login_url for its @login_required decorator
PARENT_LOGIN_URL_NAME_FOR_DASHBOARD = 'schools:parent_login' # Ensure this matches your URL name

@login_required(login_url=reverse_lazy(PARENT_LOGIN_URL_NAME_FOR_DASHBOARD))
def parent_dashboard_view(request):
    
    logger.info(f"[PARENT_DASHBOARD_VIEW_ENTER] Path: {request.path}, Method: {request.method}, User: {request.user}, User Type: {type(request.user)}")
    
    if not ParentUser: # Safety check
        messages.error(request, "Parent system is not properly configured.")
        
        logger.warning("[PARENT_DASHBOARD_VIEW] ParentUser model not imported. Redirecting to public_site:home.")
        
        return redirect(reverse('public_site:home'))

    # 1. Ensure the user is actually a ParentUser instance
    if not isinstance(request.user, ParentUser):
        user_email = getattr(request.user, 'email', 'Unknown user')
        messages.error(request, f"Access Denied. This area is for parent accounts. User '{user_email}' is not a parent.")
        auth_logout(request)
        logger.warning(f"[PARENT_DASHBOARD_VIEW] User {user_email} is not ParentUser. Redirecting to parent_login.")
        return redirect(PARENT_LOGIN_URL_NAME_FOR_DASHBOARD)

    # 2. Check if the PARENT_PORTAL feature is enabled
    # Assumes 'active_tenant_features' is added to request by your context processor
    if not hasattr(request, 'active_tenant_features') or \
    not request.active_tenant_features.get('PARENT_PORTAL', False):
        messages.warning(request, "The Parent Portal is not currently enabled for your school. Please contact administration.")
        auth_logout(request) # Log out the parent
        
        logger.warning(f"[PARENT_DASHBOARD_VIEW] PARENT_PORTAL feature for tenant '{request.tenant.name}' is FALSE. Redirecting.") # ADD/VERIFY THIS LOG
        
        return redirect(reverse('public_site:home')) # Redirect to a more general page

    logger.info(f"[PARENT_DASHBOARD_VIEW] All checks passed for {request.user.email}. Rendering parent dashboard.")

    try:
        parent_name = request.user.get_full_name() or getattr(request.user, request.user.USERNAME_FIELD)
    except AttributeError:
        parent_name = request.user.email # Fallback

    linked_students_qs = []
    if hasattr(request.user, 'students') and callable(getattr(request.user, 'students').all):
        linked_students_qs = request.user.students.all() # Assuming 'students' is related_name

    # Get announcements for parents
    try:
        from apps.announcements.models import Announcement
        parent_announcements = Announcement.get_parent_announcements(limit=5)
    except ImportError:
        parent_announcements = []

    context = {
        'tenant': request.tenant,
        'view_title': f"Parent Dashboard - {request.tenant.name}",
        'parent_name': parent_name,
        'linked_students': linked_students_qs,
        'announcements': parent_announcements,
    }
    return render(request, 'schools/parent_dashboard.html', context)



# apps/schools/views.py (or utils.py)

def calculate_reports_permissions(user, tenant_features_dict):
    """
    Calculate all report permissions and section visibility.
    This centralizes the complex logic and makes it testable.
    """
    # Helper to safely get feature flags
    def get_feature(key):
        return tenant_features_dict.get(key, False)

    # --- Visibility of individual report links ---
    show_outstanding_fees_link = (
        get_feature('REPORTS_BASIC') and 
        user.has_perm('reporting.view_outstanding_fees_report')
    )
    show_collection_report_link = (
        get_feature('REPORTS_BASIC') and 
        user.has_perm('reporting.view_collection_report')
    )
    show_trial_balance_link = (
        get_feature('REPORTS_ADVANCED') and 
        user.has_perm('reporting.view_trial_balance_report')
    )
    show_income_statement_link = (
        get_feature('REPORTS_ADVANCED') and 
        user.has_perm('reporting.view_income_expense_report')
    )
    show_balance_sheet_link = (
        get_feature('REPORTS_ADVANCED') and 
        user.has_perm('reporting.view_balance_sheet_report')
    )
    show_cash_flow_link = (
        get_feature('REPORTS_ADVANCED') and 
        user.has_perm('reporting.view_cash_flow_statement_report')
    )
    # Logic for budget variance link visibility based on original template:
    # Show if: BUDGETING is ON AND ( (REPORTS_ADVANCED is ON) OR (user has specific budget variance perm) )
    show_budget_variance_link = (
        get_feature('BUDGETING') and 
        (get_feature('REPORTS_ADVANCED') or user.has_perm('reporting.view_budget_variance_report'))
    )
    
    # --- Determine if any link in a category will be shown ---
    has_any_basic_report_link_to_show = show_outstanding_fees_link or show_collection_report_link
    
    has_any_advanced_financial_report_link_to_show = (
        show_trial_balance_link or show_income_statement_link or 
        show_balance_sheet_link or show_cash_flow_link
    )
    
    # --- Overall visibility for the "Reports" dropdown ---
    can_see_reports_dropdown = (
        has_any_basic_report_link_to_show or 
        has_any_advanced_financial_report_link_to_show or 
        show_budget_variance_link 
    )
    
    # --- Divider logic ---
    show_basic_advanced_divider = has_any_basic_report_link_to_show and has_any_advanced_financial_report_link_to_show
    
    show_advanced_budget_divider = (
        (has_any_basic_report_link_to_show or has_any_advanced_financial_report_link_to_show) and 
        show_budget_variance_link
    )
    # A slightly more accurate divider: show if advanced financial links are present AND budget link is present
    # This prevents a divider if only basic + budget are shown, or only budget.
    # show_advanced_budget_divider_refined = has_any_advanced_financial_report_link_to_show and show_budget_variance_link


    return {
        'show_outstanding_fees_link': show_outstanding_fees_link,
        'show_collection_report_link': show_collection_report_link,
        'show_trial_balance_link': show_trial_balance_link,
        'show_income_statement_link': show_income_statement_link,
        'show_balance_sheet_link': show_balance_sheet_link,
        'show_cash_flow_link': show_cash_flow_link,
        'show_budget_variance_link': show_budget_variance_link,
        
        'can_see_reports_dropdown': can_see_reports_dropdown,
        
        'show_basic_advanced_divider': show_basic_advanced_divider,
        'show_advanced_budget_divider': show_advanced_budget_divider, # Or use _refined if you prefer that logic
    }



from django.views.generic import DetailView
from django.contrib.auth.mixins import LoginRequiredMixin
from .models import StaffUser # Make sure StaffUser is imported
from django.core.exceptions import PermissionDenied # For permission checks

class StaffProfileDetailView(LoginRequiredMixin, DetailView):
    model = StaffUser
    template_name = 'schools/staff_profile_detail.html'
    context_object_name = 'staff_member'
    # permission_required = 'schools.view_staffuser' # Add if using PermissionRequiredMixin

    def get_object(self, queryset=None):
        obj = super().get_object(queryset)
        # Logic to ensure user can only view their own profile or if they have broader permissions
        if obj != self.request.user and not self.request.user.has_perm('schools.view_staffuser'): # Example admin perm
            # For a "My Profile" link, this check might be too strict if pk always matches request.user.pk
            # A simpler get_object for a "My Profile specific view":
            # if self.kwargs.get(self.pk_url_kwarg) == str(self.request.user.pk):
            #    return self.request.user
            # else:
            #    # Handle admin viewing other profiles, or raise PermissionDenied
            #    if not self.request.user.has_perm('schools.view_staffuser'):
            #        raise PermissionDenied("You cannot view this profile.")
            raise PermissionDenied("You do not have permission to view this profile.")
        return obj
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Profile: {self.object.get_full_name()}"
        context['is_own_profile'] = (self.object == self.request.user)
        return context



# apps/schools/views.py
from django.http import HttpResponse
from django.contrib.auth.decorators import login_required 
# from django.urls import reverse_lazy


@login_required #(login_url=reverse_lazy('schools:staff_login_view')) # Staff login
# @permission_required('schools.change_academicsetting', raise_exception=True) # Or your custom perm
def academic_settings_update_view(request): # Placeholder function
    # TODO: Implement actual academic settings update logic (likely a FormView or UpdateView for an AcademicSettings model or SchoolProfile)
    # For now, a placeholder:
    # logger.info(f"User {request.user.email} accessed academic_settings_update_view for tenant {request.tenant.name}")
    # context = {'view_title': "Update Academic Settings"}
    # return render(request, 'schools/academic_settings_form.html', context)
    return HttpResponse(f"Academic Settings Update Page for {request.tenant.name} (User: {request.user.email}) - Placeholder")

from django import forms
from django.utils.translation import gettext_lazy as _
from .models import AcademicSetting, AcademicYear

from .models import AcademicSetting # Assuming AcademicSetting is in schools.models
from .forms import AcademicSettingForm

class AcademicSettingUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = AcademicSetting # Not strictly used for get_object, but good for context
    form_class = AcademicSettingForm
    template_name = 'schools/academic_setting_form.html' # We'll create this template
    permission_required = 'schools.change_academicsetting' # Or a custom permission like 'schools.manage_academic_settings'
    
    # Redirect to the same page after successful update
    def get_success_url(self):
        messages.success(self.request, _("Academic settings updated successfully!"))
        return reverse_lazy('schools:academic_settings_update') 

    def get_object(self, queryset=None):
        # AcademicSetting is a singleton per tenant. Fetch or create it.
        # The get_instance() method on the model handles this.
        # If you don't have get_instance(), use get_or_create:
        obj, created = AcademicSetting.objects.get_or_create(pk=1) # Assuming pk=1 for singleton
        if created:
            # Optionally log or set default values if newly created
            logger.info(f"Created new AcademicSetting instance for tenant: {self.request.tenant.schema_name}")
        return obj

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = _("Update Academic Settings")
        context['tenant_name'] = self.request.tenant.name # For display in template
        return context

    def form_valid(self, form):
        # Optional: Add any specific logic before saving
        # form.instance.updated_by = self.request.user # If you track who updated
        response = super().form_valid(form)
        return response

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # kwargs['tenant'] = self.request.tenant # If form needs tenant
        return kwargs
    



from django.urls import reverse_lazy
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.db import transaction # For handling is_active logic

from .models import AcademicYear
from .forms import AcademicYearForm

class AcademicYearListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    model = AcademicYear
    template_name = 'schools/academic_year_list.html'
    context_object_name = 'academic_years'
    permission_required = 'schools.view_academicyear'
    login_url = reverse_lazy('schools:staff_login')
    paginate_by = 10

    def get_queryset(self):
        return AcademicYear.objects.all().order_by('-start_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Manage Academic Years"
        
        # User whose permissions we should check (from global_tenant_context processor)
        # The context processor puts it into 'user_for_tenant_perms'
        user_to_check = self.request.user_for_tenant_perms \
                        if hasattr(self.request, 'user_for_tenant_perms') \
                        else self.request.user # Fallback to request.user

        context['can_add_academic_year'] = user_to_check.has_perm('schools.add_academicyear')
        context['can_change_academic_year'] = user_to_check.has_perm('schools.change_academicyear') # Pass this
        context['can_delete_academic_year'] = user_to_check.has_perm('schools.delete_academicyear') # Pass this
        return context

class AcademicYearCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = AcademicYear
    form_class = AcademicYearForm
    template_name = 'schools/academic_year_form.html' # New template (reusable for update)
    permission_required = 'schools.add_academicyear'
    login_url = reverse_lazy('schools:staff_login')
    success_url = reverse_lazy('schools:academic_year_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Add New Academic Year"
        return context

    def form_valid(self, form):
        messages.success(self.request, "Academic Year created successfully!")
        # Logic to deactivate other active years if this one is set to active
        if form.cleaned_data.get('is_active'):
            with transaction.atomic():
                AcademicYear.objects.filter(is_active=True).update(is_active=False)
                # The super().form_valid(form) will save the current one as active
        return super().form_valid(form)

class AcademicYearUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = AcademicYear
    form_class = AcademicYearForm
    template_name = 'schools/academic_year_form.html' # Re-use form template
    permission_required = 'schools.change_academicyear'
    login_url = reverse_lazy('schools:staff_login')
    success_url = reverse_lazy('schools:academic_year_list')
    context_object_name = 'academic_year' # To refer to the object in template

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Edit Academic Year: {self.object.name}"
        return context

    def form_valid(self, form):
        messages.success(self.request, "Academic Year updated successfully!")
        # Logic to deactivate other active years if this one is set to active
        if form.cleaned_data.get('is_active'):
            with transaction.atomic():
                AcademicYear.objects.filter(is_active=True).exclude(pk=self.object.pk).update(is_active=False)
                # The super().form_valid(form) will save the current one as active
        return super().form_valid(form)

class AcademicYearDeleteView(LoginRequiredMixin, PermissionRequiredMixin, DeleteView):
    model = AcademicYear
    template_name = 'schools/confirm_delete.html' # Generic confirm delete template
    permission_required = 'schools.delete_academicyear'
    login_url = reverse_lazy('schools:staff_login')
    success_url = reverse_lazy('schools:academic_year_list')
    context_object_name = 'object_to_delete' # Generic name

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Delete Academic Year: {self.object.name}"
        context['delete_message'] = f"Are you sure you want to delete the academic year '{self.object.name}'? This might affect related terms and other records."
        context['cancel_url'] = self.success_url 
        return context
        
    def form_valid(self, form):
        # Check for related Terms before deleting
        if self.object.terms.exists(): # Assuming related_name='terms' on Term model
            messages.error(self.request, f"Cannot delete '{self.object.name}' as it has associated terms. Please delete or reassign them first.")
            return self.render_to_response(self.get_context_data(form=form)) # Re-render form with error

        messages.success(self.request, f"Academic Year '{self.object.name}' deleted successfully.")
        return super().form_valid(form)
    


from django.urls import reverse_lazy
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.shortcuts import get_object_or_404

from .models import AcademicYear, Term # Add Term
from .forms import AcademicYearForm, TermForm # Add TermForm



# --- Term Views (NEW) ---
class TermListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    model = Term
    template_name = 'schools/term_list.html' # New template
    context_object_name = 'terms'
    permission_required = 'schools.view_term'
    login_url = reverse_lazy('schools:staff_login')
    paginate_by = 10 # Or your preferred number

    def get_queryset(self):
        # Order by academic year (most recent first), then by term start date
        return Term.objects.select_related('academic_year').order_by('-academic_year__start_date', 'start_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Manage Terms / Semesters"
        
        user_to_check = getattr(self.request, 'user_for_tenant_perms', self.request.user)

        context['can_add_term'] = user_to_check.has_perm('schools.add_term')
        context['can_change_term'] = user_to_check.has_perm('schools.change_term') # Pass this
        context['can_delete_term'] = user_to_check.has_perm('schools.delete_term') # Pass this
        return context
    
    # def get_context_data(self, **kwargs):
    #     context = super().get_context_data(**kwargs)
    #     context['view_title'] = "Manage Terms / Semesters"
    #     context['can_add_term'] = self.request.user.has_perm('schools.add_term')
    #     return context

class TermCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Term
    form_class = TermForm
    template_name = 'schools/term_form.html' # New template (reusable for update)
    permission_required = 'schools.add_term'
    login_url = reverse_lazy('schools:staff_login')
    success_url = reverse_lazy('schools:term_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Add New Term / Semester"
        return context

    def form_valid(self, form):
        messages.success(self.request, "Term / Semester created successfully!")
        return super().form_valid(form)
    
    def form_invalid(self, form):
        messages.error(self.request, "Please correct the errors below.")
        return super().form_invalid(form)

class TermUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Term
    form_class = TermForm
    template_name = 'schools/term_form.html' # Re-use form template
    permission_required = 'schools.change_term'
    login_url = reverse_lazy('schools:staff_login')
    success_url = reverse_lazy('schools:term_list')
    context_object_name = 'term' 

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Edit Term: {self.object.name} ({self.object.academic_year.name})"
        return context

    def form_valid(self, form):
        messages.success(self.request, "Term / Semester updated successfully!")
        return super().form_valid(form)

    def form_invalid(self, form):
        messages.error(self.request, "Please correct the errors below.")
        return super().form_invalid(form)

class TermDeleteView(LoginRequiredMixin, PermissionRequiredMixin, DeleteView):
    model = Term
    template_name = 'schools/confirm_delete.html' # Re-use generic confirm delete template
    permission_required = 'schools.delete_term'
    login_url = reverse_lazy('schools:staff_login')
    success_url = reverse_lazy('schools:term_list')
    context_object_name = 'object_to_delete'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Delete Term: {self.object.name}"
        context['delete_message'] = (
            f"Are you sure you want to delete the term '{self.object.name}' "
            f"for the academic year '{self.object.academic_year.name}'? "
            "This action might affect related records like fee structures or invoices."
        )
        context['cancel_url'] = self.success_url 
        return context
        
    def form_valid(self, form):
        # Add checks here if terms should not be deleted if they have, e.g., associated invoices
        # For example:
        # if self.object.invoices.exists(): # Assuming related_name='invoices' from Invoice model to Term
        #     messages.error(self.request, f"Cannot delete '{self.object.name}' as it has associated invoices.")
        #     return self.render_to_response(self.get_context_data(form=form))
        
        messages.success(self.request, f"Term '{self.object.name}' deleted successfully.")
        return super().form_valid(form)
    


# # D:\school_fees_saas_v2\apps\schools\views.py

# from django.shortcuts import render, redirect
# from django.views.generic import TemplateView
# from django.utils.translation import gettext_lazy as _
# from django.utils import timezone
# from django.db.models import Q # For complex queries if needed later

# # Assuming your mixin for staff login is in common.mixins
# # Adjust the import path if it's elsewhere (e.g., this app's mixins.py)
# from apps.common.mixins import StaffLoginRequiredMixin # Or your actual mixin name

# # Import models needed for the dashboard context
# from .models import SchoolProfile # StaffUser is request.user via mixin
# from apps.students.models import Student # Example: to show student count
# from apps.fees.models import Invoice # Example: to show outstanding invoice count
# from apps.announcements.models import Announcement # For recent announcements

# import logging
# logger = logging.getLogger(__name__)

# class SchoolsDashboardView(StaffLoginRequiredMixin, TemplateView):
#     template_name = 'schools/dashboard.html' # We will create/update this template

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         tenant = self.request.tenant
#         staff_user = self.request.user # This is a StaffUser instance due to StaffLoginRequiredMixin

#         context['view_title'] = _("School Admin Dashboard")
#         context['staff_user'] = staff_user # Pass the staff user object if needed in template

#         # --- Fetch School Profile (already in tenant_base via request.tenant.schoolprofile) ---
#         # However, if you need to pass it explicitly or ensure it's handled if missing:
#         try:
#             context['school_profile'] = tenant.schoolprofile
#         except SchoolProfile.DoesNotExist:
#             context['school_profile'] = None
#             logger.warning(f"SchoolsDashboardView: SchoolProfile not found for tenant {tenant.name}")
#         except AttributeError: # If 'schoolprofile' related_name isn't set up on tenant model
#             context['school_profile'] = None
#             logger.warning(f"SchoolsDashboardView: Tenant model for {tenant.name} has no 'schoolprofile' attribute.")


#         # --- Example Dashboard Stats (customize as needed) ---
#         # These queries are tenant-specific because models are accessed within tenant schema context
#         try:
#             context['active_student_count'] = Student.objects.filter(is_active=True).count()
            
#             # Example: Outstanding Invoices Count
#             outstanding_statuses = [
#                 Invoice.InvoiceStatus.SENT,
#                 Invoice.InvoiceStatus.PARTIALLY_PAID,
#                 Invoice.InvoiceStatus.OVERDUE
#             ]
#             context['outstanding_invoice_count'] = Invoice.objects.filter(status__in=outstanding_statuses).count()

#             # Example: Recently Joined Staff (if StaffUser model is in this app)
#             # from .models import StaffUser # If StaffUser is here
#             # context['recent_staff_count'] = StaffUser.objects.filter(
#             #     date_joined__gte=timezone.now() - timezone.timedelta(days=30)
#             # ).count()

#         except Exception as e:
#             logger.error(f"SchoolsDashboardView: Error fetching dashboard stats for tenant {tenant.name}: {e}", exc_info=True)
#             context['active_student_count'] = 'N/A'
#             context['outstanding_invoice_count'] = 'N/A'


#         # --- Fetch Recent Announcements for Staff ---
#         now = timezone.now()
#         recent_announcements_qs = Announcement.objects.none() # Default to empty
#         try:
#             # Announcements can be tenant-specific or global (platform-wide to admins)
#             # The filter here assumes announcements relevant to this tenant's staff
#             # Adjust target_audience_type based on your Announcement model's design
#             relevant_audience_types = ['ALL_STAFF', 'ALL_USERS_IN_TENANT']
#             # If you have roles like 'TEACHERS', 'ADMIN_STAFF', you might filter based on user's group/role
            
#             # Q object for announcements targeted at this tenant OR global announcements
#             # This assumes your Announcement model has a 'tenant' ForeignKey (nullable for global)
#             # and an 'is_global' BooleanField.
            
#             q_tenant_specific = Q(tenant=tenant, is_global=False)
#             q_global_to_staff = Q(tenant__isnull=True, is_global=True, target_audience_type__in=['ALL_STAFF', 'PLATFORM_WIDE_TO_ADMINS']) # Example for global

#             recent_announcements_qs = Announcement.objects.filter(
#                 q_tenant_specific | q_global_to_staff # Show tenant's own OR global ones for staff
#             ).filter(
#                 is_active=True,
#                 publish_date__lte=now
#             ).filter(
#                 Q(expiry_date__gte=now) | Q(expiry_date__isnull=True)
#             ).filter(
#                 target_audience_type__in=relevant_audience_types # Further filter by audience if not global
#             ).distinct().order_by('-publish_date')[:3] # Get latest 3, use distinct if Q join causes duplicates
            
#             logger.debug(f"SchoolsDashboardView: Fetched {recent_announcements_qs.count()} announcements for tenant {tenant.name}")
#         except Exception as e:
#             logger.error(f"SchoolsDashboardView: Error fetching announcements for tenant {tenant.name}: {e}", exc_info=True)
#         context['recent_announcements'] = recent_announcements_qs

#         # Add any other context data needed for the staff dashboard
#         # context['quick_actions'] = [
#         #     {'name': 'Add New Student', 'url_name': 'students:student_create', 'icon': 'bi-person-plus-fill', 'perm': 'students.add_student'},
#         #     {'name': 'Create Invoice', 'url_name': 'fees:invoice_create', 'icon': 'bi-receipt', 'perm': 'fees.add_invoice'},
#         # ]
        
#         return context

# # You might also have function-based views or other class-based views in this file.
# # We are just adding/focusing on the SchoolsDashboardView here.

