# apps/fees/signals.py
from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import Invoice, InvoiceStatus # Your Invoice model and status choices
from apps.communication.tasks import send_invoice_issued_email_task
from apps.students.models import ParentUser
from django.conf import settings # For APP_SCHEME, APP_HOSTNAME if needed for URL
from django.urls import reverse
from django_tenants.utils import get_tenant_model # To get the School model

import logging
logger = logging.getLogger(__name__)

@receiver(post_save, sender=Invoice)
def invoice_status_changed_send_notification(sender, instance, created, **kwargs):
    invoice = instance
    
    # Check if the invoice status is SENT and if this is a status change (not just any save)
    # To detect a change, you need the previous status. Signals don't provide this directly.
    # A common pattern is to check if 'update_fields' was passed and 'status' is in it,
    # or by comparing the current status with a status stored before saving (harder with signals).

    # Simpler: Send if status is SENT and it wasn't just created with SENT status
    # (unless you want to send on creation if status is immediately SENT).
    # A field like 'notification_sent_at' on Invoice can prevent re-sending.

    if invoice.status == InvoiceStatus.SENT:
        # Check if notification was already sent for this invoice to avoid duplicates
        if hasattr(invoice, 'notification_sent_at') and invoice.notification_sent_at: # Add 'notification_sent_at' DateTimeField(null=True, blank=True) to Invoice model
            logger.info(f"Notification for invoice {invoice.invoice_number} already sent at {invoice.notification_sent_at}. Skipping.")
            return

        try:
            # Get the tenant (School instance)
            # This assumes your Invoice model has a ForeignKey to Student,
            # and Student has a ForeignKey to School (the tenant) or can be derived.
            # If Invoice is directly in a tenant schema, you might need to get tenant from current connection.
            # For simplicity, let's assume invoice.student.school gives the tenant instance.
            # However, for tasks, it's better to pass schema_name explicitly.
            
            # The signal handler runs in the context of the schema where the Invoice was saved.
            # We need the public School model to get its schema_name for the Celery task.
            TenantModel = get_tenant_model()
            # How to get the public tenant model instance from within the signal handler?
            # If invoice.student.school is the tenant-specific School model from apps.schools.models:
            # tenant_public_model_instance = TenantModel.objects.get(schema_name=invoice.student.school.schema_name)
            # This is tricky. It's often easier if the Celery task itself can derive the schema_name
            # from a passed model PK that exists in public, or if the trigger passes the schema_name.

            # Let's assume the current schema context is the one we need for the task.
            from django.db import connection
            current_schema_name = connection.schema_name
            if current_schema_name == 'public': # Or your actual public schema name
                logger.warning(f"Invoice {invoice.invoice_number} saved in public schema. Not sending tenant notification.")
                return # Should not happen for tenant invoices

            invoice_items_data = [{'desc': detail.description, 'amount': detail.amount} for detail in invoice.details.all()]
            invoice_details_dict = {
                'invoice_number': invoice.invoice_number,
                'issue_date': invoice.issue_date,
                'due_date': invoice.due_date,
                'amount_due': invoice.balance_due,
                'items': invoice_items_data,
            }
            student_name_for_email = invoice.student.get_full_name()
            parents_to_notify = invoice.student.guardians.filter(is_active=True, receive_notifications=True)

            if not parents_to_notify.exists():
                logger.warning(f"No active parents found with notifications enabled for student {invoice.student} of invoice {invoice.invoice_number} in schema {current_schema_name}.")


            for parent_user_instance in parents_to_notify:
                if parent_user_instance.email:
                    send_invoice_issued_email_task.delay(
                        parent_user_instance.id,
                        current_schema_name, # Pass the schema name
                        invoice_details_dict,
                        student_name_for_email
                    )
                    logger.info(f"Queued invoice issued email for invoice {invoice.invoice_number} to parent {parent_user_instance.email} in schema {current_schema_name}")
                else:
                    logger.warning(f"Invoice {invoice.invoice_number} issued, but parent {parent_user_instance.id} for student {invoice.student} in schema {current_schema_name} has no email.")
            
            # Mark notification as sent
            if hasattr(invoice, 'notification_sent_at'):
                from django.utils import timezone
                invoice.notification_sent_at = timezone.now()
                invoice.save(update_fields=['notification_sent_at']) # Save without triggering signal again

        except Exception as e:
            logger.error(f"Failed to queue invoice issued email for invoice {invoice.invoice_number} from signal: {e}", exc_info=True)
            
            
            