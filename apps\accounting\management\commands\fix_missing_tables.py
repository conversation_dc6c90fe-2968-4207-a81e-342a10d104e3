from django.core.management.base import BaseCommand, CommandError
from django.db import connection, transaction
from django_tenants.utils import get_tenant_model
from django.core.management import call_command
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Fix missing database tables by re-running specific migrations'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-schema',
            type=str,
            default='alpha',
            help='Tenant schema name to fix (default: alpha)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes'
        )

    def handle(self, *args, **options):
        tenant_schema = options['tenant_schema']
        dry_run = options['dry_run']
        
        try:
            # Get the tenant
            TenantModel = get_tenant_model()
            tenant = TenantModel.objects.get(schema_name=tenant_schema)
            
            # Switch to tenant schema
            connection.set_tenant(tenant)
            
            self.stdout.write(f"Working on tenant schema: {tenant_schema}")
            
            # Check what tables exist
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = %s AND table_name LIKE %s 
                    ORDER BY table_name;
                """, [tenant_schema, 'fees_%'])
                results = cursor.fetchall()
                existing_tables = [row[0] for row in results]
                
                expected_tables = [
                    'fees_concessiontype',
                    'fees_feehead', 
                    'fees_feestructure',
                    'fees_invoice',
                    'fees_invoicedetail',
                    'fees_feestructureitem',
                    'fees_studentconcession',
                    'fees_studentfeeallocation'
                ]
                
                self.stdout.write("Expected fees tables:")
                for table in expected_tables:
                    exists = table in existing_tables
                    status = '✓' if exists else '✗'
                    self.stdout.write(f"  {status} {table}")
                
                missing_tables = [table for table in expected_tables if table not in existing_tables]

                if missing_tables:
                    self.stdout.write(self.style.WARNING(f"Missing tables: {missing_tables}"))
                else:
                    self.stdout.write(self.style.SUCCESS("All expected tables exist!"))

                # Always check for missing columns, even if all tables exist
                self.stdout.write("Checking for missing columns in existing tables...")

                # Check fees_feestructureitem for missing description column
                cursor.execute("""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_schema = %s AND table_name = 'fees_feestructureitem' AND column_name = 'description';
                """, [tenant_schema])

                missing_description = not cursor.fetchone()

                # Check fees_invoicedetail for missing amount column
                cursor.execute("""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_schema = %s AND table_name = 'fees_invoicedetail' AND column_name = 'amount';
                """, [tenant_schema])

                missing_invoicedetail_amount = not cursor.fetchone()

                # Check fees_concessiontype for missing type column
                cursor.execute("""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_schema = %s AND table_name = 'fees_concessiontype' AND column_name = 'type';
                """, [tenant_schema])

                missing_concessiontype_type = not cursor.fetchone()

                # Check fees_invoice for missing columns and problematic columns
                cursor.execute("""
                    SELECT column_name, is_nullable, column_default
                    FROM information_schema.columns
                    WHERE table_schema = %s AND table_name = 'fees_invoice';
                """, [tenant_schema])

                invoice_columns_info = {row[0]: {'nullable': row[1], 'default': row[2]} for row in cursor.fetchall()}
                existing_invoice_columns = list(invoice_columns_info.keys())

                expected_invoice_columns = [
                    'subtotal_amount', 'total_concession_amount', 'amount_paid',
                    'notes_to_parent', 'internal_notes', 'created_by_id'
                ]
                missing_invoice_columns = [col for col in expected_invoice_columns if col not in existing_invoice_columns]

                # Check for problematic old columns that need to be made nullable or removed
                problematic_columns = []
                old_columns_to_fix = ['subtotal_amount_calc', 'discount_amount_calc', 'total_amount', 'period_description_override']
                for col in old_columns_to_fix:
                    if col in invoice_columns_info and invoice_columns_info[col]['nullable'] == 'NO':
                        problematic_columns.append(col)

                if not missing_tables and not missing_description and not missing_invoice_columns and not problematic_columns and not missing_invoicedetail_amount and not missing_concessiontype_type:
                    self.stdout.write(self.style.SUCCESS("All tables and columns are correct!"))
                    return

                if dry_run:
                    if missing_tables:
                        self.stdout.write(self.style.WARNING("DRY RUN: Would create missing tables"))
                    if missing_description:
                        self.stdout.write(self.style.WARNING("DRY RUN: Would add missing description column to fees_feestructureitem"))
                    if missing_invoice_columns:
                        self.stdout.write(self.style.WARNING(f"DRY RUN: Would add missing columns to fees_invoice: {missing_invoice_columns}"))
                    if problematic_columns:
                        self.stdout.write(self.style.WARNING(f"DRY RUN: Would make problematic columns nullable: {problematic_columns}"))
                    if missing_invoicedetail_amount:
                        self.stdout.write(self.style.WARNING("DRY RUN: Would add missing amount column to fees_invoicedetail"))
                    if missing_concessiontype_type:
                        self.stdout.write(self.style.WARNING("DRY RUN: Would add missing type column to fees_concessiontype"))
                    return
                
                # Create missing tables and fix missing columns
                with transaction.atomic():
                    if 'fees_feehead' in missing_tables:
                        self.stdout.write("Creating missing fees_feehead table...")

                        # Create the fees_feehead table with the correct schema
                        cursor.execute("""
                            CREATE TABLE fees_feehead (
                                id bigserial NOT NULL PRIMARY KEY,
                                name varchar(150) NOT NULL UNIQUE,
                                description text,
                                income_account_link_id bigint REFERENCES accounting_account(id) ON DELETE SET NULL,
                                is_active boolean NOT NULL DEFAULT true,
                                created_at timestamp with time zone NOT NULL DEFAULT NOW(),
                                updated_at timestamp with time zone NOT NULL DEFAULT NOW()
                            );
                        """)

                        # Create indexes
                        cursor.execute("""
                            CREATE INDEX fees_feehead_income_account_link_id_idx ON fees_feehead(income_account_link_id);
                        """)

                        self.stdout.write(self.style.SUCCESS("Created fees_feehead table"))

                    # Add missing columns
                    if missing_description:
                        self.stdout.write("Adding missing description column to fees_feestructureitem...")
                        cursor.execute("""
                            ALTER TABLE fees_feestructureitem
                            ADD COLUMN description varchar(255);
                        """)
                        self.stdout.write(self.style.SUCCESS("Added description column to fees_feestructureitem"))

                    # Add missing invoice columns
                    for column in missing_invoice_columns:
                        self.stdout.write(f"Adding missing {column} column to fees_invoice...")

                        if column in ['subtotal_amount', 'total_concession_amount', 'amount_paid']:
                            cursor.execute(f"""
                                ALTER TABLE fees_invoice
                                ADD COLUMN {column} numeric(12,2) NOT NULL DEFAULT 0.00;
                            """)
                        elif column in ['notes_to_parent', 'internal_notes']:
                            cursor.execute(f"""
                                ALTER TABLE fees_invoice
                                ADD COLUMN {column} text;
                            """)
                        elif column == 'created_by_id':
                            cursor.execute(f"""
                                ALTER TABLE fees_invoice
                                ADD COLUMN {column} bigint REFERENCES users_staffuser(id) ON DELETE SET NULL;
                            """)

                        self.stdout.write(self.style.SUCCESS(f"Added {column} column to fees_invoice"))

                    # Fix problematic old columns by making them nullable
                    for column in problematic_columns:
                        self.stdout.write(f"Making problematic column {column} nullable...")
                        cursor.execute(f"""
                            ALTER TABLE fees_invoice
                            ALTER COLUMN {column} DROP NOT NULL;
                        """)
                        self.stdout.write(self.style.SUCCESS(f"Made {column} column nullable"))

                    # Add missing amount column to fees_invoicedetail
                    if missing_invoicedetail_amount:
                        self.stdout.write("Adding missing amount column to fees_invoicedetail...")
                        cursor.execute("""
                            ALTER TABLE fees_invoicedetail
                            ADD COLUMN amount numeric(12,2) NOT NULL DEFAULT 0.00;
                        """)
                        self.stdout.write(self.style.SUCCESS("Added amount column to fees_invoicedetail"))

                    # Add missing type column to fees_concessiontype
                    if missing_concessiontype_type:
                        self.stdout.write("Adding missing type column to fees_concessiontype...")
                        cursor.execute("""
                            ALTER TABLE fees_concessiontype
                            ADD COLUMN type varchar(20) NOT NULL DEFAULT 'PERCENTAGE';
                        """)
                        self.stdout.write(self.style.SUCCESS("Added type column to fees_concessiontype"))

                    # Update the migration record to reflect that the migration is properly applied
                    cursor.execute("""
                        UPDATE django_migrations
                        SET applied = NOW()
                        WHERE app = 'fees' AND name = '0001_initial'
                    """)

                    self.stdout.write(self.style.SUCCESS("Updated migration record timestamp"))
                    
                    # Verify the fix
                    cursor.execute("""
                        SELECT table_name 
                        FROM information_schema.tables 
                        WHERE table_schema = %s AND table_name LIKE %s 
                        ORDER BY table_name;
                    """, [tenant_schema, 'fees_%'])
                    results = cursor.fetchall()
                    existing_tables_after = [row[0] for row in results]
                    
                    self.stdout.write("Tables after fix:")
                    for table in expected_tables:
                        exists = table in existing_tables_after
                        status = '✓' if exists else '✗'
                        self.stdout.write(f"  {status} {table}")
                        
        except Exception as e:
            raise CommandError(f"Error fixing missing tables: {e}")
