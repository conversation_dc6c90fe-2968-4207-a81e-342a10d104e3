{# D:\school_fees_saas_v2\apps\parent_portal\templates\parent_portal\dashboard.html #}
{% extends "parent_portal/parent_portal_base.html" %} {# 1. VERIFY THIS PATH to parent_portal_base.html #}

{% load static humanize i18n %}

{% block parent_portal_page_title %}{{ view_title|default:_("Parent Dashboard") }}{% endblock parent_portal_page_title %}

{% block extra_parent_portal_css %} {# This is a hook in parent_portal_base.html for page-specific CSS #}
    {{ block.super }} {# Good practice #}
    <style>
        .dashboard-card { margin-bottom: 1.5rem; }
        .student-list-item { 
            padding: 0.75rem 1.25rem; 
            border-bottom: 1px solid #eee; 
        }
        .student-list-item:last-child { border-bottom: none; }
        .fees-summary-value { font-size: 1.5rem; font-weight: 700; }
        .text-outstanding { color: var(--bs-danger, #dc3545); }

        /* Styles for student avatar in the list */
        .student-avatar-sm {
            width: 60px; /* Adjust size as needed */
            height: 60px;
            object-fit: cover;
            border: 2px solid #eee;
            margin-right: 0.75rem; /* Space between avatar and text */
        }
        .student-avatar-placeholder-sm {
            font-size: 2.5rem; /* Adjust icon size */
            width: 60px;
            height: 60px;
            line-height: 56px; /* Adjust for vertical centering with border */
            border: 2px solid #eee;
            border-radius: 50%;
            margin-right: 0.75rem;
            color: #6c757d; /* Muted color */
        }
    </style>
{% endblock extra_parent_portal_css %}

{% block parent_portal_main_content %}
<div class="container mt-4">

    {# Welcome Message & School Info #}
    <div class="row mb-4 align-items-center">
        <div class="col-md-8">
            <h1 class="mb-0 display-5">{{ view_title|default:_("My Dashboard") }}</h1>
            <p class="lead text-muted">{% blocktrans with full_name=parent.get_full_name|default:parent.email %}Welcome back, {{ full_name }}!{% endblocktrans %}</p>
        </div>
        {% if school_profile and school_profile.logo %}
            <div class="col-md-4 text-md-end">
                <img src="{{ school_profile.logo.url }}" alt="{% if school_profile.school_name_on_reports %}{{ school_profile.school_name_on_reports }}{% else %}{{ request.tenant.name }}{% endif %} Logo" style="max-height: 60px; max-width: 180px;" class="rounded img-fluid">
            </div>
        {% elif school_profile %}
            <div class="col-md-4 text-md-end">
                <h4 class="fw-light">{{ school_profile.school_name_on_reports|default:request.tenant.name }}</h4>
            </div>
        {% endif %}
    </div>

    {# Display messages from views #}
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="row">
        {# Column 1: Children's Information #}
        <div class="col-lg-7">
            <div class="card dashboard-card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="bi bi-people-fill me-2"></i>{% trans "Your Children" %}</h5>
                </div>
                <div class="card-body p-0">
                    {% if linked_students %}
                        <ul class="list-group list-group-flush">
                            {% for student in linked_students %}
                            <li class="list-group-item student-list-item">
                                <div class="d-flex align-items-center">
                                    {# === INSERTED STUDENT AVATAR/PHOTO BLOCK === #}
                                    <div>
                                        {% if student.photo and student.photo.url %}
                                            <img src="{{ student.photo.url }}" 
                                                alt="{% blocktrans with student_name=student.get_full_name|default:student.first_name %}Photo of {{ student_name }}{% endblocktrans %}" 
                                                class="rounded-circle student-avatar-sm">
                                        {% else %}
                                            <div class="text-center student-avatar-placeholder-sm">
                                                <i class="bi bi-person-circle"></i>
                                            </div>
                                        {% endif %}
                                    </div>
                                    {# === END OF AVATAR/PHOTO BLOCK === #}
                                    
                                    <div class="flex-grow-1">
                                        <strong>{{ student.get_full_name|default:"Student Name Missing" }}</strong><br>
                                        <small class="text-muted">
                                            {% trans "Admission No" %}: {{ student.admission_number|default:"N/A" }} |
                                            {% trans "Class" %}: {{ student.current_class.name|default:"N/A" }}{% if student.current_section %} - {{ student.current_section.name }}{% endif %}
                                        </small>
                                    </div>
                                    <div class="ms-auto mt-2 mt-md-0 text-nowrap">
                                        <a href="{% url 'parent_portal:student_detail' student_pk=student.pk %}" class="btn btn-sm btn-outline-info me-1" title="{% trans 'View Details for' %} {{ student.first_name|default:'this student' }}">
                                            <i class="bi bi-eye-fill"></i> {% trans "Details" %}
                                        </a>
                                        <a href="{% url 'parent_portal:student_fees' student_pk=student.pk %}" class="btn btn-sm btn-outline-success" title="{% trans 'View Fees for' %} {{ student.first_name|default:'this student' }}">
                                            <i class="bi bi-receipt"></i> {% trans "Fees" %}
                                        </a>
                                    </div>
                                </div>
                            </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <div class="card-body text-center">
                            <p class="mb-0 p-3">
                                <i class="bi bi-info-circle me-2"></i>
                                {% trans "No students are currently linked to your parent account." %}
                                {% if school_profile %}
                                    {% trans "Please contact the school" %}
                                    {% if school_profile.contact_email %}
                                        {% trans "at" %} <a href="mailto:{{ school_profile.contact_email }}">{{ school_profile.contact_email }}</a>
                                    {% elif school_profile.contact_phone %}
                                        {% trans "at" %} {{ school_profile.contact_phone }}
                                    {% endif %}
                                    {% trans "if this is incorrect." %}
                                {% else %}
                                    {% trans "Please contact the school administration if this is incorrect." %}
                                {% endif %}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>
            {# School Announcements #}
            <div class="card dashboard-card shadow-sm">
                {% if announcements %}
                    {% include "announcements/widgets/parent_announcement_widget.html" with announcements=announcements %}
                {% else %}
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-bell-fill me-2"></i>
                            {% trans "School Announcements" %}
                        </h5>
                    </div>
                    <div class="card-body text-center py-4">
                        <i class="bi bi-megaphone display-4 text-muted mb-3"></i>
                        <h6 class="text-muted">{% trans "No announcements at this time" %}</h6>
                        <p class="text-muted small mb-0">{% trans "Check back later for important updates from the school." %}</p>
                    </div>
                {% endif %}
            </div>
        </div>

        {# Column 2: Fees Summary & Other Info #}
        <div class="col-lg-5">
            <div class="card dashboard-card shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="bi bi-cash-coin me-2"></i>{% trans "Fees Summary" %}</h5>
                </div>
                <div class="card-body text-center">
                    <p class="card-text text-muted mt-2 mb-1">{% trans "Total Outstanding Fees for All Children" %}:</p>
                    <p class="card-text fees-summary-value text-outstanding mb-3">
                        {{ school_profile.currency_symbol|default:"$" }}{{ total_outstanding_fees|floatformat:2|intcomma }} {# Corrected to total_outstanding_fees #}
                    </p>
                    <a href="{% if tenant_features.ONLINE_PAYMENTS and total_outstanding_fees > 0 %}{% url 'parent_portal:make_payment_summary' %}{% else %}#{% endif %}" 
                        class="btn btn-primary mt-2 w-100 {% if not tenant_features.ONLINE_PAYMENTS or total_outstanding_fees <= 0 %}disabled{% endif %}" 
                        title="{% if tenant_features.ONLINE_PAYMENTS and total_outstanding_fees > 0 %}{% trans 'Make Payment' %}{% elif not tenant_features.ONLINE_PAYMENTS %}{% trans 'Online Payments Not Currently Enabled' %}{% else %}{% trans 'No Outstanding Fees to Pay' %}{% endif %}">
                        <i class="bi bi-credit-card-fill me-2"></i>{% trans "Make a Payment" %}
                    </a>
                    <a href="{% url 'parent_portal:payment_history_all' %}" class="btn btn-outline-secondary mt-2 w-100">
                        <i class="bi bi-clock-history me-2"></i>{% trans "View Payment History" %}
                    </a>
                </div>
            </div>

            <div class="card dashboard-card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-link-45deg me-2"></i>{% trans "Quick Links" %}</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'parent_portal:profile_display' %}" class="list-group-item list-group-item-action"><i class="bi bi-person-badge me-2"></i>{% trans "My Profile" %}</a>
                    <a href="#" class="list-group-item list-group-item-action disabled" title="{% trans 'Coming Soon' %}"><i class="bi bi-envelope-paper me-2"></i>{% trans "Contact School" %}</a>
                    <a href="{% url 'school_calendar:calendar' %}" class="list-group-item list-group-item-action"><i class="bi bi-calendar-event me-2"></i>{% trans "School Calendar" %}</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock parent_portal_main_content %}

