{# D:\school_fees_saas_v2\templates\schools\school_profile_form.html #}
{% extends "tenant_base.html" %}

{% load static i18n %} {# Assuming you use i18n for _() in labels/help_texts from form #}

{% block title %}{{ view_title|default:"School Profile" }} - {{ tenant_name|default:request.tenant.name }}{% endblock %}

{% block page_specific_css %}
    {{ block.super }}
    {# If tenant_base.html doesn't load tenant_form_styles.css, uncomment:   <link rel="stylesheet" href="{% static 'css/tenant_form_styles.css' %}">     #}
{% endblock %}

{% block content %}
<div class="container mt-4 mb-5">
    <div class="pagetitle mb-3">
        <h1>{{ view_title|default:"Edit School Profile & Settings" }}</h1>
        <nav>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item active">School Profile</li>
            </ol>
        </nav>
    </div>

    {% include "partials/_messages.html" %} {# For Django messages framework #}

    <form method="post" enctype="multipart/form-data" novalidate>
        {% csrf_token %}
        
        {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    <p class="mb-0">{{ error }}</p>
                {% endfor %}
            </div>
        {% endif %}

        {% for hidden_field in form.hidden_fields %}
            {{ hidden_field }}
            {% for error in hidden_field.errors %}
                <div class="alert alert-danger mt-1 small">{{ hidden_field.label_tag }}: {{ error }}</div>
            {% endfor %}
        {% endfor %}

        {# --- Basic School Information Card --- #}
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light py-3">
                <h5 class="mb-0">Basic School Information</h5>
            </div>
            <div class="card-body p-4">
                <div class="row g-3">
                    
                    {% if form.school_name_override %}
                    <div class="col-md-12">
                        {{ form.school_name_override.label_tag }}
                        {{ form.school_name_override }}
                        {% if form.school_name_override.help_text %}<small class="form-text text-muted helptext">{{ form.school_name_override.help_text|safe }}</small>{% endif %}
                        {% for error in form.school_name_override.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                    </div>
                    {% endif %}

                    {% if form.school_motto %}
                    <div class="col-md-12">
                        {{ form.school_motto.label_tag }}
                        {{ form.school_motto }}
                        {% if form.school_motto.help_text %}<small class="form-text text-muted helptext">{{ form.school_motto.help_text|safe }}</small>{% endif %}
                        {% for error in form.school_motto.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                    </div>
                    {% endif %}
                    
                    {# CORRECTED field name from 'contact_email' to 'school_email' #}
                    {% if form.school_email %} 
                    <div class="col-md-6">
                        {{ form.school_email.label_tag }}
                        {{ form.school_email }}
                        {% if form.school_email.help_text %}<small class="form-text text-muted helptext">{{ form.school_email.help_text|safe }}</small>{% endif %}
                        {% for error in form.school_email.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                    </div>
                    {% endif %}

                    {# CORRECTED field name from 'contact_phone' to 'phone_number' #}
                    {% if form.phone_number %} 
                    <div class="col-md-6">
                        {{ form.phone_number.label_tag }}
                        {{ form.phone_number }}
                        {% if form.phone_number.help_text %}<small class="form-text text-muted helptext">{{ form.phone_number.help_text|safe }}</small>{% endif %}
                        {% for error in form.phone_number.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                    </div>
                    {% endif %}

                    <div class="col-12"><hr class="my-3"><h6>Address</h6></div>

                    {% if form.address_line1 %}<div class="col-12">{{ form.address_line1.label_tag }} {{ form.address_line1 }} {% if form.address_line1.help_text %}<small class="form-text text-muted helptext">{{ form.address_line1.help_text|safe }}</small>{% endif %} {% for error in form.address_line1.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}</div>{% endif %}
                    {% if form.address_line2 %}<div class="col-12">{{ form.address_line2.label_tag }} {{ form.address_line2 }} {% if form.address_line2.help_text %}<small class="form-text text-muted helptext">{{ form.address_line2.help_text|safe }}</small>{% endif %} {% for error in form.address_line2.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}</div>{% endif %}
                    {% if form.city %}<div class="col-md-6">{{ form.city.label_tag }} {{ form.city }} {% if form.city.help_text %}<small class="form-text text-muted helptext">{{ form.city.help_text|safe }}</small>{% endif %} {% for error in form.city.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}</div>{% endif %}
                    {% if form.state_province %}<div class="col-md-6">{{ form.state_province.label_tag }} {{ form.state_province }} {% if form.state_province.help_text %}<small class="form-text text-muted helptext">{{ form.state_province.help_text|safe }}</small>{% endif %} {% for error in form.state_province.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}</div>{% endif %}
                    {% if form.postal_code %}<div class="col-md-6">{{ form.postal_code.label_tag }} {{ form.postal_code }} {% if form.postal_code.help_text %}<small class="form-text text-muted helptext">{{ form.postal_code.help_text|safe }}</small>{% endif %} {% for error in form.postal_code.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}</div>{% endif %}
                    
                    {# CORRECTED field name from 'country' to 'country_name' #}
                    {% if form.country_name %}
                    <div class="col-md-6">
                        {{ form.country_name.label_tag }}
                        {{ form.country_name }}
                        {% if form.country_name.help_text %}<small class="form-text text-muted helptext">{{ form.country_name.help_text|safe }}</small>{% endif %}
                        {% for error in form.country_name.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                    </div>
                    {% endif %}

                    <div class="col-12"><hr class="my-3"><h6>Financial & Academic Settings</h6></div>
                    
                    {% if form.school_name_on_reports %}<div class="col-md-6">{{ form.school_name_on_reports.label_tag }} {{ form.school_name_on_reports }} {% if form.school_name_on_reports.help_text %}<small class="form-text text-muted helptext">{{ form.school_name_on_reports.help_text|safe }}</small>{% endif %} {% for error in form.school_name_on_reports.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}</div>{% endif %}
                    {% if form.currency_symbol %}<div class="col-md-6">{{ form.currency_symbol.label_tag }} {{ form.currency_symbol }} {% if form.currency_symbol.help_text %}<small class="form-text text-muted helptext">{{ form.currency_symbol.help_text|safe }}</small>{% endif %} {% for error in form.currency_symbol.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}</div>{% endif %}
                    {% if form.financial_year_start_month %}<div class="col-md-6">{{ form.financial_year_start_month.label_tag }} {{ form.financial_year_start_month }} {% if form.financial_year_start_month.help_text %}<small class="form-text text-muted helptext">{{ form.financial_year_start_month.help_text|safe }}</small>{% endif %} {% for error in form.financial_year_start_month.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}</div>{% endif %}
                    {% if form.default_due_days %}<div class="col-md-6">{{ form.default_due_days.label_tag }} {{ form.default_due_days }} {% if form.default_due_days.help_text %}<small class="form-text text-muted helptext">{{ form.default_due_days.help_text|safe }}</small>{% endif %} {% for error in form.default_due_days.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}</div>{% endif %}
                    {% if form.current_academic_year %}<div class="col-md-6">{{ form.current_academic_year.label_tag }} {{ form.current_academic_year }} {% if form.current_academic_year.help_text %}<small class="form-text text-muted helptext">{{ form.current_academic_year.help_text|safe }}</small>{% endif %} {% for error in form.current_academic_year.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}</div>{% endif %}

                    {% if form.logo %}
                    <div class="col-md-6">
                        {{ form.logo.label_tag }}
                        {% if form.instance and form.instance.logo and form.instance.logo.url %}
                            <div class="mb-2">Current Logo: <a href="{{ form.instance.logo.url }}" target="_blank"><img src="{{ form.instance.logo.url }}" alt="Current Logo" style="max-height: 50px; max-width: 150px; border: 1px solid #ddd; padding: 2px;"></a></div>
                        {% endif %}
                        {{ form.logo }}
                        {% if form.logo.help_text %}<small class="form-text text-muted helptext">{{ form.logo.help_text|safe }}</small>{% endif %}
                        {% for error in form.logo.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                    </div>
                    {% endif %}
                </div> {# End .row g-3 #}
            </div> {# End .card-body #}
        </div> {# End .card #}

        {# --- Accounting Linkages Card --- #}
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light py-3">
                <h5 class="mb-0">Accounting Linkages (Default CoA Selections)</h5>
            </div>
            <div class="card-body p-4">
                <p class="text-muted"><small>Select the corresponding accounts from your Chart of Accounts that the system will use for automated postings.</small></p>
                
                {# CORRECTED field name #}
                {% if form.default_accounts_receivable_coa %}
                <div class="mb-3">
                    {{ form.default_accounts_receivable_coa.label_tag }}
                    {{ form.default_accounts_receivable_coa }}
                    {% if form.default_accounts_receivable_coa.help_text %}<small class="form-text text-muted helptext">{{ form.default_accounts_receivable_coa.help_text|safe }}</small>{% endif %}
                    {% for error in form.default_accounts_receivable_coa.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                </div>
                {% endif %}

                {% if form.default_cash_coa %}
                <div class="mb-3">
                    {{ form.default_cash_coa.label_tag }}
                    {{ form.default_cash_coa }}
                    {% if form.default_cash_coa.help_text %}<small class="form-text text-muted helptext">{{ form.default_cash_coa.help_text|safe }}</small>{% endif %}
                    {% for error in form.default_cash_coa.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                </div>
                {% endif %}

                {% if form.default_bank_coa %}
                <div class="mb-3">
                    {{ form.default_bank_coa.label_tag }}
                    {{ form.default_bank_coa }}
                    {% if form.default_bank_coa.help_text %}<small class="form-text text-muted helptext">{{ form.default_bank_coa.help_text|safe }}</small>{% endif %}
                    {% for error in form.default_bank_coa.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                </div>
                {% endif %}

                {# ADDED missing default_fee_income_coa #}
                {% if form.default_fee_income_coa %}
                <div class="mb-3">
                    {{ form.default_fee_income_coa.label_tag }}
                    {{ form.default_fee_income_coa }}
                    {% if form.default_fee_income_coa.help_text %}<small class="form-text text-muted helptext">{{ form.default_fee_income_coa.help_text|safe }}</small>{% endif %}
                    {% for error in form.default_fee_income_coa.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                </div>
                {% endif %}

                {# CORRECTED field name #}
                {% if form.default_discount_given_coa %}
                <div class="mb-3">
                    {{ form.default_discount_given_coa.label_tag }}
                    {{ form.default_discount_given_coa }}
                    {% if form.default_discount_given_coa.help_text %}<small class="form-text text-muted helptext">{{ form.default_discount_given_coa.help_text|safe }}</small>{% endif %}
                    {% for error in form.default_discount_given_coa.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                </div>
                {% endif %}
                
                {# ADDED missing default_expense_coa #}
                {% if form.default_expense_coa %}
                <div class="mb-3">
                    {{ form.default_expense_coa.label_tag }}
                    {{ form.default_expense_coa }}
                    {% if form.default_expense_coa.help_text %}<small class="form-text text-muted helptext">{{ form.default_expense_coa.help_text|safe }}</small>{% endif %}
                    {% for error in form.default_expense_coa.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                </div>
                {% endif %}
            </div> {# End .card-body #}
        </div> {# End .card #}

        {{ form.media }} {# Important for forms with JS/CSS based widgets like Select2, DatePicker etc. #}

        <div class="mt-4 text-center fixed-bottom bg-light py-3 border-top"> {# Example fixed submit bar #}
            <button type="submit" class="btn btn-success btn-lg me-2">
                <i class="bi bi-check-circle me-2"></i>Save Profile & Settings
            </button>
            <a href="{% url 'schools:dashboard' %}" class="btn btn-secondary btn-lg">
                <i class="bi bi-x-circle me-2"></i>Cancel
            </a>
        </div>
    </form>
</div>
{% endblock %}
