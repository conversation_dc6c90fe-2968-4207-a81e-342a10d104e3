#!/usr/bin/env python
"""
Test script to verify Chart of Accounts seeding for new tenants.
This script creates a test tenant and verifies that the COA is automatically seeded.
"""

import os
import sys
import django

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import transaction
from django_tenants.utils import schema_context
from apps.tenants.models import School
from apps.users.models import User
from apps.accounting.models import Account, AccountType


def test_coa_seeding():
    """Test that COA is automatically seeded for new tenants."""
    
    print("🧪 Testing Chart of Accounts seeding for new tenants...")
    
    # Create a test owner user
    test_email = "<EMAIL>"
    
    # Clean up any existing test data
    try:
        existing_user = User.objects.get(email=test_email)
        print(f"🧹 Cleaning up existing test user: {test_email}")
        existing_user.delete()
    except User.DoesNotExist:
        pass
    
    try:
        existing_tenant = School.objects.get(schema_name="test_coa")
        print(f"🧹 Cleaning up existing test tenant: test_coa")
        existing_tenant.delete()
    except School.DoesNotExist:
        pass
    
    # Create test owner
    print(f"👤 Creating test owner: {test_email}")
    owner = User.objects.create_user(
        email=test_email,
        password="testpass123",
        first_name="Test",
        last_name="Owner"
    )
    
    # Create test tenant
    print("🏫 Creating test tenant: test_coa")
    with transaction.atomic():
        tenant = School.objects.create(
            schema_name="test_coa",
            name="Test COA School",
            owner=owner,
            domain_url="test-coa.myapp.test"
        )
    
    print(f"✅ Tenant created: {tenant.schema_name}")
    
    # Check if COA was seeded
    print("🔍 Checking if Chart of Accounts was seeded...")
    
    with schema_context(tenant.schema_name):
        account_count = Account.objects.count()
        print(f"📊 Total accounts in tenant: {account_count}")
        
        if account_count > 0:
            print("✅ Chart of Accounts was successfully seeded!")
            
            # Show some sample accounts
            print("\n📋 Sample accounts:")
            for account in Account.objects.filter(parent_account=None).order_by('code')[:5]:
                print(f"  - {account.code}: {account.name} ({account.account_type.classification})")
                
            # Check hierarchical structure
            assets = Account.objects.filter(code="1000").first()
            if assets:
                children_count = assets.get_children().count()
                print(f"  - Assets account has {children_count} child accounts")
                
        else:
            print("❌ Chart of Accounts was NOT seeded!")
            return False
    
    # Clean up test data
    print("\n🧹 Cleaning up test data...")
    tenant.delete()
    owner.delete()
    
    print("✅ Test completed successfully!")
    return True


if __name__ == "__main__":
    try:
        success = test_coa_seeding()
        if success:
            print("\n🎉 All tests passed! COA seeding is working correctly.")
            sys.exit(0)
        else:
            print("\n❌ Tests failed! COA seeding needs attention.")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
