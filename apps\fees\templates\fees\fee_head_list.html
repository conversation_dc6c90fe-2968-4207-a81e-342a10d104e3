{# D:\school_fees_saas_v2\apps\fees\templates\fees\fee_head_list.html #}
{% extends "tenant_base.html" %}
{% load static humanize core_tags %}

{% block title %}{{ view_title|default:"Manage Fee Heads" }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1>{{ view_title|default:"Manage Fee Heads" }}</h1>
        <a href="{% url 'fees:fee_head_create' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle-fill me-1"></i> Add New Fee Head
        </a>
    </div>

    {% include "partials/_messages.html" %}

    {% if fee_heads %} {# This uses the direct queryset from the ListView #}
        <div class="table-responsive">
            <table class="table table-striped table-hover table-sm">
                <thead class="table-light">
                    <tr>
                        <th>Name</th>
                        <th>Description</th>
                        <th>Linked Income Account (CoA)</th>
                        <th>Active</th> {# Added for completeness #}
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for fee_head_item in fee_heads %} {# CHANGED: Loop through fee_heads #}
                    <tr>
                        <td>{{ fee_head_item.name }}</td>
                        <td>{{ fee_head_item.description|truncatewords:15|default:"-" }}</td>
                        <td>
                            {% if fee_head_item.income_account %}
                                {{ fee_head_item.income_account.name }} 
                                {% if fee_head_item.income_account.code %}
                                    ({{ fee_head_item.income_account.code }})
                                {% endif %}
                            {% else %}
                                N/A
                            {% endif %}
                        </td>
                        <td>{{ fee_head_item.is_active|yesno:"Yes,No" }}</td> {# Displaying is_active #}
                        <td>
                            <a href="{% url 'fees:fee_head_update' fee_head_item.pk %}" class="btn btn-sm btn-outline-primary me-1" title="Edit">
                                <i class="bi bi-pencil-square"></i>
                            </a>
                            <a href="{% url 'fees:fee_head_delete' fee_head_item.pk %}" class="btn btn-sm btn-outline-danger" title="Delete">
                                <i class="bi bi-trash"></i>
                            </a>
                        </td>
                    </tr>
                    {% empty %} {# Added Django's built-in empty for querysets #}
                        <tr>
                            <td colspan="5" class="text-center">No fee heads found.</td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% if page_obj %} {# Only include pagination if page_obj exists #}
            {% include "partials/_pagination.html" with page_obj=page_obj %}
        {% endif %}
    {% else %}
        <div class="alert alert-info mt-3">No fee heads have been defined yet. <a href="{% url 'fees:fee_head_create' %}" class="alert-link">Add one now</a>.</div>
    {% endif %}

    <div class="mt-3">
        <a href="{% url 'schools:dashboard' %}" class="btn btn-secondary">Back to Dashboard</a>
    </div>
</div>
{% endblock %}



























{% comment %} {# D:\school_fees_saas_v2\apps\fees\templates\fees\fee_head_list.html #}
{% extends "tenant_base.html" %}
{% load static humanize core_tags %} {# Load tags if needed #}

{% block title %}{{ view_title|default:"Manage Fee Heads" }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1>{{ view_title|default:"Manage Fee Heads" }}</h1>
        <a href="{% url 'fees:fee_head_create' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle-fill me-1"></i> Add New Fee Head
        </a>
    </div>

    {% include "partials/_messages.html" %}

    {% if fee_heads %} {# context_object_name from FeeHeadListView #}
        <div class="table-responsive">
            <table class="table table-striped table-hover table-sm">
                <thead class="table-light">
                    <tr>
                        <th>Name</th>
                        <th>Description</th>
                        <th>Linked Income Account (CoA)</th>
                        <th>Actions</th>
                    </tr>
                </thead>

                <tbody>
                    {% for item in fee_heads_processed %} {# Loop through processed list #}
                    <tr>
                        <td>{{ item.object.name }}</td>
                        <td>{{ item.object.description|truncatewords:15|default:"-" }}</td>
                        <td>
                            {{ item.linked_account.name }} 
                            {% if item.linked_account.code != "N/A" %}
                                ({{ item.linked_account.code }})
                            {% endif %}
                        </td>
                        <td>
                            <a href="{% url 'fees:fee_head_update' item.object.pk %}" ...>Edit</a>
                            <a href="{% url 'fees:fee_head_delete' item.object.pk %}" ...>Delete</a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                
            </table>
        </div>
        {% include "partials/_pagination.html" with page_obj=page_obj %}
    {% else %}
        <div class="alert alert-info">No fee heads have been defined yet.</div>
    {% endif %}

    <div class="mt-3">
        <a href="{% url 'schools:dashboard' %}" class="btn btn-secondary">Back to Dashboard</a>
    </div>
</div>
{% endblock %}
 {% endcomment %}
