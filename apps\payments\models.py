from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from decimal import Decimal
from django.core.validators import MinValueValidator
from django.conf import settings # For STAFF_USER_MODEL and PARENT_USER_MODEL

# Ensure correct import paths for related models
# Adjust these paths if your models are located elsewhere
from apps.students.models import Student, ParentUser
from apps.fees.models import Invoice
from apps.accounting.models import Account
from apps.schools.models import AcademicYear
from apps.common.models import BaseModel # Assuming PaymentMethod uses this


class PaymentMethod(BaseModel): # Assuming BaseModel provides created_at, updated_at, etc.
    PAYMENT_METHOD_TYPE_CHOICES = [
        ('BANK_TRANSFER', _('Bank Transfer')),
        ('CASH', _('Cash')),
        ('MOBILE_MONEY', _('Mobile Money')),
        ('CARD_PAYMENT', _('Card Payment (Generic)')), # For actual online gateway transactions
        ('ONLINE_MOCK', _('Online Mock Payment')),    # For our simulation
        ('CHEQUE', _('Cheque')),
        ('INTERNAL_TRANSFER', _('Internal/Wallet Transfer')),
        ('SCHOLARSHIP_WAIVER', _('Scholarship/Waiver Application')),
        ('OTHER', _('Other')),
    ]
    name = models.CharField(
        max_length=100,
        unique=True, # Ensure this is unique per tenant if not schema-isolated & has tenant FK
        help_text=_("Name of the payment method (e.g., Cash, Online Portal Payment, Bank Transfer - Zenith).")
    )
    type = models.CharField(
        _("method type"),
        max_length=30,
        choices=PAYMENT_METHOD_TYPE_CHOICES,
        default='OTHER',
        help_text=_("The general type of this payment method.")
    )
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    linked_account = models.ForeignKey(
        Account,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='payment_methods_linked', # Clearer related name
        limit_choices_to={'account_type__balance_type': 'DEBIT', 'account_type__category': 'ASSET'}, # Guide user
        help_text=_("The Cash, Bank, or Payment Gateway Clearing account in CoA.")
    )
    # tenant = models.ForeignKey(settings.TENANT_MODEL, on_delete=models.CASCADE) # Add if not schema-isolated

    class Meta:
        ordering = ['name']
        verbose_name = _("Payment Method")
        verbose_name_plural = _("Payment Methods")
        # unique_together = ('tenant', 'name') # If has tenant FK

    def __str__(self):
        return f"{self.name} ({self.get_type_display()})"


class Payment(models.Model):
    # Choices from your existing Payment model
    TYPE_FEE = 'FEE'
    TYPE_OTHER_INCOME = 'OTHER_INCOME'
    TYPE_REFUND_OUT = 'REFUND_OUT'
    PAYMENT_TYPE_CHOICES = [
        (TYPE_FEE, _('Fee Payment')),
        (TYPE_OTHER_INCOME, _('Other Income')),
        (TYPE_REFUND_OUT, _('Refund Issued')),
    ]

    STATUS_PENDING = 'PENDING'
    STATUS_COMPLETED = 'COMPLETED'
    STATUS_FAILED = 'FAILED'
    STATUS_CANCELLED = 'CANCELLED'
    STATUS_REVERSED = 'REVERSED' # If you handle payment reversals
    STATUS_REFUNDED = 'REFUNDED' # Full refund
    STATUS_PARTIALLY_REFUNDED = 'PARTIALLY_REFUNDED'
    PAYMENT_STATUS_CHOICES = [
        (STATUS_PENDING, _('Pending Confirmation')),
        (STATUS_COMPLETED, _('Completed Successfully')),
        (STATUS_FAILED, _('Failed')),
        (STATUS_CANCELLED, _('Cancelled')),
        (STATUS_REVERSED, _('Reversed/Chargeback')), # e.g. bank reversed it
        (STATUS_REFUNDED, _('Fully Refunded')),
        (STATUS_PARTIALLY_REFUNDED, _('Partially Refunded')),
    ]

    # Who initiated/owns this payment (especially for online portal payments)
    parent_payer = models.ForeignKey(
        ParentUser, # Directly using imported ParentUser
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='payments_made' # payments made by this parent
    )
    # Optional: if a payment is directly recorded against a student by staff, not via parent portal
    student = models.ForeignKey(
        Student,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='payments_for_student'
    )
    payment_method = models.ForeignKey(
        PaymentMethod,
        on_delete=models.PROTECT,
        related_name='payments_using_this_method',
        null= True, blank=True
    )
    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.PROTECT,
        related_name='payments_in_year',
        null=True, blank=False # Usually, a payment relates to an academic year
    )
    amount = models.DecimalField( # Your existing 'amount' field
        _("payment amount"),
        max_digits=12, decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))]
    )
    payment_date = models.DateTimeField( # Your existing DateTimeField
        default=timezone.now,
        help_text=_("Date and time the payment was received/confirmed.")
    )
    transaction_reference = models.CharField( # Renamed from 'reference_number' for clarity
        _("transaction reference"),
        max_length=150, blank=True, null=True,
        help_text=_("E.g., Gateway Transaction ID, Cheque number, bank slip ID.")
    )
    notes = models.TextField(_("notes"), blank=True, null=True)
    payment_type = models.CharField(
        _("type of payment"), max_length=20, # Your existing length
        choices=PAYMENT_TYPE_CHOICES, default=TYPE_FEE
    )
    status = models.CharField(
        _("payment status"), max_length=20, # Your existing length
        choices=PAYMENT_STATUS_CHOICES,
        default=STATUS_PENDING, # Online payments often start pending
        help_text=_("The current status of this payment transaction.")
    )
    # Who recorded this in the system if by staff?
    # Your 'recorded_by' linked to STAFF_USER_MODEL is good for this.
    # Let's rename it for clarity if 'created_by' will be the parent for online payments.
    processed_by_staff = models.ForeignKey(
        settings.STAFF_USER_MODEL, # From your settings
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='processed_payments'
    )
    # If ParentUser is your AUTH_USER_MODEL or you have a generic 'created_by'
    created_by = models.ForeignKey( # Tracks who initiated, could be ParentUser or StaffUser
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL, null=True, blank=True,
        related_name='created_all_payments' # Generic related name
    )
    unallocated_amount = models.DecimalField(
        _("unallocated amount"),
        max_digits=12, decimal_places=2, default=Decimal('0.00'),
        help_text=_("Amount of this payment not yet allocated to any invoice (e.g., overpayment, advance).")
    )
    created_at = models.DateTimeField(auto_now_add=True) # Your existing field
    updated_at = models.DateTimeField(auto_now=True)    # Your existing field
    # tenant = models.ForeignKey(settings.TENANT_MODEL, on_delete=models.CASCADE) # If not schema-isolated

    class Meta:
        ordering = ['-payment_date', '-created_at']
        verbose_name = _("Payment") # Simpler name
        verbose_name_plural = _("Payments")

    def __str__(self):
        payer_display = "Unknown Payer"
        if self.parent_payer:
            payer_display = self.parent_payer.get_full_name() or str(self.parent_payer.email)
        elif self.student:
            payer_display = f"For {self.student.get_full_name()}"
        elif self.created_by: # Fallback to creator if no specific payer
            payer_display = f"By {self.created_by.get_full_name() or str(self.created_by.email)}"
        
        return f"Payment P-{self.pk or 'New'} of {self.amount} by {payer_display} ({self.get_status_display()})"

    @property
    def transaction_account(self):
        if self.payment_method and self.payment_method.linked_account:
            return self.payment_method.linked_account
        return None

    @property
    def total_allocated(self):
        # Sums up amounts from related PaymentAllocation records
        # Assumes PaymentAllocation model has related_name='allocations' to Payment
        if self.pk and hasattr(self, 'allocations'): # Check if instance is saved and has allocations manager
            return self.allocations.aggregate(total=Coalesce(models.Sum('amount_allocated'), Decimal('0.00')))['total']
        return Decimal('0.00')
    
    @property
    def calculated_unallocated_amount(self): # More explicit name
        return self.amount - self.total_allocated

    def save(self, *args, **kwargs):
        # It's generally better to calculate and set unallocated_amount
        # in the service layer where allocations are made, to avoid recursion
        # or stale data if total_allocated relies on a query.
        # If you do set it here, ensure total_allocated is efficient.
        # For example:
        # if self.pk: # Only if object is already saved and might have allocations
        #     self.unallocated_amount = self.amount - self.total_allocated() # Call as method if it queries
        super().save(*args, **kwargs)


class PaymentAllocation(models.Model):
    payment = models.ForeignKey(Payment, on_delete=models.CASCADE, related_name='allocations', null=True, blank=True)
    invoice = models.ForeignKey(Invoice, on_delete=models.PROTECT, related_name='payment_allocations', null=True, blank=True) # PROTECT invoice
    amount_allocated = models.DecimalField(
        _("amount allocated"),
        max_digits=12, decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))]
    )
    allocation_date = models.DateField( # Date the allocation was effectively made
        _("allocation date"),
        default=timezone.now
    )
    # created_by = models.ForeignKey(settings.STAFF_USER_MODEL, ...) # Optional: who did the allocation if manual
    created_at = models.DateTimeField(auto_now_add=True) # Timestamp of record creation
    # tenant = models.ForeignKey(settings.TENANT_MODEL, on_delete=models.CASCADE) # If not schema-isolated

    class Meta:
        ordering = ['allocation_date', 'pk']
        unique_together = ('payment', 'invoice') # A payment can be allocated to an invoice only once
        verbose_name = _("Payment Allocation")
        verbose_name_plural = _("Payment Allocations")

    def __str__(self):
        return f"P-{self.payment.pk}: {self.amount_allocated} allocated to Inv-{self.invoice.invoice_number if self.invoice else 'N/A'}"

    def clean(self):
        super().clean()
        if self.amount_allocated <= Decimal('0.00'):
            raise ValidationError(_("Allocated amount must be positive."))
        
        # These validations are complex and better handled in the service creating the allocation.
        # Reason: A formset for allocations would handle checking against total payment amount.
        # Checking against invoice balance is also tricky here without knowing previous allocations.
        # if self.pk: # If updating an existing allocation
        #     original_allocation = PaymentAllocation.objects.get(pk=self.pk)
        #     # Check if update exceeds payment's remaining unallocated amount (considering this allocation's original amount)
        #     if self.payment.calculated_unallocated_amount + original_allocation.amount_allocated < self.amount_allocated:
        #         raise ValidationError(_("Allocation update exceeds available unallocated payment amount."))
        #     # Check if update exceeds invoice's remaining balance_due (considering this allocation's original amount)
        #     if self.invoice.balance_due + original_allocation.amount_allocated < self.amount_allocated:
        #         raise ValidationError(_("Allocation update exceeds invoice balance due."))
        # else: # New allocation
        #     if self.payment.calculated_unallocated_amount < self.amount_allocated:
        #         raise ValidationError(_("Allocation exceeds available unallocated payment amount."))
        #     if self.invoice.balance_due < self.amount_allocated:
        #         raise ValidationError(_("Allocation exceeds invoice balance due."))

    def save(self, *args, **kwargs):
        # Critical: Logic to update Invoice.amount_paid and Invoice.status
        # should happen when a PaymentAllocation is saved.
        # This is best handled in a service function or using signals to ensure atomicity
        # and prevent circular dependencies or race conditions.
        # Direct manipulation in save() here can be risky if not managed carefully with transactions.
        # For now, this save method is simple. The service function record_parent_payment will handle this.
        super().save(*args, **kwargs)
        
        
        
        