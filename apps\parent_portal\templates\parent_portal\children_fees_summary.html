{# D:\school_fees_saas_v2\apps\parent_portal\templates\parent_portal\children_fees_summary.html #}
{% extends "tenant_base.html" %} {# Or your specific parent portal base #}
{% load static humanize %}

{% block tenant_page_title %}{{ view_title|default:"Children's Fees Summary" }}{% endblock tenant_page_title %}

{% block page_specific_css %}
    {{ block.super }}
    <style>
        .summary-table th, .summary-table td {
            vertical-align: middle;
        }
        .currency-value {
            text-align: right;
        }
        .text-outstanding {
            color: var(--bs-danger); /* Or your preferred color for outstanding amounts */
            font-weight: bold;
        }
        .text-paid {
            color: var(--bs-success);
        }
        .text-invoiced {
            color: var(--bs-info);
        }
    </style>
{% endblock page_specific_css %}

{% block tenant_specific_content %} {# Or {% block content %} if your base is different #}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="mb-1">{{ view_title }}</h1>
            <p class="lead text-muted">For {{ parent.get_full_name|default:parent.email }}</p>
        </div>
        {% if school_profile and school_profile.logo %}
            <img src="{{ school_profile.logo.url }}" alt="{{ request.tenant.name }} Logo" style="max-height: 60px; max-width: 180px;" class="rounded">
        {% endif %}
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    {% if children_fee_details %}
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">Fees Breakdown by Child</h5>
            </div>
            <div class="table-responsive">
                <table class="table table-hover summary-table mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Child Name</th>
                            <th>Admission No.</th>
                            <th>Class</th>
                            <th class="currency-value">Total Invoiced</th>
                            <th class="currency-value">Total Paid</th>
                            <th class="currency-value">Total Outstanding</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for child_detail in children_fee_details %}
                        <tr>
                            <td><strong>{{ child_detail.student_obj.full_name }}</strong></td>
                            <td>{{ child_detail.student_obj.admission_number }}</td>
                            <td>{{ child_detail.student_obj.current_class|default:"N/A" }} {{ child_detail.student_obj.current_section|default:"" }}</td>
                            <td class="currency-value text-invoiced">{{ school_profile.currency_symbol|default:"$" }}{{ child_detail.total_invoiced|floatformat:2|intcomma }}</td>
                            <td class="currency-value text-paid">{{ school_profile.currency_symbol|default:"$" }}{{ child_detail.total_paid|floatformat:2|intcomma }}</td>
                            <td class="currency-value {% if child_detail.has_outstanding %}text-outstanding{% endif %}">
                                {{ school_profile.currency_symbol|default:"$" }}{{ child_detail.total_outstanding|floatformat:2|intcomma }}
                            </td>
                            <td>
                                <a href="{% url 'parent_portal:student_fees' student_pk=child_detail.student_obj.pk %}" class="btn btn-sm btn-outline-primary" title="View Detailed Fees for {{ child_detail.student_obj.first_name }}">
                                    <i class="bi bi-receipt"></i> View Invoices
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-group-divider">
                        <tr class="fw-bold table-secondary">
                            <td colspan="3" class="text-end">Grand Totals:</td>
                            <td class="currency-value text-invoiced">{{ school_profile.currency_symbol|default:"$" }}{{ grand_total_invoiced|floatformat:2|intcomma }}</td>
                            <td class="currency-value text-paid">{{ school_profile.currency_symbol|default:"$" }}{{ grand_total_paid|floatformat:2|intcomma }}</td>
                            <td class="currency-value text-outstanding">{{ school_profile.currency_symbol|default:"$" }}{{ grand_total_outstanding|floatformat:2|intcomma }}</td>
                            <td></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>

        {% if grand_total_outstanding > 0 %}
        <div class="text-center mb-4">
            <a href="#" class="btn btn-lg btn-success disabled {% if not tenant_features.ONLINE_PAYMENTS %}d-none{% endif %}" title="Make Payment (Coming Soon)">
                <i class="bi bi-credit-card-fill me-2"></i>Make a Payment for Outstanding Fees
            </a>
        </div>
        {% endif %}

    {% else %}
        <div class="alert alert-info">
            <i class="bi bi-info-circle me-2"></i>
            No student fee information to display. If you believe this is an error, please contact the school.
        </div>
    {% endif %}

    <div class="text-center mt-3">
        <a href="{% url 'parent_portal:dashboard' %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left-circle me-1"></i>Back to Dashboard
        </a>
    </div>

</div>
{% endblock tenant_specific_content %}

