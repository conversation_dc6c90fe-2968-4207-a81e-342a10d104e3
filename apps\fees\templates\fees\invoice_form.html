{% extends "tenant_base.html" %}
{% load static humanize %}

{% block title %}{{ view_title|default:"Invoice Form" }} - {{ request.tenant.name }}{% endblock %}

{% block extra_tenant_css %}
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" />
    <style>
        .formset-item-container {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            margin-bottom: 1.5rem;
            background-color: #f8f9fa;
            box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,.075);
        }
        .formset-item-header {
            background-color: #e9ecef;
            padding: 0.75rem 1.25rem;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .formset-item-header h6 {
            margin-bottom: 0;
            font-weight: 600;
            color: #212529;
        }
        .formset-item-body {
            padding: 1.25rem;
        }
        .select2-container .select2-selection--single { height: calc(1.5em + 0.75rem + 2px) !important; }
        .select2-container--default .select2-selection--single .select2-selection__rendered { line-height: calc(1.5em + 0.75rem) !important; padding-left: 0.75rem !important; }
        .select2-container--default .select2-selection--single .select2-selection__arrow { height: calc(1.5em + 0.75rem) !important; }
        
        .errorlist {
            color: var(--bs-danger);
            font-size: 0.875em;
            list-style: none;
            padding-left: 0;
            margin-top: .25rem;
        }
        .invalid-feedback.d-block { /* Ensure our manual error divs also look like Bootstrap's */
            display: block !important; /* Override if needed */
            width: 100%;
            margin-top: .25rem;
            font-size: .875em;
            color: var(--bs-danger);
        }
        /* Specific styling for form field wrappers (adjust if your structure is different) */
        .field-wrapper { /* Add this class to divs wrapping label+input+errors */
            margin-bottom: 1rem; /* Standard Bootstrap spacing */
        }
    </style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    {% include "partials/_breadcrumb.html" %}

    <div class="row justify-content-center">
        <div class="col-xl-10 col-lg-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h4 class="mb-0">{{ view_title|default:"Invoice" }}</h4>
                </div>
                <div class="card-body p-lg-4 p-3">
                    {% include "partials/_messages.html" %}

                    <form method="post" novalidate id="invoiceForm" enctype="multipart/form-data">
                        {% csrf_token %}
                        {{ form.media }}

                        <h5 class="mb-3 text-primary border-bottom pb-2"><i class="bi bi-file-earmark-text me-2"></i>Invoice Information</h5>
                        <div class="row g-3 mb-4">
                            {% for field in form %}
                                {% if field.is_hidden %}
                                    {{ field }}
                                {% else %}
                                    <div class="col-md-{% if field.name == 'notes_to_parent' or field.name == 'internal_notes' %}12{% elif field.name == 'student' or field.name == 'academic_year' %}6{% else %}4{% endif %} field-wrapper">
                                        {{ field.label_tag }}
                                        {{ field }}
                                        {% if field.help_text %}<div class="form-text text-muted small">{{ field.help_text|safe }}</div>{% endif %}
                                        {% for error in field.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                    </div>
                                {% endif %}
                            {% endfor %}
                            {% if form.non_field_errors %}
                                <div class="col-12 mt-3">
                                    {% for error in form.non_field_errors %}<div class="alert alert-danger py-2">{{ error }}</div>{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        {% if object and not object.is_editable %}
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>This invoice is not in a draft state. Main details and items may be locked or restricted.
                            </div>
                        {% endif %}

                        <hr class="my-4">

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0 text-primary"><i class="bi bi-list-ol me-2"></i>Line Items</h5>
                            {% if not object or object.is_editable %}
                            <button type="button" id="add-invoice-item-form" class="btn btn-sm btn-outline-success">
                                <i class="bi bi-plus-lg me-1"></i> Add Item
                            </button>
                            {% endif %}
                        </div>

                        {{ item_formset.management_form }}
                        {% if item_formset.non_form_errors %}
                            <div class="alert alert-danger">
                                {% for error in item_formset.non_form_errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}

                        <div id="invoice-items-formset-container">
                            {% for item_form in item_formset.forms %}
                                <div class="formset-item-container" id="{{ item_form.prefix }}-container">
                                    <div class="formset-item-header">
                                        <h6>Item #<span class="item-number">{{ forloop.counter }}</span></h6>
                                        {% if item_formset.can_delete and not object or object.is_editable %}
                                            {% if forloop.counter0 >= item_formset.initial_form_count or not item_form.instance.pk %}
                                                <button type="button" class="btn-close remove-invoice-item-form" aria-label="Remove item" title="Remove this item"></button>
                                            {% endif %}
                                        {% endif %}
                                    </div>
                                    <div class="formset-item-body">
                                        {% if item_form.non_field_errors %}
                                            <div class="alert alert-danger py-2">
                                                {% for error in item_form.non_field_errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                        {% for hidden_field in item_form.hidden_fields %}{{ hidden_field }}{% endfor %}
                                        <div class="row g-3">
                                            {# Render each field with its errors #}
                                            <div class="col-md-3 field-wrapper field-line_type">
                                                {{ item_form.line_type.label_tag }}
                                                {{ item_form.line_type }}
                                                {% for error in item_form.line_type.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                            </div>
                                            <div class="col-md-3 field-wrapper field-fee_head">
                                                {{ item_form.fee_head.label_tag }}
                                                {{ item_form.fee_head }}
                                                {% for error in item_form.fee_head.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                            </div>
                                            <div class="col-md-3 field-wrapper field-concession_type" style="display:none;">
                                                {{ item_form.concession_type.label_tag }}
                                                {{ item_form.concession_type }}
                                                {% for error in item_form.concession_type.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                            </div>
                                            <div class="col-md-3 field-wrapper field-description">
                                                {{ item_form.description.label_tag }}
                                                {{ item_form.description }}
                                                {% for error in item_form.description.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                            </div>
                                            <div class="col-md-2 field-wrapper field-quantity">
                                                {{ item_form.quantity.label_tag }}
                                                {{ item_form.quantity }}
                                                {% for error in item_form.quantity.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                            </div>
                                            <div class="col-md-2 field-wrapper field-unit_price">
                                                {{ item_form.unit_price.label_tag }}
                                                {{ item_form.unit_price }}
                                                {% for error in item_form.unit_price.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                            </div>
                                        </div>
                                        {% if item_formset.can_delete and item_form.instance.pk and not object or object.is_editable %}
                                            <div class="col-12 text-end mt-2">
                                                <div class="form-check form-check-inline">
                                                    {{ item_form.DELETE }}
                                                    <label for="{{ item_form.DELETE.id_for_label }}" class="form-check-label text-danger">
                                                        {{ item_form.DELETE.label|default:"Mark for Deletion" }}
                                                    </label>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>

                        {# Hidden template for new formset items (empty_form) #}
                        <div id="empty-invoice-item-form-template" style="display: none;">
                            <div class="formset-item-container" id="item-__prefix__">
                                <div class="formset-item-header">
                                    <h6>Item #<span class="item-number">__prefix_display__</span></h6>
                                    <button type="button" class="btn-close remove-invoice-item-form" aria-label="Remove item" title="Remove this item"></button>
                                </div>
                                <div class="formset-item-body">
                                    {% for hidden_field in item_formset.empty_form.hidden_fields %}{{ hidden_field }}{% endfor %}
                                    <div class="row g-3">
                                        <div class="col-md-3 field-wrapper field-line_type">
                                            {{ item_formset.empty_form.line_type.label_tag }}
                                            {{ item_formset.empty_form.line_type }}
                                            {% comment %} Errors won't typically be on the empty form template, but doesn't hurt {% endcomment %}
                                            {% for error in item_formset.empty_form.line_type.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                        </div>
                                        <div class="col-md-3 field-wrapper field-fee_head">
                                            {{ item_formset.empty_form.fee_head.label_tag }}
                                            {{ item_formset.empty_form.fee_head }}
                                            {% for error in item_formset.empty_form.fee_head.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                        </div>
                                        <div class="col-md-3 field-wrapper field-concession_type" style="display:none;">
                                            {{ item_formset.empty_form.concession_type.label_tag }}
                                            {{ item_formset.empty_form.concession_type }}
                                            {% for error in item_formset.empty_form.concession_type.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                        </div>
                                        <div class="col-md-3 field-wrapper field-description">
                                            {{ item_formset.empty_form.description.label_tag }}
                                            {{ item_formset.empty_form.description }}
                                            {% for error in item_formset.empty_form.description.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                        </div>
                                        <div class="col-md-2 field-wrapper field-quantity">
                                            {{ item_formset.empty_form.quantity.label_tag }}
                                            {{ item_formset.empty_form.quantity }}
                                            {% for error in item_formset.empty_form.quantity.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                        </div>
                                        <div class="col-md-2 field-wrapper field-unit_price">
                                            {{ item_formset.empty_form.unit_price.label_tag }}
                                            {{ item_formset.empty_form.unit_price }}
                                            {% for error in item_formset.empty_form.unit_price.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <div class="d-flex justify-content-end gap-2">
                            {% if object and object.pk %}
                                <a href="{{ object.get_absolute_url }}" class="btn btn-outline-secondary"><i class="bi bi-x-circle me-1"></i>Cancel</a>
                            {% else %}
                                <a href="{% url 'fees:invoice_list' %}" class="btn btn-outline-secondary"><i class="bi bi-x-circle me-1"></i>Cancel</a>
                            {% endif %}
                            {% if not object or object.is_editable %}
                            <button type="submit" class="btn btn-success px-4">
                                <i class="bi bi-check-circle-fill me-1"></i>
                                {% if object %}Update{% else %}Save{% endif %} Invoice
                            </button>
                            {% endif %}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}

{% block page_specific_js %}
<script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js"></script> {# Updated jQuery to latest stable #}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const formsetContainer = document.getElementById('invoice-items-formset-container');
    const addButton = document.getElementById('add-invoice-item-form');
    const emptyFormTemplateHTML = document.getElementById('empty-invoice-item-form-template').innerHTML;
    const totalFormsInput = document.querySelector('input[name$="-TOTAL_FORMS"]'); // e.g., details-TOTAL_FORMS
    const initialFormsInput = document.querySelector('input[name$="-INITIAL_FORMS"]'); // e.g., details-INITIAL_FORMS
    
    let initialFormsCount = initialFormsInput ? parseInt(initialFormsInput.value) : 0;
    let currentFormCount = totalFormsInput ? parseInt(totalFormsInput.value) : initialFormsCount;

    // This class should wrap each field's label, input, help_text, and error display.
    // Use the actual Bootstrap column classes that wrap the form fields
    const formFieldWrapperClass = '[class*="col-"]';

    function toggleInvoiceLineFields(formRow) {
        if (!formRow) return;
        const lineTypeSelector = formRow.querySelector('select[name$="-line_type"]');

        const getColumnWrapper = (fieldNameSuffix) => {
            const fieldElement = formRow.querySelector(`[name$="-${fieldNameSuffix}"]`);
            // Assuming the direct parent with class 'field-wrapper' is the one to hide/show.
            return fieldElement ? fieldElement.closest(formFieldWrapperClass) : null;
        };

        const feeHeadWrapper = getColumnWrapper('fee_head');
        const concessionTypeWrapper = getColumnWrapper('concession_type');

        if (!lineTypeSelector) return;

        const selectedType = lineTypeSelector.value;

        const setupField = (wrapper, show, required = false) => {
            if (wrapper) {
                wrapper.style.display = show ? '' : 'none';
                const inputElement = wrapper.querySelector('input, select, textarea');
                if (inputElement) {
                    inputElement.required = required;
                    if (!show) { // Optionally clear value if hidden
                        if (inputElement.tagName === 'SELECT') inputElement.value = '';
                        // else inputElement.value = ''; // For inputs/textareas if needed
                    }
                }
            }
        };
        
        if (selectedType === 'FEE_ITEM') {
            setupField(feeHeadWrapper, true, true); // Fee head is visible and required
            setupField(concessionTypeWrapper, false, false); // Concession type is hidden and not required
        } else if (selectedType === 'CONCESSION_ITEM') {
            setupField(feeHeadWrapper, false, false); // Fee head is hidden and not required
            setupField(concessionTypeWrapper, true, true); // Concession type is visible and required
        } else { // Default state for new/empty rows (which will be 'FEE_ITEM' due to JS default)
            setupField(feeHeadWrapper, true, true);
            setupField(concessionTypeWrapper, false, false);
        }
    }

    function initializeFormRowEventListeners(formRow) {
        if (!formRow) return;
        const lineTypeSelector = formRow.querySelector('select[name$="-line_type"]');
        if (lineTypeSelector) {
            lineTypeSelector.addEventListener('change', function() {
                toggleInvoiceLineFields(formRow);
            });
        }
        toggleInvoiceLineFields(formRow); // Call once to set initial state
    }

    function updateAllItemNumbersAndRemoveButtons() {
        const items = formsetContainer.querySelectorAll('.formset-item-container');
        items.forEach((item, index) => {
            const numberSpan = item.querySelector('.item-number');
            if (numberSpan) {
                numberSpan.textContent = index + 1;
            }
            const removeButton = item.querySelector('.remove-invoice-item-form');
            if (removeButton) {
                // Show remove 'X' button only for forms that are NOT initial forms,
                // OR if they are initial forms but don't have a PK (i.e., truly empty initial extra forms)
                const isInitialPersistedForm = index < initialFormsCount && item.querySelector('input[name$="-id"][value]') ;
                if (isInitialPersistedForm) {
                     removeButton.style.display = 'none'; // Hide for initial forms that have data
                } else {
                    removeButton.style.display = 'block';
                }
            }
        });
    }
    
    function initializeSelect2ForElement(elementContext) {
        if (!elementContext || typeof $ === 'undefined' || !$.fn.select2) return;

        // Initialize Select2 for all select fields with select2-field class or specific selectors
        $(elementContext).find('select.select2-field, select[data-control="select2"], select.fee-head-selector, select.applies-to-selector, select.line-type-selector').each(function() {
            if (!$(this).data('select2')) { // Initialize only if not already initialized
                $(this).select2({
                    theme: "bootstrap-5",
                    width: $(this).data('width') ? $(this).data('width') : $(this).hasClass('w-100') ? '100%' : 'style',
                    placeholder: $(this).data('placeholder') || "Select an option",
                    allowClear: $(this).find('option[value=""]').length > 0, // Allow clear if there's an empty option
                    // dropdownParent: $(this).closest('.modal').length ? $(this).closest('.modal') : $(document.body) // If inside a modal
                });
            }
        });

        // Also initialize main form fields specifically (for invoice form)
        $('#{{ form.student.id_for_label }}, #{{ form.academic_year.id_for_label }}, #{{ form.term.id_for_label }}').each(function() {
            if (!$(this).data('select2')) {
                $(this).select2({
                    theme: "bootstrap-5",
                    width: '100%',
                    placeholder: "Select an option",
                    allowClear: true
                });
            }
        });
    }

    if (addButton) {
        addButton.addEventListener('click', function() {
            if (!totalFormsInput) return;
            const prefix = totalFormsInput.name.split('-')[0]; // e.g., 'details'
            let formIdx = parseInt(totalFormsInput.value);

            const newFormHtml = emptyFormTemplateHTML.replace(/__prefix__/g, formIdx).replace(/__prefix_display__/g, formIdx + 1);
            
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = newFormHtml;
            const newFormElement = tempDiv.firstElementChild;

            formsetContainer.appendChild(newFormElement);
            
            const newLineTypeSelect = newFormElement.querySelector('select[name$="-line_type"]');
            if (newLineTypeSelect) {
                newLineTypeSelect.value = 'FEE_ITEM'; // Explicitly set to FEE_ITEM for new rows
            }

            initializeSelect2ForElement(newFormElement);
            initializeFormRowEventListeners(newFormElement); 

            totalFormsInput.value = formIdx + 1;
            // currentFormCount = parseInt(totalFormsInput.value); // Update if you use currentFormCount elsewhere
            updateAllItemNumbersAndRemoveButtons();
        });
    }

    formsetContainer.addEventListener('click', function(event) {
        if (event.target.classList.contains('remove-invoice-item-form')) {
            event.preventDefault();
            const itemToRemove = event.target.closest('.formset-item-container');
            const deleteCheckbox = itemToRemove.querySelector('input[type="checkbox"][name$="-DELETE"]');

            if (deleteCheckbox && deleteCheckbox.name.indexOf('__prefix__') === -1) { 
                deleteCheckbox.checked = true;
                itemToRemove.style.display = 'none';
            } else {
                itemToRemove.remove();
                // Note: TOTAL_FORMS should ideally be decremented and subsequent forms re-indexed
                // if removing a non-persisted form. Many libraries handle this.
                // For manual handling, it's complex. Simplest is to let server ignore empty forms.
            }
            updateAllItemNumbersAndRemoveButtons();
        }
    });

    // Initialize for existing forms on page load
    document.querySelectorAll('#invoice-items-formset-container .formset-item-container').forEach(formRow => {
        initializeSelect2ForElement(formRow);
        initializeFormRowEventListeners(formRow);
    });
    initializeSelect2ForElement(document.getElementById('invoiceForm')); // For main form selects

    updateAllItemNumbersAndRemoveButtons(); // Initial update
});
</script>
{% endblock %}




















{% comment %} {# D:\school_fees_saas_v2\templates\fees\invoice_form.html #}
{% extends "tenant_base.html" %}

{% load static humanize %} {# Load humanize if you plan to use its filters #}

{% block title %}{{ view_title|default:"Invoice Form" }} - {{ request.tenant.name }}{% endblock %}

{% block extra_tenant_css %}
    {# Select2 CSS - Consider hosting locally if CDN is an issue #}
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" />
    <style>
        .formset-item-container {
            border: 1px solid #dee2e6; /* Bootstrap default border color */
            border-radius: 0.375rem; /* Bootstrap default border-radius */
            margin-bottom: 1.5rem;
            background-color: #f8f9fa; /* Bootstrap light background */
            box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,.075);
        }
        .formset-item-header {
            background-color: #e9ecef; /* Bootstrap secondary background or lighter */
            padding: 0.75rem 1.25rem;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .formset-item-header h6 {
            margin-bottom: 0;
            font-weight: 600;
            color: #212529; /* Bootstrap default text color */
        }
        .formset-item-body {
            padding: 1.25rem;
        }
        /* Select2 Bootstrap 5 Theme should handle most styling, but these are good overrides if needed */
        .select2-container .select2-selection--single {
            height: calc(1.5em + 0.75rem + 2px) !important;
        }
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: calc(1.5em + 0.75rem) !important;
            padding-left: 0.75rem !important;
        }
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: calc(1.5em + 0.75rem) !important;
        }
        /* Error list styling for better visibility */
        .errorlist {
            color: var(--bs-danger); /* Use Bootstrap danger color variable */
            font-size: 0.875em;
            list-style: none;
            padding-left: 0;
            margin-top: .25rem;
        }
    </style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    {% include "partials/_breadcrumb.html" %}

    <div class="row justify-content-center">
        <div class="col-xl-10 col-lg-12"> {# Or col-md-10 col-lg-8 for a narrower form on larger screens #}
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h4 class="mb-0">{{ view_title|default:"Invoice" }}</h4>
                </div>
                <div class="card-body p-lg-4 p-3">
                    {% include "partials/_messages.html" %}

                    <form method="post" novalidate id="invoiceForm" enctype="multipart/form-data"> {# Add enctype if any form fields are FileField #}
                        {% csrf_token %}
                        {{ form.media }} {# For widgets like Select2, DatePicker if form defines media #}

                        <h5 class="mb-3 text-primary border-bottom pb-2"><i class="bi bi-file-earmark-text me-2"></i>Invoice Details</h5>
                        <div class="row g-3 mb-4">
                            {# Loop through main form fields #}
                            {% for field in form %}
                                {% if field.is_hidden %}
                                    {{ field }}
                                {% else %}
                                    <div class="col-md-{% if field.name == 'notes' or field.name == 'internal_notes' %}12{% elif field.name == 'student' or field.name == 'academic_year' %}6{% else %}4{% endif %}">
                                        <label for="{{ field.id_for_label }}" class="form-label">{{ field.label_tag }}</label> {# Use label_tag for full label with required asterisk #}
                                        {{ field }}
                                        {% if field.help_text %}<div class="form-text text-muted small">{{ field.help_text|safe }}</div>{% endif %}
                                        {% for error in field.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                    </div>
                                {% endif %}
                            {% endfor %}
                            {% if form.non_field_errors %}
                                <div class="col-12 mt-3">
                                    {% for error in form.non_field_errors %}<div class="alert alert-danger py-2">{{ error }}</div>{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        {% if object and not object.is_editable %}
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>This invoice is not in a draft state. Main details and items may be locked or restricted.
                            </div>
                        {% endif %}

                        <hr class="my-4">

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0 text-primary"><i class="bi bi-list-ol me-2"></i>Invoice Items</h5>
                            {% if not object or object.is_editable %} {# Show add button for new or editable draft invoices #}
                            <button type="button" id="add-invoice-item-form" class="btn btn-sm btn-outline-success">
                                <i class="bi bi-plus-lg me-1"></i> Add Item
                            </button>
                            {% endif %}
                        </div>

                        {{ item_formset.management_form }}
                        {% if item_formset.non_form_errors %}
                            <div class="alert alert-danger">
                                {% for error in item_formset.non_form_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <div id="invoice-items-formset-container">
                            {% for item_form in item_formset %}
                                <div class="formset-item-container" id="{{ item_form.prefix }}-container"> {# Unique ID for container #}
                                    <div class="formset-item-header">
                                        <h6>Item #<span class="item-number">{{ forloop.counter }}</span></h6>
                                        {# Show remove button for newly added (non-initial) forms or if can_delete #}
                                        
                                        {% if item_formset.can_delete %}
                                            {# Condition: Show remove button if it's a new invoice (no object yet) OR if it's an existing invoice AND it's editable. #}
                                            {% if not object or object.is_editable %}
                                                <button type="button" class="btn-close remove-invoice-item-form" aria-label="Remove item" title="Remove this item"
                                                        style="display: {% if not item_form.instance.pk and forloop.counter0 >= item_formset.initial_form_count %}block{% else %}none{% endif %};"></button>
                                            {% endif %}
                                        {% endif %}
                                                                                
                                        {% comment %} {% if item_formset.can_delete and (not object or object.is_editable) %}
                                            {# Hide remove button if it's an initial form that shouldn't be removable without DELETE flag #}
                                            <button type="button" class="btn-close remove-invoice-item-form" aria-label="Remove item" title="Remove this item"
                                                    style="display: {% if forloop.counter0 < item_formset.initial_form_count and not item_form.instance.pk %}none{% elif item_form.instance.pk and not item_formset.can_delete_extra %}none{% else %}block{% endif %};"></button>
                                        {% endif %}
                                    </div>
                                    <div class="formset-item-body">
                                        {% if item_form.non_field_errors %}
                                            <div class="alert alert-danger py-2">
                                                {% for error in item_form.non_field_errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                        <div class="row g-3">
                                            {% for field in item_form %}
                                                {% if field.is_hidden %}
                                                    {{ field }}
                                                {% else %}
                                                    {# Dynamic column sizing for item form fields #}
                                                    <div class="col-md-{% if field.name == 'description' %}12{% elif field.name == 'fee_head' or field.name == 'concession_type' %}6{% elif field.name == 'quantity' or field.name == 'unit_price' %}3{% elif field.name == 'line_type' %}6{% else %}6{% endif %}">
                                                        <label for="{{ field.id_for_label }}" class="form-label">{{ field.label_tag }}</label>
                                                        {{ field }}
                                                        {% if field.help_text %}<div class="form-text text-muted small">{{ field.help_text|safe }}</div>{% endif %}
                                                        {% for error in field.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                                    </div>
                                                {% endif %}
                                            {% endfor %}


                                            {% if item_formset.can_delete and item_form.instance.pk %}
                                                {% if not object or object.is_editable %}
                                                <div class="col-12 text-end mt-2">
                                                    <div class="form-check form-check-inline">
                                                        {{ item_form.DELETE }}
                                                        <label for="{{ item_form.DELETE.id_for_label }}" class="form-check-label text-danger">
                                                            {{ item_form.DELETE.label|default:"Mark for Deletion" }}
                                                        </label>
                                                    </div>
                                                </div>
                                                {% endif %}
                                            {% endif %}
                                            {% comment %} {% if item_formset.can_delete and item_form.instance.pk and (not object or object.is_editable) %}
                                            {# Show DELETE checkbox for existing items only #}
                                            <div class="col-12 text-end mt-2">
                                                <div class="form-check form-check-inline">
                                                    {{ item_form.DELETE }}
                                                    <label for="{{ item_form.DELETE.id_for_label }}" class="form-check-label text-danger">{{ item_form.DELETE.label }}</label>
                                                </div>
                                            </div>
                                            {% endif %} 
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>

                        {# Hidden template for new formset items #}
                        <div id="empty-invoice-item-form-template" style="display: none;">
                            {# This content should mirror one item_form structure above, with __prefix__ #}
                            <div class="formset-item-container" id="item-__prefix__">
                                <div class="formset-item-header">
                                    <h6>Item #<span class="item-number">__prefix_display__</span></h6>
                                    <button type="button" class="btn-close remove-invoice-item-form" aria-label="Remove item" title="Remove this item"></button>
                                </div>
                                <div class="formset-item-body">
                                    <div class="row g-3">
                                        {% for field in item_formset.empty_form %} {# Use empty_form for correct field rendering #}
                                            {% if field.is_hidden %}
                                                {{ field }}
                                            {% else %}
                                                <div class="col-md-{% if field.name == 'description' %}12{% elif field.name == 'fee_head' or field.name == 'concession_type' %}6{% elif field.name == 'quantity' or field.name == 'unit_price' %}3{% elif field.name == 'line_type' %}6{% else %}6{% endif %}">
                                                    <label for="{{ field.id_for_label }}" class="form-label">{{ field.label_tag }}</label>
                                                    {{ field }} {# This renders the input with __prefix__ #}
                                                </div>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <div class="d-flex justify-content-end gap-2">
                            {# --- CORRECTED CANCEL LINK --- #}
                            {% if object and object.pk %} {# If object exists (UpdateView) #}
                                <a href="{{ object.get_absolute_url }}" class="btn btn-outline-secondary">
                                    <i class="bi bi-x-circle me-1"></i>Cancel
                                </a>
                            {% else %} {# If object doesn't exist (CreateView) #}
                                <a href="{% url 'fees:invoice_list' %}" class="btn btn-outline-secondary">
                                    <i class="bi bi-x-circle me-1"></i>Cancel
                                </a>
                            {% endif %}
                            {# --- END CORRECTED CANCEL LINK --- #}
                        
                            {% if not object or object.is_editable %}
                            <button type="submit" class="btn btn-success px-4">
                                <i class="bi bi-check-circle-fill me-1"></i>
                                {% if object %}Update{% else %}Save{% endif %} Invoice
                            </button>
                            {% endif %}
                        </div>
                        {% comment %} <div class="d-flex justify-content-end gap-2">
                            <a href="{{ object.get_absolute_url|default_if_none:(url 'fees:invoice_list') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-1"></i>Cancel
                            </a>
                            {% if not object or object.is_editable %}
                            <button type="submit" class="btn btn-success px-4">
                                <i class="bi bi-check-circle-fill me-1"></i>
                                {% if object %}Update{% else %}Save{% endif %} Invoice
                            </button>
                            {% endif %}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}


{% block page_specific_js %}
<script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script> {# jQuery is often needed for Select2 #}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const formsetContainer = document.getElementById('invoice-items-formset-container'); // Your formset container ID
    const addButton = document.getElementById('add-invoice-item-form'); // Your "add" button ID
    const emptyFormTemplateHTML = document.getElementById('empty-invoice-item-form-template').innerHTML; // Your empty form template
    const totalFormsInput = document.querySelector('input[name$="-TOTAL_FORMS"]'); // e.g., items-TOTAL_FORMS
    const initialFormsCount = parseInt(document.querySelector('input[name$="-INITIAL_FORMS"]').value);
    let currentFormCount = parseInt(totalFormsInput.value);

    // --- START: NEW FUNCTIONS FOR TOGGLING FIELDS ---
    // Use the actual Bootstrap column classes that wrap the form fields
    const formFieldWrapperClass = '[class*="col-"]';

    function toggleInvoiceLineFields(formRow) {
        if (!formRow) return; // Guard clause for formRow
        const lineTypeSelector = formRow.querySelector('select[name$="-line_type"]'); // More specific selector

        // Find wrappers based on unique parts of field names or CSS classes on the selects themselves
        const feeHeadWrapper = formRow.querySelector('select[name$="-fee_head"]') ? 
                            formRow.querySelector('select[name$="-fee_head"]').closest(formFieldWrapperClass) : null;
        const concessionTypeWrapper = formRow.querySelector('select[name$="-concession_type"]') ? 
                                    formRow.querySelector('select[name$="-concession_type"]').closest(formFieldWrapperClass) : null;
        // Note: applies_to_line field doesn't exist in our form, so we don't need this wrapper

        if (!lineTypeSelector) {
            // console.warn("Line type selector not found in formRow:", formRow);
            return; 
        }

        const selectedType = lineTypeSelector.value;

        const setupField = (wrapper, show, clearSelect = true) => {
            if (wrapper) {
                wrapper.style.display = show ? '' : 'none';
                if (!show && clearSelect) {
                    const select = wrapper.querySelector('select');
                    if (select) select.value = ''; // Clear selection
                }
            } else {
                // console.warn("A field wrapper was not found during setupField. Check selectors and formFieldWrapperClass.");
            }
        };

        if (selectedType === 'FEE_ITEM') {
            setupField(feeHeadWrapper, true);
            setupField(concessionTypeWrapper, false);
        } else if (selectedType === 'CONCESSION_ITEM') {
            setupField(feeHeadWrapper, false);
            setupField(concessionTypeWrapper, true);
        } else { // Default or empty line_type (e.g., if placeholder is selected)
            setupField(feeHeadWrapper, true); // Or false, depending on desired default for new rows
            setupField(concessionTypeWrapper, false);
        }
    }

    function initializeFormRowEventListeners(formRow) {
        if (!formRow) return;
        toggleInvoiceLineFields(formRow); // Set initial state based on current line_type
        const lineTypeSelector = formRow.querySelector('select[name$="-line_type"]');
        if (lineTypeSelector) {
            lineTypeSelector.addEventListener('change', function() {
                toggleInvoiceLineFields(formRow);
            });
        }
        // Add unit price validation
        const unitPriceInput = formRow.querySelector('input[name$="-unit_price"]');
        if (unitPriceInput) {
            unitPriceInput.addEventListener('input', function() {
                validateUnitPrice(formRow);
            });
        }
    }

    function validateUnitPrice(formRow) {
        const lineTypeSelect = formRow.querySelector('select[name$="-line_type"]');
        const unitPriceInput = formRow.querySelector('input[name$="-unit_price"]');

        if (!lineTypeSelect || !unitPriceInput) return;

        const lineType = lineTypeSelect.value;
        const value = parseFloat(unitPriceInput.value);

        // Remove existing validation classes
        unitPriceInput.classList.remove('is-invalid', 'is-valid');

        // Skip validation for empty values
        if (isNaN(value) || unitPriceInput.value === '') {
            return;
        }

        // Validate based on line type
        if (lineType === 'FEE_ITEM' && value < 0) {
            unitPriceInput.classList.add('is-invalid');
            unitPriceInput.title = 'Fee items must have positive amounts';
        } else if (lineType === 'CONCESSION_ITEM' && value >= 0) {
            unitPriceInput.classList.add('is-invalid');
            unitPriceInput.title = 'Concessions must have negative amounts (e.g., -50.00)';
        } else {
            unitPriceInput.classList.add('is-valid');
            unitPriceInput.title = '';
        }
    }
    // --- END: NEW FUNCTIONS FOR TOGGLING FIELDS ---


    function updateAllItemNumbersAndRemoveButtons() {
        const items = formsetContainer.querySelectorAll('.formset-item-container'); // Assuming each form row has this class
        items.forEach((item, index) => {
            const numberSpan = item.querySelector('.item-number');
            if (numberSpan) {
                numberSpan.textContent = index + 1;
            }

            const removeButton = item.querySelector('.remove-invoice-item-form');
            if (removeButton) {
                const isInitialForm = index < initialFormsCount;
                if (isInitialForm) {
                    removeButton.style.display = 'none';
                } else {
                    removeButton.style.display = 'block';
                }
            }
        });
    }

    function initializeSelect2ForElement(elementContext) {
        if (!elementContext || typeof $ === 'undefined' || !$.fn.select2) return; // Guard against missing jQuery/Select2
        $(elementContext).find('select[data-control="select2"]').each(function() {
            if (!$(this).data('select2')) {
                $(this).select2({
                    theme: "bootstrap-5",
                    width: $(this).data('width') ? $(this).data('width') : $(this).hasClass('w-100') ? '100%' : 'style',
                    placeholder: $(this).data('placeholder') || "Select an option",
                    allowClear: true
                });
            }
        });
    }

    if (addButton) {
        addButton.addEventListener('click', function() {
            const prefix = totalFormsInput.name.split('-')[0];
            const newFormHtml = emptyFormTemplateHTML.replace(/__prefix__/g, currentFormCount);
            
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = newFormHtml;
            const newFormElement = tempDiv.firstElementChild; // This should be your '.formset-item-container'

            formsetContainer.appendChild(newFormElement);
            
            // --- CALL INITIALIZERS FOR THE NEW FORM ---
            initializeSelect2ForElement(newFormElement);
            initializeFormRowEventListeners(newFormElement); // <<< --- ADDED THIS CALL

            totalFormsInput.value = currentFormCount + 1;
            currentFormCount++;

            updateAllItemNumbersAndRemoveButtons();
        });
    }

    formsetContainer.addEventListener('click', function(event) {
        if (event.target.classList.contains('remove-invoice-item-form')) {
            event.preventDefault();
            const itemToRemove = event.target.closest('.formset-item-container');
            const deleteCheckbox = itemToRemove.querySelector('input[type="checkbox"][name$="-DELETE"]');

            if (deleteCheckbox) {
                deleteCheckbox.checked = true;
                itemToRemove.style.display = 'none';
            } else {
                itemToRemove.remove();
            }
            // updateAllItemNumbersAndRemoveButtons(); // Call after modifying the DOM
        }
    });

    // Initialize for existing forms on page load
    formsetContainer.querySelectorAll('.formset-item-container').forEach(formRow => { // Assuming '.formset-item-container' is your row class
        initializeSelect2ForElement(formRow);
        initializeFormRowEventListeners(formRow); // <<< --- ADDED THIS CALL
    });
    
    updateAllItemNumbersAndRemoveButtons(); // Initial update

});
</script>
{% endblock %}
 {% endcomment %}
