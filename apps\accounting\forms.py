# D:\school_fees_saas_v2\apps\accounting\forms.py
from django import forms
from django.forms import inlineformset_factory, BaseInlineFormSet
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from decimal import Decimal

from .models import AccountType, Account, JournalEntry, JournalEntryItem, JournalLine


# --- AccountType Form ---
class AccountTypeForm(forms.ModelForm):
    name = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'form-control form-control-sm'}), 
        label=_("Account Type Name")
    )
    # Add the 'code' field explicitly if you want to customize its widget or label,
    # otherwise ModelForm will create it from the model if it's in Meta.fields.
    code = forms.CharField( # Assuming 'code' is CharField in model
        required=False, # Based on your model (null=True, blank=True)
        widget=forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
        label=_("Code")
    )
    description = forms.CharField(
        required=False, 
        widget=forms.Textarea(attrs={'class': 'form-control form-control-sm', 'rows': 2}),
        label=_("Description")
    )
    classification = forms.ChoiceField(
        choices=AccountType.ClassificationChoices.choices, # This should be correct if ClassificationChoices exists in model
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        label=_("Classification")
    )
    statement_section = forms.ChoiceField(
        choices=AccountType.StatementSectionChoices.choices, # This should be correct if StatementSectionChoices exists
        required=False, # Based on your model (blank=True, null=True)
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        label=_("Statement Section")
    )
    normal_balance = forms.ChoiceField(
        choices=AccountType.NormalBalanceChoices.choices, # <<< CORRECTED HERE
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        label=_("Normal Balance")
        # This field is required in the model (no blank=True/null=True in my recommendation)
        # so required=True is implicit here, which is good.
    )

    class Meta:
        model = AccountType
        # Ensure all fields you want on the form are listed here AND defined above if customized,
        # OR let ModelForm create them by default if no customization is needed.
        fields = ['name', 'code', 'description', 'classification', 'statement_section', 'normal_balance']
        
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
            'code': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
            'description': forms.Textarea(attrs={'class': 'form-control form-control-sm', 'rows': 2}),
            'classification': forms.Select(attrs={'class': 'form-select form-select-sm'}),
            'normal_balance': forms.Select(attrs={'class': 'form-select form-select-sm'}),
            'statement_section': forms.Select(attrs={'class': 'form-select form-select-sm'}),
        }


    # The __init__ method for adding 'form-control-sm' might still be useful
    # if you want to ensure it for any field, or you can rely on widgets in Meta.
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Optional: Loop to ensure all relevant fields have form-control-sm
        for field_name, field in self.fields.items():
            current_class = field.widget.attrs.get('class', '')
            is_select = isinstance(field.widget, forms.Select)
            target_class_part = 'form-select-sm' if is_select else 'form-control-sm'
            base_class_part = 'form-select' if is_select else 'form-control'

            if base_class_part not in current_class: # Add base bootstrap class
                field.widget.attrs['class'] = f'{current_class} {base_class_part}'.strip()
            if target_class_part not in current_class: # Add sm class
                field.widget.attrs['class'] = f'{field.widget.attrs.get("class", "")} {target_class_part}'.strip()
                
                

# --- Account Form (Chart of Account Form) ---
class AccountForm(forms.ModelForm):
    class Meta:
        model = Account
        fields = [
            'tenant', 'name', 'code', 'account_type',  # 'account_code',
            'parent_account', 
            'description', 'is_active', 
            'is_control_account', 'can_be_used_in_je' # Added can_be_used_in_je
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
            'account_code': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
            'account_type': forms.Select(attrs={'class': 'form-select form-select-sm'}),
            'parent_account': forms.Select(attrs={'class': 'form-select form-select-sm', 'data-control': 'select2'}), # Assuming select2 integration
            'description': forms.Textarea(attrs={'class': 'form-control form-control-sm', 'rows': 3}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_control_account': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'can_be_used_in_je': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'name': _("Account Name"),
            'account_code': _("Account Code"),
            'account_type': _("Type"),
            'parent_account': _("Parent Account"),
            'is_active': _("Active?"),
            'is_control_account': _("Control Account?"),
            'can_be_used_in_je': _("Allow Direct Journal Entries?"),
        }
        help_texts = {
            'account_code': _("Optional unique code, e.g., 1010."),
            'parent_account': _("Leave blank if this is a top-level account."),
            'is_control_account': _("Check if this account summarizes sub-accounts."),
            'can_be_used_in_je': _("Uncheck for control accounts that are only updated by sub-ledgers (e.g., main A/R).")
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.fields['account_type'].queryset = AccountType.objects.all().order_by('classification', 'name')
        self.fields['account_type'].empty_label = _("Select Account Type")

        parent_queryset = Account.objects.all()
        if self.instance and self.instance.pk:
            parent_queryset = parent_queryset.exclude(pk=self.instance.pk)
        
        self.fields['parent_account'].queryset = parent_queryset.order_by('code', 'name') # Changed 'code' to 'account_code'
        self.fields['parent_account'].label_from_instance = lambda obj: f"{obj.code if obj.code else 'N/C'} - {obj.name} ({obj.account_type.name})"
        self.fields['parent_account'].required = False 
        self.fields['parent_account'].empty_label = _("None (Top-Level Account)")

    def clean_parent_account(self):
        parent = self.cleaned_data.get('parent_account')
        if self.instance and self.instance.pk and parent and parent.pk == self.instance.pk:
            raise forms.ValidationError(_("An account cannot be its own parent."))
        return parent

    def clean(self):
        cleaned_data = super().clean()
        parent_account = cleaned_data.get('parent_account')
        account_type = cleaned_data.get('account_type')
        is_control_account = cleaned_data.get('is_control_account')
        can_be_used_in_je = cleaned_data.get('can_be_used_in_je')

        if parent_account and account_type:
            if parent_account.account_type and account_type: # Both types must be selected
                if parent_account.account_type.classification != account_type.classification:
                    msg = _("Child account classification ('%(child_class)s') must align with parent's ('%(parent_class)s').") % {
                        'child_class': account_type.get_classification_display(),
                        'parent_class': parent_account.account_type.get_classification_display()
                    }
                    self.add_error('account_type', msg)
        
        # Business rule: Control accounts should generally not allow direct JEs
        if is_control_account and can_be_used_in_je:
            self.add_error('can_be_used_in_je', _("Control accounts typically should not allow direct journal entries. Uncheck this or 'Control Account?'."))

        # Business rule: If an account has children, it should be a control account
        # This requires checking the instance in the database, might be better in model's full_clean or save
        # if self.instance and self.instance.pk and self.instance.children.exists() and not is_control_account:
        #     self.add_error('is_control_account', _("This account has sub-accounts and should be marked as a Control Account."))

        return cleaned_data


# --- JournalEntryItem Form (for each line in the JE) ---
class JournalEntryItemForm(forms.ModelForm):
    account = forms.ModelChoiceField(
        queryset=Account.objects.filter(is_active=True, can_be_used_in_je=True).order_by('account_type__name', 'name'),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm jei-account'})
    )
    debit = forms.DecimalField( # Field name matches your JournalEntryItem.debit
        max_digits=12, decimal_places=2, initial=Decimal('0.00'), required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control form-control-sm jei-debit', 'step': '0.01', 'placeholder': '0.00'})
    )
    credit = forms.DecimalField( # Field name matches your JournalEntryItem.credit
        max_digits=12, decimal_places=2, initial=Decimal('0.00'), required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control form-control-sm jei-credit', 'step': '0.01', 'placeholder': '0.00'})
    )
    description = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control form-control-sm jei-description', 'placeholder': _('Line description (optional)')})
    )

    class Meta:
        model = JournalEntryItem
        fields = ['account', 'debit', 'credit', 'description']

    def clean(self):
        cleaned_data = super().clean()
        debit_val = cleaned_data.get('debit') or Decimal('0.00')
        credit_val = cleaned_data.get('credit') or Decimal('0.00')
        account = cleaned_data.get('account')
        description_val = cleaned_data.get('description')

        if account or debit_val > 0 or credit_val > 0 or description_val:
            if not account:
                self.add_error('account', _("Account is required if other line data is present."))
            if debit_val < Decimal('0.00'):
                self.add_error('debit', _("Debit amount cannot be negative."))
            if credit_val < Decimal('0.00'):
                self.add_error('credit', _("Credit amount cannot be negative."))
            if debit_val > Decimal('0.00') and credit_val > Decimal('0.00'):
                self.add_error(None, ValidationError(_("Enter either a debit or a credit amount per line, not both."), code='debit_credit_exclusive'))
            if account and debit_val == Decimal('0.00') and credit_val == Decimal('0.00'):
                if not description_val:
                    self.add_error(None, ValidationError(_("Enter a debit or credit amount for the selected account, or a line description."), code='amount_or_desc_required_for_account'))
        return cleaned_data


# --- Base FormSet for JournalEntryItems ---
class BaseJournalEntryItemFormSet(BaseInlineFormSet):
    def clean(self):
        super().clean()
        if any(self.errors):
            return

        total_debits = Decimal('0.00')
        total_credits = Decimal('0.00')
        actual_lines_count = 0

        for form in self.forms:
            if self.can_delete and self._should_delete_form(form):
                continue
            
            if not form.has_changed() and not form.instance.pk :
                if not (form.cleaned_data.get('account') or \
                        (form.cleaned_data.get('debit') and form.cleaned_data.get('debit') > 0) or \
                        (form.cleaned_data.get('credit') and form.cleaned_data.get('credit') > 0) or \
                        form.cleaned_data.get('description')):
                    continue

            if form.errors: return

            if not hasattr(form, 'cleaned_data'): continue

            debit = form.cleaned_data.get('debit') or Decimal('0.00') # Use 'debit'
            credit = form.cleaned_data.get('credit') or Decimal('0.00') # Use 'credit'
            
            if form.cleaned_data.get('account') or debit > 0 or credit > 0 or form.cleaned_data.get('description'):
                actual_lines_count += 1
            
            total_debits += debit
            total_credits += credit
        
        if self.validate_min and actual_lines_count < self.min_num:
            raise ValidationError(
                _("You must have at least %(min_num)d complete journal lines.") % {'min_num': self.min_num}
            )

        if actual_lines_count == 0:
            if not (self.initial_form_count() == 0 and not any(f.has_changed() or self._should_delete_form(f) for f in self.forms)):
                raise ValidationError(_("At least one journal line is required."))
        elif total_debits != total_credits:
            diff = total_debits - total_credits
            raise ValidationError(
                _("Journal entry is unbalanced. Total Debits: %(debits)s, Total Credits: %(credits)s. Difference: %(diff)s.") % {
                    'debits': total_debits, 'credits': total_credits, 'diff': diff
                }
            )



# --- Inline FormSet for JournalEntryItems ---
JournalEntryItemFormSet = inlineformset_factory(
    JournalEntry, 
    JournalEntryItem, 
    form=JournalEntryItemForm,
    formset=BaseJournalEntryItemFormSet,
    fields=['account', 'debit', 'credit', 'description'], # Matches JournalEntryItem fields
    extra=2, 
    min_num=2, 
    validate_min=False,
    can_delete=True
)



class JournalEntryForm(forms.ModelForm):
    date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        initial=timezone.now().date(),
        label=_("Entry Date")
    )
    # Assuming your model field is 'description'. If it's 'narration', change 'description' to 'narration' below.
    description = forms.CharField( 
        widget=forms.Textarea(attrs={'class': 'form-control form-control-sm', 'rows': 3, 'placeholder': _('e.g., To record year-end accruals for salaries')}),
        label=_("Overall Description / Narration")
    )
    reference_number = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control form-control-sm', 'placeholder': _('Optional reference, e.g., ADJ001')}),
        label=_("Reference Number")
    )

    class Meta:
        model = JournalEntry
        fields = ['date', 'description', 'reference_number'] # Ensure these field names match your JournalEntry model

    def __init__(self, *args, **kwargs):
        # Pop custom arguments BEFORE calling super().__init__
        # Store them on the instance if needed by other methods of this form
        self.user = kwargs.pop('user', None)
        self.tenant = kwargs.pop('tenant', None) # If you also pass tenant

        # Now, kwargs definitely does not contain 'user' or 'tenant'
        super().__init__(*args, **kwargs) 
        # Any other custom initialization after super can go here
        # For example, if you needed to modify field querysets based on self.tenant or self.user

    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if self.user: # self.user was set in __init__
            if not instance.pk: 
                instance.created_by = self.user
            if hasattr(instance, 'last_modified_by'): # Assuming this field exists
                instance.last_modified_by = self.user
        
        instance.entry_type = JournalEntry.EntryType.MANUAL 
        if not instance.pk: 
            instance.status = JournalEntry.EntryStatus.DRAFT
            
        # If JournalEntry model has a direct tenant FK and it's not auto-set:
        # if self.tenant and hasattr(instance, 'tenant') and not instance.tenant_id:
        #    instance.tenant = self.tenant
            
        if commit:
            instance.save()
            # If you have a many-to-many field that needs form.save_m2m()
            # and commit was True, call it here after instance.save()
            # For JournalEntry, this is not typical unless you add M2M fields.
        return instance
    
    
    
class JournalEntryLineForm(forms.ModelForm):
    account = forms.ModelChoiceField(
        queryset=Account.objects.none(), # Queryset will be set dynamically in the view/formset
        widget=forms.Select(attrs={'class': 'form-select form-select-sm jel-account account-select'}) # Added 'account-select' for JS if needed
    )
    debit_amount = forms.DecimalField(
        max_digits=12, decimal_places=2, initial=Decimal('0.00'), required=False, # required=False, validation in formset
        widget=forms.NumberInput(attrs={'class': 'form-control form-control-sm jel-debit text-end', 'step': '0.01', 'placeholder': '0.00'})
    )
    credit_amount = forms.DecimalField(
        max_digits=12, decimal_places=2, initial=Decimal('0.00'), required=False, # required=False, validation in formset
        widget=forms.NumberInput(attrs={'class': 'form-control form-control-sm jel-credit text-end', 'step': '0.01', 'placeholder': '0.00'})
    )
    description = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control form-control-sm jel-description', 'placeholder': 'Line description'})
    )

    class Meta:
        model = JournalLine
        fields = ['account', 'debit_amount', 'credit_amount', 'description']

    def __init__(self, *args, **kwargs):
        # Pop tenant before super().__init__ if you plan to use it here
        # For now, queryset is handled at formset or view level
        # tenant = kwargs.pop('tenant', None) 
        super().__init__(*args, **kwargs)
        # Dynamically set the queryset for the account field if a tenant is available
        # This is often better done when instantiating the formset in the view,
        # but can be done here if tenant context is reliably passed.
        # For now, assume it's set when formset is created.
        # if tenant:
        #    self.fields['account'].queryset = ChartOfAccount.objects.filter(tenant=tenant, is_active=True).order_by('account_type__name', 'name')
        # else:
        #    self.fields['account'].queryset = ChartOfAccount.objects.filter(is_active=True).order_by('account_type__name', 'name') # Or specific to tenant schema

        # To ensure fields are not marked as required by default HTML5 validation if they are optional
        self.fields['debit_amount'].required = False
        self.fields['credit_amount'].required = False
        self.fields['description'].required = False


    def clean(self):
        cleaned_data = super().clean()
        debit = cleaned_data.get('debit_amount') or Decimal('0.00') # Ensure Decimal
        credit = cleaned_data.get('credit_amount') or Decimal('0.00') # Ensure Decimal

        # This validation is per-line. The formset will validate overall balance and if any line exists.
        if debit > Decimal('0.00') and credit > Decimal('0.00'):
            # Adding error to a specific field is better for display
            self.add_error('debit_amount', ValidationError(_("Enter either a debit or a credit amount per line, not both."), code='debit_credit_conflict'))
            self.add_error('credit_amount', ValidationError(_("Enter either a debit or a credit amount per line, not both."), code='debit_credit_conflict'))
            # raise ValidationError(_("Enter either a debit or a credit amount per line, not both."), code='debit_credit_conflict')

        # If a line has an account, it should have some amount or a description (or both)
        # This logic can be refined based on how "empty" a line can be before it's ignored by the formset.
        account = cleaned_data.get('account')
        description = cleaned_data.get('description', '').strip()
        if account and debit == Decimal('0.00') and credit == Decimal('0.00') and not description:
            # This line is effectively empty if amounts are zero and no description.
            # Formset will handle if *all* lines are like this.
            pass

        if debit < Decimal('0.00'):
            self.add_error('debit_amount', ValidationError(_("Debit amount cannot be negative."), code='negative_debit'))
        if credit < Decimal('0.00'):
            self.add_error('credit_amount', ValidationError(_("Credit amount cannot be negative."), code='negative_credit'))
            
        return cleaned_data
    
    

class JournalLineForm(forms.ModelForm): # Ensure model name matches
    account = forms.ModelChoiceField(
        queryset=Account.objects.filter(is_active=True, can_be_used_in_je=True).order_by('account_type__name', 'name'),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm jel-account'})
    )
    debit_amount = forms.DecimalField( # Use debit_amount
        max_digits=12, decimal_places=2, initial=Decimal('0.00'), required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control form-control-sm jel-debit', 'step': '0.01', 'placeholder': '0.00'})
    )
    credit_amount = forms.DecimalField( # Use credit_amount
        max_digits=12, decimal_places=2, initial=Decimal('0.00'), required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control form-control-sm jel-credit', 'step': '0.01', 'placeholder': '0.00'})
    )
    description = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control form-control-sm jel-description', 'placeholder': 'Line description'})
    )

    class Meta:
        model = JournalLine # Or JournalEntryItem if you renamed it
        fields = ['account', 'debit_amount', 'credit_amount', 'description'] # CORRECTED FIELDS

    def clean(self): # This clean method is for the double-entry style
        cleaned_data = super().clean()
        debit = cleaned_data.get('debit_amount') or Decimal('0.00')
        credit = cleaned_data.get('credit_amount') or Decimal('0.00')
        account = cleaned_data.get('account')
        description = cleaned_data.get('description')

        if account or debit > 0 or credit > 0 or description:
            if not account:
                self.add_error('account', _("Account is required if other line data is present."))
            if debit < 0:
                self.add_error('debit_amount', _("Debit amount cannot be negative."))
            if credit < 0:
                self.add_error('credit_amount', _("Credit amount cannot be negative."))
            if debit > 0 and credit > 0:
                self.add_error(None, ValidationError(_("Enter either a debit or a credit amount per line, not both."), code='debit_credit_exclusive'))
            if account and debit == 0 and credit == 0: # If account selected, amount must be present
                if not description: # Allow zero amount if there's a description
                    self.add_error(None, ValidationError(_("Enter a debit or credit amount for the selected account, or a description."), code='amount_or_desc_required_for_account'))
        return cleaned_data


class BaseJournalLineFormSet(BaseInlineFormSet):
    def clean(self):
        super().clean()
        if any(self.errors):
            return

        total_debits = Decimal('0.00')
        total_credits = Decimal('0.00')
        actual_lines_count = 0

        for form in self.forms:
            if self.can_delete and self._should_delete_form(form):
                continue
            
            account = form.cleaned_data.get('account')
            debit = form.cleaned_data.get('debit_amount') or Decimal('0.00') # Use debit_amount
            credit = form.cleaned_data.get('credit_amount') or Decimal('0.00') # Use credit_amount
            description = form.cleaned_data.get('description')

            if not (account or debit > 0 or credit > 0 or description):
                if form.has_changed():
                    pass
                    continue

            if form.errors:
                return

            actual_lines_count += 1
            total_debits += debit
            total_credits += credit
        
        if self.validate_min and actual_lines_count < self.min_num:
            raise ValidationError(
                _("You must have at least %(min_num)d journal lines with amounts.") % {'min_num': self.min_num}
            )

        if actual_lines_count > 0 and total_debits != total_credits:
            diff = total_debits - total_credits
            raise ValidationError(
                _("Journal entry is unbalanced. Total Debits: %(debits)s, Total Credits: %(credits)s. Difference: %(diff)s") % {
                    'debits': total_debits, 'credits': total_credits, 'diff': diff
                }
            )
        
        if actual_lines_count == 0 and (self.initial_form_count() > 0 or any(f.has_changed() for f in self.forms if not self._should_delete_form(f))):
            raise ValidationError(_("At least one complete journal line (Account, Debit/Credit) is required."))



JournalLineFormSet = inlineformset_factory(
    JournalEntry, JournalLine, form=JournalLineForm, # Ensure correct model name
    formset=BaseJournalLineFormSet,
    fields=['account', 'debit_amount', 'credit_amount', 'description'], # CORRECTED FIELDS
    extra=1, min_num=2, validate_min=False,
    can_delete=True
)



class JournalEntryItemForm(forms.ModelForm):
    account = forms.ModelChoiceField(
        queryset=Account.objects.filter(is_active=True, can_be_used_in_je=True).order_by('account_type__name', 'name'),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm jei-account'}) # jei prefix for JournalEntryItem
    )
    debit = forms.DecimalField( # Matches your JournalEntryItem.debit field
        max_digits=12, decimal_places=2, initial=Decimal('0.00'), required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control form-control-sm jei-debit', 'step': '0.01', 'placeholder': '0.00'})
    )
    credit = forms.DecimalField( # Matches your JournalEntryItem.credit field
        max_digits=12, decimal_places=2, initial=Decimal('0.00'), required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control form-control-sm jei-credit', 'step': '0.01', 'placeholder': '0.00'})
    )
    description = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control form-control-sm jei-description', 'placeholder': _('Line description (optional)')})
    )

    class Meta:
        model = JournalEntryItem
        fields = ['account', 'debit', 'credit', 'description']

    def clean(self):
        cleaned_data = super().clean()
        debit_val = cleaned_data.get('debit') or Decimal('0.00')
        credit_val = cleaned_data.get('credit') or Decimal('0.00')
        account = cleaned_data.get('account')
        description_val = cleaned_data.get('description') # Use a different name to avoid conflict

        # Only perform strict validation if any part of the line has data.
        # This allows empty "extra" forms in the formset to be ignored.
        if account or debit_val > 0 or credit_val > 0 or description_val:
            if not account:
                self.add_error('account', _("Account is required if other line data is present."))
            
            if debit_val < Decimal('0.00'):
                self.add_error('debit', _("Debit amount cannot be negative."))
            if credit_val < Decimal('0.00'):
                self.add_error('credit', _("Credit amount cannot be negative."))
            
            if debit_val > Decimal('0.00') and credit_val > Decimal('0.00'):
                # Using None as field key links error to non_field_errors of this specific form in the formset
                self.add_error(None, ValidationError(_("Enter either a debit or a credit amount per line, not both."), code='debit_credit_exclusive'))
            
            if account and debit_val == Decimal('0.00') and credit_val == Decimal('0.00'):
                # Allow zero amounts if a description is provided (e.g., for memo lines if supported)
                # If descriptions are not sufficient for zero-amount lines, then this error is needed.
                if not description_val: # Or make description required if amount is zero
                    self.add_error(None, ValidationError(_("Enter a debit or credit amount for the selected account, or a line description."), code='amount_or_desc_required_for_account'))
        return cleaned_data



JournalEntryLineInlineFormSet = inlineformset_factory(
    JournalEntry,
    JournalLine,
    form=JournalLineForm,
    fields=['account', 'debit_amount', 'credit_amount', 'description'], # <<< ADDED 'description'
    extra=1,
    can_delete=True,
    can_order=False,
    # ...
)


class BaseJournalEntryLineFormSet(BaseInlineFormSet):
    def __init__(self, *args, **kwargs):
        # Pass tenant to forms in the formset
        self.tenant = kwargs.pop('tenant', None)
        super().__init__(*args, **kwargs)
        # Set the account queryset for all forms in the formset
        # This ensures all forms use the same tenant-specific COA
        # This assumes ChartOfAccount is tenant-specific either via schema or a tenant FK
        if self.tenant: # If ChartOfAccount has a direct tenant FK
            account_qs = Account.objects.filter(tenant=self.tenant, is_active=True, is_control_account=False).order_by('account_type__name', 'name')
        else: # If ChartOfAccount is schema-specific (no direct tenant FK)
            account_qs = Account.objects.filter(is_active=True, is_control_account=False).order_by('account_type__name', 'name')
        
        for form in self.forms:
            form.fields['account'].queryset = account_qs


    def clean(self):
        super().clean() # Call parent's clean first
        if any(self.errors): # Don't bother with further validation if individual forms have errors
            return

        total_debits = Decimal('0.00')
        total_credits = Decimal('0.00')
        line_count = 0

        for form in self.forms:
            # Skip empty forms that are not marked for deletion (common with 'extra' forms)
            if not form.has_changed() and not self.can_delete and form.empty_permitted: # Check if form has data or is initial
                if not any(form.cleaned_data.values()): # Truly empty extra form
                    continue 
            
            if self.can_delete and self._should_delete_form(form): # If form is marked for deletion
                continue

            # Ensure cleaned_data exists, might not if form is invalid or empty and skipped
            if not hasattr(form, 'cleaned_data'):
                continue # Should have been caught by `any(self.errors)`

            cleaned_data = form.cleaned_data
            debit = cleaned_data.get('debit_amount') or Decimal('0.00')
            credit = cleaned_data.get('credit_amount') or Decimal('0.00')
            account = cleaned_data.get('account')

            if account and (debit > Decimal('0.00') or credit > Decimal('0.00')):
                line_count += 1
            
            total_debits += debit
            total_credits += credit
        
        if line_count < self.min_num and self.validate_min: # min_num is set in inlineformset_factory
            # This error relates to the minimum number of forms, not just lines with values.
            # If min_num is 2, we need at least 2 valid (non-deleted) forms.
            # The 'has_at_least_one_line' check before was more about meaningful data.
            # This standard Django formset validation should handle min_num.
            # Let's add a check for at least *one* meaningful line.
            if line_count == 0:
                raise ValidationError(
                    _("At least one journal line with an account and amount is required."),
                    code='no_lines_provided'
                )

        if total_debits != total_credits:
            # This will be a non_form_error for the formset
            raise ValidationError(
                _("Journal entry is unbalanced. Total debits (%(debits)s) must equal total credits (%(credits)s). Difference: %(diff)s") % {
                    'debits': total_debits, 'credits': total_credits, 'diff': abs(total_debits - total_credits)
                }, code='unbalanced_entry'
            )
        
        if line_count > 0 and total_debits == Decimal('0.00') and total_credits == Decimal('0.00'):
            raise ValidationError(
                _("Journal entry cannot have zero total debits and credits if lines are present and populated."),
                code='zero_total_with_lines'
            )

# min_num=2 makes sure there are at least two lines displayed initially for debit/credit and enforces this
# extra=1 initially shows one empty form beyond the min_num. For JE, starting with 2 empty is good.
JournalEntryLineFormSet = inlineformset_factory(
    JournalEntry, 
    JournalLine, 
    form=JournalEntryLineForm,
    formset=BaseJournalEntryLineFormSet, # Use our custom base formset
    fields=['account', 'debit_amount', 'credit_amount', 'description'],
    extra=2, # Start with 2 empty forms
    min_num=0, # Allow saving a JE with 0 lines initially (e.g. draft then add), formset clean will check
    validate_min=False, # Let our custom clean handle "at least one line" logic
    can_delete=True,
    can_delete_extra=True # Allow deleting extra forms that haven't been saved
)



JournalLineFormSet = inlineformset_factory(
    JournalEntry,
    JournalLine,
    form=JournalLineForm,  # <<< ENSURE THIS NAME MATCHES THE CLASS DEFINED ABOVE
    formset=BaseJournalLineFormSet,
    fields=['account', 'debit_amount', 'credit_amount', 'description'],
    extra=1,
    min_num=2,
    validate_min=False,
    can_delete=True
)






# apps/accounting/forms.py
# ... (existing imports: forms, ChartOfAccount, timezone, _) ...

class AccountLedgerFilterForm(forms.Form):
    account = forms.ModelChoiceField(
        queryset=Account.objects.none(), # Will be populated in the view
        label=_("GL Account"),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm select2-account'}),
        required=True
    )
    start_date = forms.DateField(
        label=_("Start Date"),
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=False # Allow fetching all transactions if not specified (or up to a default start)
    )
    end_date = forms.DateField(
        label=_("End Date"),
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        initial=timezone.now().date(), # Default to today
        required=True # Usually an end date is good to have
    )

    def __init__(self, *args, **kwargs):
        # Pop tenant before super().__init__ if you plan to use it here
        # tenant = kwargs.pop('tenant', None) 
        super().__init__(*args, **kwargs)
        
        # Dynamically set the queryset for the account field
        # This assumes the form is instantiated with tenant context in the view
        # if tenant:
        #    self.fields['account'].queryset = ChartOfAccount.objects.filter(tenant=tenant, is_active=True, is_control_account=False).order_by('account_type__name', 'name')
        # else: # Schema-specific
        self.fields['account'].queryset = Account.objects.filter(is_active=True, is_control_account=False).order_by('account_type__name', 'name')

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and start_date > end_date:
            self.add_error('start_date', _("Start date cannot be after end date."))
        return cleaned_data
    
    
    
