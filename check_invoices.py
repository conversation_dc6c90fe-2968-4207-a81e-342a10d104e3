import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
from django_tenants.utils import get_tenant_model

# Get the tenant
TenantModel = get_tenant_model()
tenant = TenantModel.objects.get(schema_name='alpha')

# Switch to tenant schema
connection.set_tenant(tenant)

# Check existing invoices
from apps.fees.models import Invoice

try:
    invoices = Invoice.objects.all()
    print(f'Total invoices: {invoices.count()}')
    
    for invoice in invoices:
        print(f'Invoice ID: {invoice.id}, Number: "{invoice.invoice_number}", Student: {invoice.student}')
    
    # Check for empty invoice numbers
    empty_invoice_numbers = Invoice.objects.filter(invoice_number='')
    print(f'\nInvoices with empty invoice_number: {empty_invoice_numbers.count()}')
    
    # Check for null invoice numbers
    null_invoice_numbers = Invoice.objects.filter(invoice_number__isnull=True)
    print(f'Invoices with null invoice_number: {null_invoice_numbers.count()}')
    
except Exception as e:
    print(f'Error checking invoices: {e}')
    import traceback
    traceback.print_exc()
