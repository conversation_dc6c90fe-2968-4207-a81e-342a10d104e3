# apps/common/templatetags/math_filters.py
from django import template
from decimal import Decimal, InvalidOperation

register = template.Library()

@register.filter
def multiply(value, arg):
    try:
        return Decimal(str(value)) * Decimal(str(arg))
    except (<PERSON><PERSON>rror, ValueError, InvalidOperation):
        return None # Or 0, or raise error

@register.filter
def subtract(value, arg):
    try:
        return Decimal(str(value)) - Decimal(str(arg))
    except (TypeError, ValueError, InvalidOperation):
        return None # Or 0
    
    
    