# apps/common/templatetags/math_filters.py
from django import template
from decimal import Decimal, InvalidOperation

register = template.Library()

@register.filter
def multiply(value, arg):
    try:
        return Decimal(str(value)) * Decimal(str(arg))
    except (TypeError, ValueError, InvalidOperation):
        return None # Or 0, or raise error

@register.filter
def subtract(value, arg):
    try:
        return Decimal(str(value)) - Decimal(str(arg))
    except (TypeError, ValueError, InvalidOperation):
        return None # Or 0
    
    
# D:\school_fees_saas_v2\apps\common\templatetags\math_filters.py
from django import template
from decimal import Decimal, InvalidOperation # Import Decimal and InvalidOperation

register = template.Library()

@register.filter(name='abs')
def absolute_value(value):
    """Returns the absolute value of a number."""
    try:
        # Attempt to convert to Decimal first for precision with monetary values
        if isinstance(value, str):
            try:
                num_value = Decimal(value)
            except InvalidOperation: # Fallback if string is not a valid decimal
                num_value = float(value)
        elif not isinstance(value, (int, float, Decimal)):
            num_value = float(value) # Try to convert other types to float
        else:
            num_value = value
        
        return abs(num_value)
    except (ValueError, TypeError, InvalidOperation):
        # Handle cases where conversion or abs fails gracefully
        # You might want to log this error as well
        return value # Return original value or 0, or an empty string

# You might have other math filters here already
# @register.filter
# def subtract(value, arg):
#     try:
#         return Decimal(str(value)) - Decimal(str(arg))
#     except (ValueError, TypeError, InvalidOperation):
#         return None 
