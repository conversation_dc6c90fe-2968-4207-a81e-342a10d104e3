{# D:\school_fees_saas_v2\templates\reporting\balance_sheet.html #}
{% extends "tenant_base.html" %}
{% load static humanize %} {# Assuming you might use humanize for formatting numbers #}

{% block title %}
    {{ view_title|default:"Balance Sheet" }} - {{ request.tenant.name }}
{% endblock title %}

{% block page_specific_css %}
    {{ block.super }}
    <style>
        .report-table th, .report-table td {
            padding: 0.5rem;
            vertical-align: top;
        }
        .report-table .indent-1 { padding-left: 1.5em; }
        .report-table .indent-2 { padding-left: 3em; }
        .report-table .total-row td { font-weight: bold; border-top: 1px solid #dee2e6; }
        .report-table .grand-total-row td { font-weight: bold; border-top: 2px solid #000; border-bottom: 2px solid #000; }
        .text-end { text-align: right !important; }
    </style>
{% endblock page_specific_css %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-collection-fill" %} {# Define icon for this specific report #}
        {% include "partials/_report_header.html" %}
    {% endwith %}
{% comment %} <div class="container-fluid mt-4">
    <div class="pagetitle mb-3"> {% endcomment %}
        {% comment %} <h1>{{ view_title|default:"Balance Sheet" }}</h1> {% endcomment %}
        {% comment %} <nav>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{% url 'reporting:reporting_home' %}">Reports</a></li>
                <li class="breadcrumb-item active">Balance Sheet</li>
            </ol>
        </nav> {% endcomment %}
    </div>

    {% include "partials/_messages.html" %}
    {% comment %} {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Expense Filters" %} {% endcomment %}
    
    <div class="card">
        <div class="card-body">
            <h5 class="card-title">Filters</h5>
            <form method="get" class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label for="{{ form.as_at_date.id_for_label }}" class="form-label">{{ form.as_at_date.label }}</label>
                    {{ form.as_at_date }}
                    {% if form.as_at_date.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.as_at_date.errors %}{{ error }}{% endfor %}
                        </div>
                    {% endif %}
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary btn-sm">Apply Filter</button>
                    <a href="{% url 'reporting:balance_sheet_report' %}" class="btn btn-secondary btn-sm ms-2">Reset</a>
                </div>
            </form>
        </div>
    </div>

    {% if error %}
        <div class="alert alert-danger mt-3" role="alert">
            Error generating report: {{ error }}
        </div>
    {% elif as_at_date %} {# Check if report data is expected based on form submission/initial load #}
        <div class="card mt-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Balance Sheet as at {{ as_at_date|date:"D, d M Y" }}</h5>
                    <div>
                        {# Add Export Buttons Later if needed #}
                        {# <a href="?{{ request.GET.urlencode }}&export=csv" class="btn btn-sm btn-outline-secondary me-1"><i class="bi bi-file-earmark-spreadsheet"></i> CSV</a> #}
                        {# <a href="?{{ request.GET.urlencode }}&export=pdf" class="btn btn-sm btn-outline-danger"><i class="bi bi-file-earmark-pdf"></i> PDF</a> #}
                    </div>
                </div>
            </div>
            <div class="card-body pt-3">
                <div class="table-responsive">
                    <table class="table table-bordered report-table">
                        <thead>
                            <tr>
                                <th style="width: 60%;">Account</th>
                                <th style="width: 20%;" class="text-end">Amount ({{ request.tenant.profile.currency_symbol|default:"$" }})</th>
                                <th style="width: 20%;" class="text-end">Total ({{ request.tenant.profile.currency_symbol|default:"$" }})</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- ASSETS -->
                            <tr><td colspan="3"><strong>ASSETS</strong></td></tr>
                            <tr><td class="indent-1" colspan="3"><strong>Current Assets</strong></td></tr>
                            {% for item in assets.current %}
                                <tr>
                                    <td class="indent-2">{{ item.name }}</td>
                                    <td class="text-end">{{ item.balance|floatformat:2|intcomma }}</td>
                                    <td></td>
                                </tr>
                            {% empty %}
                                <tr><td class="indent-2" colspan="3"><em>No current assets.</em></td></tr>
                            {% endfor %}
                            <tr class="total-row">
                                <td class="indent-1"><strong>Total Current Assets</strong></td>
                                <td></td>
                                <td class="text-end"><strong>{{ assets.total_current|floatformat:2|intcomma }}</strong></td>
                            </tr>

                            <tr><td class="indent-1" colspan="3"><strong>Non-Current Assets</strong></td></tr>
                            {% for item in assets.non_current %}
                                <tr>
                                    <td class="indent-2">{{ item.name }}</td>
                                    <td class="text-end">{{ item.balance|floatformat:2|intcomma }}</td>
                                    <td></td>
                                </tr>
                            {% empty %}
                                <tr><td class="indent-2" colspan="3"><em>No non-current assets.</em></td></tr>
                            {% endfor %}
                            <tr class="total-row">
                                <td class="indent-1"><strong>Total Non-Current Assets</strong></td>
                                <td></td>
                                <td class="text-end"><strong>{{ assets.total_non_current|floatformat:2|intcomma }}</strong></td>
                            </tr>
                            <tr class="grand-total-row">
                                <td><strong>TOTAL ASSETS</strong></td>
                                <td></td>
                                <td class="text-end"><strong>{{ assets.total_assets|floatformat:2|intcomma }}</strong></td>
                            </tr>
                            <tr><td colspan="3"> </td></tr> <!-- Spacer -->

                            <!-- LIABILITIES -->
                            <tr><td colspan="3"><strong>LIABILITIES</strong></td></tr>
                            <tr><td class="indent-1" colspan="3"><strong>Current Liabilities</strong></td></tr>
                            {% for item in liabilities.current %}
                                <tr>
                                    <td class="indent-2">{{ item.name }}</td>
                                    <td class="text-end">{{ item.balance|floatformat:2|intcomma }}</td>
                                    <td></td>
                                </tr>
                            {% empty %}
                                <tr><td class="indent-2" colspan="3"><em>No current liabilities.</em></td></tr>
                            {% endfor %}
                            <tr class="total-row">
                                <td class="indent-1"><strong>Total Current Liabilities</strong></td>
                                <td></td>
                                <td class="text-end"><strong>{{ liabilities.total_current|floatformat:2|intcomma }}</strong></td>
                            </tr>

                            <tr><td class="indent-1" colspan="3"><strong>Non-Current Liabilities</strong></td></tr>
                            {% for item in liabilities.non_current %}
                                <tr>
                                    <td class="indent-2">{{ item.name }}</td>
                                    <td class="text-end">{{ item.balance|floatformat:2|intcomma }}</td>
                                    <td></td>
                                </tr>
                            {% empty %}
                                <tr><td class="indent-2" colspan="3"><em>No non-current liabilities.</em></td></tr>
                            {% endfor %}
                            <tr class="total-row">
                                <td class="indent-1"><strong>Total Non-Current Liabilities</strong></td>
                                <td></td>
                                <td class="text-end"><strong>{{ liabilities.total_non_current|floatformat:2|intcomma }}</strong></td>
                            </tr>
                            <tr class="total-row"> {# Changed to total-row for consistency, could be grand-total-row if preferred #}
                                <td><strong>TOTAL LIABILITIES</strong></td>
                                <td></td>
                                <td class="text-end"><strong>{{ liabilities.total_liabilities|floatformat:2|intcomma }}</strong></td>
                            </tr>
                            <tr><td colspan="3"> </td></tr> <!-- Spacer -->

                            <!-- EQUITY -->
                            <tr><td colspan="3"><strong>EQUITY</strong></td></tr>
                            {% for item in equity.accounts %}
                                <tr>
                                    <td class="indent-1">{{ item.name }}</td>
                                    <td class="text-end">{{ item.balance|floatformat:2|intcomma }}</td>
                                    <td></td>
                                </tr>
                            {% endfor %}
                            {% if retained_earnings is not None %} {# Check if retained_earnings is passed and not None #}
                            <tr>
                                <td class="indent-1">Retained Earnings (Opening)</td>
                                <td class="text-end">{{ retained_earnings|floatformat:2|intcomma }}</td>
                                <td></td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td class="indent-1">Net Income for Period</td>
                                <td class="text-end">{{ net_income_for_period|floatformat:2|intcomma }}</td>
                                <td></td>
                            </tr>
                            <tr class="total-row"> {# Changed to total-row #}
                                <td><strong>TOTAL EQUITY</strong></td>
                                <td></td>
                                <td class="text-end"><strong>{{ equity.total_equity|floatformat:2|intcomma }}</strong></td>
                            </tr>
                            <tr><td colspan="3"> </td></tr>

                            <tr class="grand-total-row">
                                <td><strong>TOTAL LIABILITIES AND EQUITY</strong></td>
                                <td></td>
                                <td class="text-end"><strong>{{ total_liabilities_and_equity|floatformat:2|intcomma }}</strong></td>
                            </tr>

                            {% if balance_check is not None and balance_check != 0 %}
                            <tr class="total-row bg-warning text-dark">
                                <td><strong>BALANCE CHECK (Assets - (Liabilities + Equity))</strong></td>
                                <td></td>
                                <td class="text-end"><strong>{{ balance_check|floatformat:2|intcomma }}</strong></td>
                            </tr>
                            {% elif balance_check == 0 %}
                            <tr class="total-row bg-success-light">
                                <td colspan="3" class="text-center"><em>Balance sheet is balanced.</em></td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    {% else %}
        <div class="alert alert-info mt-3" role="alert">
            Please use the filter to generate the balance sheet.
        </div>
    {% endif %}
</div>
{% endblock content %}