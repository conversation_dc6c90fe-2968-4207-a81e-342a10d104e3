{# D:\school_fees_saas_v2\templates\reporting\pdf\_pdf_base.html #}
{% load static humanize %} {# Added humanize for consistency #}
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{% block pdf_title %}School Document{% endblock pdf_title %}</title>
    <style type="text/css">
        @page {
            size: a4 portrait;
            margin: 1.5cm; /* Top, Right, Bottom, Left margin for the printable area */

            @frame header_frame {
                -pdf-frame-content: page_header_content_container;
                left: 1cm; right: 1cm; /* Slightly less than page margin for full bleed feel */
                top: 0.5cm; height: 3.5cm; /* Allocate space for header */
            }
            @frame content_frame { /* Main content flows here */
                left: 1.5cm; /* Page margin */
                right: 1.5cm; /* Page margin */
                top: 4.2cm; /* Must be > header_frame.top + header_frame.height + some_spacing */
                bottom: 2cm; /* Must be > footer_frame.bottom + footer_frame.height + some_spacing */
            }
            @frame footer_frame {
                -pdf-frame-content: page_footer_content_container;
                left: 1cm; right: 1cm;
                bottom: 0.5cm; height: 1.3cm; /* Allocate space for footer */
            }
        }

        body { 
            font-family: "Helvetica", "Arial", sans-serif; 
            font-size: 9pt; /* Slightly smaller default for more content */
            color: #333333; 
            line-height: 1.3;
        }

        /* Header specific styles */
        #page_header_content_container {
            width: 100%;
        }
        #page_header_content_container table {
            width: 100%;
            border-collapse: collapse;
        }
        #page_header_content_container td { 
            border: none; 
            vertical-align: top;
            padding: 0;
        }
        #page_header_content_container .school-details h2 { 
            margin: 0 0 5px 0; 
            font-size: 14pt; /* Adjusted */
            color: #000000; 
        }
        #page_header_content_container .school-details p { 
            margin: 1px 0; 
            font-size: 8pt; /* Adjusted */
        }
        #page_header_content_container .document-info {
            text-align: right;
        }
        #page_header_content_container .document-info h1 {
             margin: 0 0 8px 0; 
             font-size: 22pt; /* Adjusted */
             text-transform: uppercase;
        }
        #page_header_content_container .document-info p { 
            margin: 1px 0; 
            font-size: 9pt; /* Adjusted */
        }
        .logo { 
            max-width: 150px; /* Adjusted */
            max-height: 60px; /* Adjusted */
            /* float: right; is fine, or manage with table cell alignment */
        }
        
        /* Footer specific styles */
        #page_footer_content_container { 
            text-align: center; 
            font-size: 8pt; 
            color: #6c757d; /* Muted color */
        }
        #page_footer_content_container .page-number::after {
            content: counter(page) " of " counter(pages); /* Corrected page numbering */
        }


        /* General styles for main content (can be overridden by child) */
        h3 { font-size: 12pt; font-weight: bold; margin-top: 15px; margin-bottom: 8px; border-bottom: 0.5px solid #999; padding-bottom: 3px;}
        h4 { font-size: 11pt; font-weight: bold; margin-top: 10px; margin-bottom: 5px; }
        
        table.items-table { width: 100%; border-collapse: collapse; margin-top: 5px; margin-bottom: 15px; font-size: 9pt;}
        .items-table th, .items-table td { border: 0.5px solid #cccccc; padding: 5px 7px; text-align: left; vertical-align: top; }
        .items-table thead th { background-color: #EAEAEA; font-weight: bold; }
        
        .text-end { text-align: right !important; }
        .text-center { text-align: center !important; }
        .fw-bold { font-weight: bold !important; }
        .mb-0 { margin-bottom: 0 !important; }
        .mb-1 { margin-bottom: 0.20rem !important; } /* Smaller margin */
        
        .no-border, .no-border td, .no-border th { border: none !important; }
        .no-border-bottom, .no-border-bottom td, .no-border-bottom th { border-bottom: none !important; }

        /* Additional styles that might be inherited by child PDF templates */
        {% block pdf_base_extra_styles %}{% endblock pdf_base_extra_styles %}
    </style>
    {# Allow child templates to add more specific styles #}
    {% block pdf_extra_styles %}{% endblock pdf_extra_styles %}
</head>
<body>
    {# Fixed Header Content Container #}
    <div id="page_header_content_container">
        {% block pdf_header_content %}
        <table>
            <tr>
                <td style="width: 60%;" class="school-details">
                    {% if school_profile %}
                        <h2>
                            {{ school_profile.school_name_on_reports|default:school_profile.school_name_override|default:tenant.name|default:"School Name" }}
                        </h2>
                        {% if school_profile.address_line_1 %}<p>{{ school_profile.address_line_1 }}</p>{% endif %}
                        {% if school_profile.address_line_2 %}<p>{{ school_profile.address_line_2 }}</p>{% endif %}
                        <p>
                            {% if school_profile.city %}{{ school_profile.city }}{% if school_profile.state_province or school_profile.postal_code %}, {% endif %}{% endif %}
                            {% if school_profile.state_province %}{{ school_profile.state_province }} {% endif %}
                            {% if school_profile.postal_code %}{{ school_profile.postal_code }}{% endif %}
                        </p>
                        {% if school_profile.country_name %} <p>{{ school_profile.country_name }}</p>{% endif %}
                        {% if school_profile.phone_number %}<p>Phone: {{ school_profile.phone_number }}</p>{% endif %}
                        {% if school_profile.school_email %}<p>Email: {{ school_profile.school_email }}</p>{% endif %}
                        {% block school_extra_header_info %}{% endblock school_extra_header_info %}
                    {% else %}
                        <h2>{{ tenant.name|default:"School Name" }}</h2>
                        <p>School profile details not available.</p>
                    {% endif %}
                </td>
                <td style="width: 40%; text-align: right;" class="document-info">
                    {% if school_profile and school_profile.logo and school_profile.logo.path %}
                        <img src="{{ school_profile.logo.path }}" alt="Logo" class="logo">
                    {% else %}
                         <div style="height: 60px;"> </div> {# Placeholder for logo height #}
                    {% endif %}
                    <h1>{% block document_name_header %}DOCUMENT{% endblock document_name_header %}</h1>
                    {% block document_specific_header_info %}
                        {# e.g., Invoice No, Date, Due Date - to be filled by child #}
                    {% endblock document_specific_header_info %}
                </td>
            </tr>
        </table>
        <hr style="margin-top: 2px; margin-bottom: 2px; border: none; border-top: 0.5px solid #555;">
        {% endblock pdf_header_content %}
    </div>

    {# Fixed Footer Content Container #}
    <div id="page_footer_content_container">
        {% block pdf_footer_content %}
        <p>
            Page <span class="page-number"></span>
            | {{ school_profile.school_name_on_reports|default:school_profile.school_name_override|default:tenant.name|default:"School Name" }}
            | Generated: {% now "F d, Y, H:i" %}
        </p>
        {% endblock pdf_footer_content %}
    </div>

    {# Main Content Area - This div itself is not strictly needed by xhtml2pdf if frames are used, #}
    {# but child templates will target pdf_main_content. Content here flows into content_frame. #}
    {% block pdf_main_content %}
        <p>Error: Main content block ('pdf_main_content') not defined in the specific document template.</p>
    {% endblock pdf_main_content %}
</body>
</html>

























{% comment %} {# D:\school_fees_saas_v2\templates\reporting\pdf\_pdf_base.html (Ensure this is your actual path if project-level, or adapt if app-level) #}
{% load static %} {# Add humanize if you use filters like intcomma here #}
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{% block pdf_title %}School Document{% endblock %}</title>
    <style>
        /* Basic CSS for PDF - For more complex styles, link an external CSS file */
        @page {
            size: a4 portrait;
            margin: 1.5cm 1.5cm 2cm 1.5cm; /* top, right, bottom, left */

            @frame header_frame {
                -pdf-frame-content: page_header_content; /* Content for the header */
                left: 1cm; width: 19cm; top: 0.5cm; height: 4cm; /* Adjust dimensions */
            }
            @frame content_frame {
                left: 1cm; width: 19cm; top: 5cm; height: 20cm; /* Adjust top based on header height, and height */
            }
            @frame footer_frame {
                -pdf-frame-content: page_footer_content; /* Content for the footer */
                left: 1cm; width: 19cm; bottom: 0.5cm; height: 1cm; /* Adjust dimensions */
            }
        }

        body { font-family: "Helvetica", "Arial", sans-serif; font-size: 10pt; color: #333333; }
        #page_header_content table, #page_header_content td { border: none; vertical-align: top; }
        #page_header_content h2 { margin: 0 0 5px 0; font-size: 16pt; color: #000000; }
        #page_header_content p { margin: 1px 0; font-size: 9pt; line-height: 1.3; }
        .logo { max-width: 160px; max-height: 80px; float: right; } /* Example styling */
        
        #page_footer_content { text-align: center; font-size: 8pt; color: #777777; }

        /* Styles from your specific document template (invoice_pdf_template.html) will apply to #content_frame */
        /* You provided good styles in your invoice template directly, which is fine */
        h4 { font-size: 12pt; font-weight: bold; margin-top: 15px; margin-bottom: 5px; }
        table.items-table { width: 100%; border-collapse: collapse; margin-top: 10px; margin-bottom: 15px; }
        .items-table th, .items-table td { border: 1px solid #cccccc; padding: 6px; text-align: left; vertical-align: top; }
        .items-table th { background-color: #f0f0f0; font-weight: bold; }
        .text-end { text-align: right !important; }
        .fw-bold { font-weight: bold !important; }
        .mb-0 { margin-bottom: 0 !important; }
        .mb-1 { margin-bottom: 0.25rem !important; }
        .no-border td, .no-border th, .no-border-bottom td, .no-border-bottom th { border-bottom: none !important; }
        .no-border td, .no-border th { border-left: none !important; border-right: none !important; border-top: none !important; }


    </style>
    {% block pdf_extra_styles %}{% endblock %}
</head>
<body>
    {# Fixed Header Content #}
    <div id="page_header_content">
        <table>
            <tr>
                <td style="width: 65%;">
                    {% if school_profile %}
                        <h2>
                            {{ school_profile.school_name_on_reports|default:school_profile.school_name_override|default:tenant.name|default:"School Name" }}
                        </h2>
                        {% if school_profile.address_line1 %}<p>{{ school_profile.address_line1 }}</p>{% endif %}
                        {% if school_profile.address_line2 %}<p>{{ school_profile.address_line2 }}</p>{% endif %}
                        <p>
                            {% if school_profile.city %}{{ school_profile.city }}{% if school_profile.state_province or school_profile.postal_code %}, {% endif %}{% endif %}
                            {% if school_profile.state_province %}{{ school_profile.state_province }} {% endif %}
                            {% if school_profile.postal_code %}{{ school_profile.postal_code }}{% endif %}
                            {% if school_profile.country_name %} <br>{{ school_profile.country_name }}{% endif %}
                        </p>
                        {% if school_profile.phone_number %}<p>Phone: {{ school_profile.phone_number }}</p>{% endif %}
                        {% if school_profile.school_email %}<p>Email: {{ school_profile.school_email }}</p>{% endif %}
                        {% block school_extra_header_info %}{% endblock %} {# For things like Tax ID #}
                    {% else %}
                        <h2>{{ tenant.name|default:"School Name" }}</h2>
                        <p>School profile details not available.</p>
                    {% endif %}
                </td>
                <td style="width: 35%; text-align: right;">
                    {% if school_profile and school_profile.logo and school_profile.logo.url %}
                        <img src="{{ school_profile.logo.url }}" alt="Logo" class="logo">
                    {% else %}
                        <p style="padding-top:20px;"><em>(School Logo)</em></p>
                    {% endif %}
                    <h1 style="font-size: 20pt; margin-top: 10px; margin-bottom:0;">{% block document_name_header %}DOCUMENT{% endblock %}</h1>
                    {# Document specific header info from child template #}
                    {% block document_specific_header_info %}{% endblock %}
                </td>
            </tr>
        </table>
        <hr style="margin-top: 0px; margin-bottom: 5px; border-top: 1px solid #555;">
    </div>

    {# Fixed Footer Content #}
    <div id="page_footer_content">
        <p class="footer">
            Page <pdf:pagenumber /> of <pdf:pagecount />
            | {{ school_profile.school_name_on_reports|default:school_profile.school_name_override|default:tenant.name|default:"School Name" }}
            | Generated: {% now "d M Y, H:i" %}
        </p>
    </div>

    {# Main Content Area - This will be filled by child templates #}
    <div id="content_frame">
        {% block pdf_main_content %}
            <p>This is the default PDF content. Please override 'pdf_main_content' block in your specific template.</p>
        {% endblock pdf_main_content %}
    </div>
</body>
</html>
 {% endcomment %}














{% comment %} {# D:\school_fees_saas_v2\templates\reporting\pdf\_pdf_base.html (Example Structure) #}
{% load static %}
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{% block pdf_title %}School Document{% endblock %}</title>
    <style>
        /* Minimal CSS for PDF - actual styles should be in a linked CSS file for better management */
        @page {
            size: a4 portrait;
            margin: 1.5cm; /* Adjust margins as needed */

            @frame header_frame { /* Static header */
                -pdf-frame-content: page_header;
                left: 1.5cm; right: 1.5cm; top: 1cm; height: 4cm; /* Adjust height */
            }
            @frame footer_frame { /* Static footer */
                -pdf-frame-content: page_footer;
                left: 1.5cm; right: 1.5cm; bottom: 0.5cm; height: 1cm;
            }
            @frame content_frame { /* Content frame */
                left: 1.5cm; right: 1.5cm; top: 5.5cm; bottom: 2cm; /* Adjust top/bottom based on header/footer */
            }
        }

        body { font-family: "Helvetica", "Arial", sans-serif; font-size: 10pt; color: #333; }
        h1, h2, h3, h4 { margin-top: 0; margin-bottom: 0.5em; color: #111; }
        p { margin-top: 0; margin-bottom: 0.7em; line-height: 1.4; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 15px; }
        th, td { border: 1px solid #ddd; padding: 6px; text-align: left; vertical-align: top; }
        th { background-color: #f0f0f0; font-weight: bold; }
        .text-end { text-align: right !important; }
        .fw-bold { font-weight: bold !important; }
        .mb-0 { margin-bottom: 0 !important; }
        .mb-1 { margin-bottom: 0.25rem !important; }
        .no-border td, .no-border th { border: none !important; }
        .header-table td { vertical-align: middle; }
        .logo { max-width: 150px; max-height: 70px; /* Adjust as needed */ }
        .footer { font-size: 8pt; text-align: center; color: #777; }
    </style>
    {% block pdf_specific_styles %}{% endblock %}
</head>
<body>
    {# Header Content - This will be repeated on each page if using @frame #}
    <div id="page_header">
        <table class="no-border header-table">
            <tr>
                <td style="width: 60%;" class="no-border">
                    {% if school_profile %}
                        <h2 class="fw-bold mb-1">
                            {{ school_profile.school_name_on_reports|default:school_profile.school_name_override|default:tenant.name|default:"School Name" }}
                        </h2>
                        {% if school_profile.address_line1 %}<p class="mb-0">{{ school_profile.address_line1 }}</p>{% endif %}
                        {% if school_profile.address_line2 %}<p class="mb-0">{{ school_profile.address_line2 }}</p>{% endif %}
                        <p class="mb-0">
                            {% if school_profile.city %}{{ school_profile.city }}{% if school_profile.state_province or school_profile.postal_code %}, {% endif %}{% endif %}
                            {% if school_profile.state_province %}{{ school_profile.state_province }} {% endif %}
                            {% if school_profile.postal_code %}{{ school_profile.postal_code }}{% endif %}
                        </p>
                        {% if school_profile.country_name %}<p class="mb-0">{{ school_profile.country_name }}</p>{% endif %}
                        {% if school_profile.phone_number %}<p class="mb-0">Phone: {{ school_profile.phone_number }}</p>{% endif %}
                        {% if school_profile.school_email %}<p class="mb-0">Email: {{ school_profile.school_email }}</p>{% endif %}
                    {% else %}
                        <h2 class="fw-bold mb-1">{{ tenant.name|default:"School Name" }}</h2>
                        <p>Address not available.</p>
                    {% endif %}
                </td>
                <td style="width: 40%; text-align: right;" class="no-border">
                    {% if school_profile and school_profile.logo %}
                        {# The link_callback in your utils.py will try to resolve this logo.url #}
                        <img src="{{ school_profile.logo.url }}" alt="School Logo" class="logo">
                    {% else %}
                        <p>(No Logo)</p>
                    {% endif %}
                    <h1 style="margin-top: 10px; margin-bottom:0;">{% block document_name_header %}DOCUMENT{% endblock %}</h1>
                    {# Document specific header info from child template #}
                    {% block document_specific_header_info %}{% endblock %}
                </td>
            </tr>
        </table>
        <hr style="margin-top: 5px; margin-bottom: 5px;">
    </div>

    {# Footer Content - Repeated on each page #}
    <div id="page_footer">
        <p class="footer">
            Page <pdf:pagenumber /> of <pdf:pagecount />
            <br>
            {{ school_profile.school_name_on_reports|default:school_profile.school_name_override|default:tenant.name|default:"School Name" }} - Generated on {% now "d M Y, H:i" %}
        </p>
    </div>

    {# Main Content Area - This will be filled by child templates #}
    <div id="content_frame">
        {% block pdf_main_content %}
            <p>Default PDF content if not overridden.</p>
        {% endblock pdf_main_content %}
    </div>
</body>
</html> {% endcomment %}






{% comment %} {# templates/reporting/pdf/_pdf_base.html #}
{% load static humanize %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{% block pdf_title %}{{ report_title|default:"Document" }}{% endblock %}</title>
    <style type="text/css">
        @page {
            size: A4 portrait; /* Or landscape if needed */
            margin: 1.5cm; /* Generous margins */

            @frame header_frame {
                -pdf-frame-content: page-header;
                left: 1cm; width: 19cm; top: 1cm; height: 3.5cm; /* Adjust height */
            }
            @frame content_frame {
                left: 1cm; width: 19cm; top: 5cm; height: 20cm; /* Adjust top & height */
            }
            @frame footer_frame {
                -pdf-frame-content: page-footer;
                left: 1cm; width: 19cm; bottom: 0.5cm; height: 1cm; /* Use bottom for footer */
            }
        }
        body { font-family: "Helvetica", Arial, sans-serif; font-size: 9pt; color: #333333; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 12px; }
        th, td { border: 1px solid #cccccc; padding: 5px; text-align: left; vertical-align: top; }
        th { background-color: #eeeeee; font-weight: bold; }
        .text-center { text-align: center; }
        .text-end { text-align: right; } /* Bootstrap 5 class name */
        .text-danger { color: #dc3545; }
        .text-success { color: #198754; }
        .fw-bold { font-weight: bold; }
        .mb-0 { margin-bottom: 0 !important; }
        .mt-0 { margin-top: 0 !important; }
        .mb-1 { margin-bottom: 0.25rem !important; }
        .mb-2 { margin-bottom: 0.5rem !important; }
        .mt-3 { margin-top: 1rem !important; }
        h1, h2, h3, h4 { margin:0; padding:0; font-weight: normal; }
        h2 { font-size: 16pt; margin-bottom: 5px; }
        h3 { font-size: 12pt; margin-bottom: 3px; }
        p { margin-bottom: 0.5em; line-height: 1.4; }
        .no-border { border: none !important; } /* For layout tables */
        .header-table td, .footer-table td { border: none; padding: 0; } /* No borders in header/footer tables */

        .logo-container { width: 25%; float: left; } /* Example layout */
        .school-info-container { width: 45%; float: left; }
        .document-info-container { width: 30%; float: right; text-align: right;}

        .clearfix::after { content: ""; clear: both; display: table; } /* Clearfix for floats */

        /* Ensure paths to static assets are resolvable by link_callback */
        /* For example, if you have a global CSS for PDFs: */
        /* @font-face { font-family: 'DejaVuSans'; src: url({% static 'fonts/DejaVuSans.ttf' %}); } */
        /* body { font-family: 'DejaVuSans'; } */

    </style>
    {% block extra_pdf_styles %}{% endblock %}
</head>
<body>
    {# Header Content - Placed inside a div that pisa will use for @frame #}
    <div id="page-header">
        <div class="clearfix">
            <div class="logo-container">
                {% if school_profile and school_profile.logo and school_profile.logo.url %}
                    {# For xhtml2pdf, using settings.MEDIA_ROOT is more reliable if logo is local #}
                    {# Assuming link_callback can resolve this path #}
                    <img src="{{ school_profile.logo.url }}" alt="Logo" style="max-height: 60px; max-width: 100%;">
                {% else %}
                      {# Placeholder if no logo #}
                {% endif %}
            </div>
            <div class="school-info-container">
                <h2 class="fw-bold mb-1">{{ school_profile.name|default:request.tenant.name|default:"School Name" }}</h2>
                {% if school_profile.address_line_1 %}<p class="mb-0">{{ school_profile.address_line_1 }}</p>{% endif %}
                {% if school_profile.address_line2 %}<p class="mb-0">{{ school_profile.address_line2 }}</p>{% endif %}
                <p class="mb-0">
                    {% if school_profile.city %}{{ school_profile.city }}{% endif %}
                    {% if school_profile.city and school_profile.state_province %}, {% endif %}
                    {% if school_profile.state_province %}{{ school_profile.state_province }}{% endif %}
                    {% if school_profile.postal_code %} {{ school_profile.postal_code }}{% endif %}
                </p>
                {% if school_profile.phone_number %}<p class="mb-0">Phone: {{ school_profile.phone_number }}</p>{% endif %}
                {% if school_profile.school_email %}<p class="mb-0">Email: {{ school_profile.school_email }}</p>{% endif %}
            </div>
            <div class="document-info-container">
                <h3 class="fw-bold mb-1">{% block document_name_header %}{{ report_title|default:"Document" }}{% endblock %}</h3>
                {% block document_specific_header_info %}
                <p class="mb-0">Date: {{ current_datetime|date:"d M Y" }}</p>
                {% endblock %}
            </div>
        </div>
        <hr style="margin-top: 5px; margin-bottom: 5px; border-top: 1px solid #888;">
    </div>

    {# Footer Content #}
    <div id="page-footer">
        <p class="text-center" style="font-size: 8pt; color: #777;">
            Page <pdf:pagenumber /> of <pdf:pagecount />
            {% if request.tenant %} - {{ request.tenant.name }}{% endif %}
        </p>
    </div>

    {# Main Content - This will be filled by the specific report/invoice/receipt template #}
    <div id="report-content">
        {% block pdf_main_content %}
            <p>Main PDF content goes here.</p>
        {% endblock %}
    </div>
</body>
</html> {% endcomment %}





