{# D:\school_fees_saas_v2\templates\reporting\pdf\_pdf_base.html #}
{% load static humanize %} {# Added humanize for consistency #}
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{% block pdf_title %}School Document{% endblock pdf_title %}</title>
    <style type="text/css">
        @page {
            size: a4 portrait;
            margin: 1.5cm; /* Top, Right, Bottom, Left margin for the printable area */

            @frame header_frame {
                -pdf-frame-content: page_header_content_container;
                left: 1cm; right: 1cm; /* Slightly less than page margin for full bleed feel */
                top: 0.5cm; height: 3.5cm; /* Allocate space for header */
            }
            @frame content_frame { /* Main content flows here */
                left: 1.5cm; /* Page margin */
                right: 1.5cm; /* Page margin */
                top: 4.2cm; /* Must be > header_frame.top + header_frame.height + some_spacing */
                bottom: 2cm; /* Must be > footer_frame.bottom + footer_frame.height + some_spacing */
            }
            @frame footer_frame {
                -pdf-frame-content: page_footer_content_container;
                left: 1cm; right: 1cm;
                bottom: 0.5cm; height: 1.3cm; /* Allocate space for footer */
            }
        }

        body { 
            font-family: "Helvetica", "Arial", sans-serif; 
            font-size: 9pt; /* Slightly smaller default for more content */
            color: #333333; 
            line-height: 1.3;
        }

        /* Header specific styles */
        #page_header_content_container {
            width: 100%;
        }
        #page_header_content_container table {
            width: 100%;
            border-collapse: collapse;
        }
        #page_header_content_container td { 
            border: none; 
            vertical-align: top;
            padding: 0;
        }
        #page_header_content_container .school-details h2 { 
            margin: 0 0 5px 0; 
            font-size: 14pt; /* Adjusted */
            color: #000000; 
        }
        #page_header_content_container .school-details p { 
            margin: 1px 0; 
            font-size: 8pt; /* Adjusted */
        }
        #page_header_content_container .document-info {
            text-align: right;
        }
        #page_header_content_container .document-info h1 {
            margin: 0 0 8px 0; 
             font-size: 22pt; /* Adjusted */
            text-transform: uppercase;
        }
        #page_header_content_container .document-info p { 
            margin: 1px 0; 
            font-size: 9pt; /* Adjusted */
        }
        .logo { 
            max-width: 150px; /* Adjusted */
            max-height: 60px; /* Adjusted */
            /* float: right; is fine, or manage with table cell alignment */
        }
        
        /* Footer specific styles */
        #page_footer_content_container { 
            text-align: center; 
            font-size: 8pt; 
            color: #6c757d; /* Muted color */
        }
        #page_footer_content_container .page-number::after {
            content: counter(page) " of " counter(pages); /* Corrected page numbering */
        }


        /* General styles for main content (can be overridden by child) */
        h3 { font-size: 12pt; font-weight: bold; margin-top: 15px; margin-bottom: 8px; border-bottom: 0.5px solid #999; padding-bottom: 3px;}
        h4 { font-size: 11pt; font-weight: bold; margin-top: 10px; margin-bottom: 5px; }
        
        table.items-table { width: 100%; border-collapse: collapse; margin-top: 5px; margin-bottom: 15px; font-size: 9pt;}
        .items-table th, .items-table td { border: 0.5px solid #cccccc; padding: 5px 7px; text-align: left; vertical-align: top; }
        .items-table thead th { background-color: #EAEAEA; font-weight: bold; }
        
        .text-end { text-align: right !important; }
        .text-center { text-align: center !important; }
        .fw-bold { font-weight: bold !important; }
        .mb-0 { margin-bottom: 0 !important; }
        .mb-1 { margin-bottom: 0.20rem !important; } /* Smaller margin */
        
        .no-border, .no-border td, .no-border th { border: none !important; }
        .no-border-bottom, .no-border-bottom td, .no-border-bottom th { border-bottom: none !important; }

        /* Additional styles that might be inherited by child PDF templates */
        {% block pdf_base_extra_styles %}{% endblock pdf_base_extra_styles %}
    </style>
    {# Allow child templates to add more specific styles #}
    {% block pdf_extra_styles %}{% endblock pdf_extra_styles %}
</head>
<body>
    {# Fixed Header Content Container #}
    <div id="page_header_content_container">
        {% block pdf_header_content %}
        <table>
            <tr>
                <td style="width: 60%;" class="school-details">
                    {% if school_profile %}
                        <h2>
                            {{ school_profile.school_name_on_reports|default:school_profile.school_name_override|default:tenant.name|default:"School Name" }}
                        </h2>
                        {% if school_profile.address_line_1 %}<p>{{ school_profile.address_line_1 }}</p>{% endif %}
                        {% if school_profile.address_line_2 %}<p>{{ school_profile.address_line_2 }}</p>{% endif %}
                        <p>
                            {% if school_profile.city %}{{ school_profile.city }}{% if school_profile.state_province or school_profile.postal_code %}, {% endif %}{% endif %}
                            {% if school_profile.state_province %}{{ school_profile.state_province }} {% endif %}
                            {% if school_profile.postal_code %}{{ school_profile.postal_code }}{% endif %}
                        </p>
                        {% if school_profile.country_name %} <p>{{ school_profile.country_name }}</p>{% endif %}
                        {% if school_profile.phone_number %}<p>Phone: {{ school_profile.phone_number }}</p>{% endif %}
                        {% if school_profile.school_email %}<p>Email: {{ school_profile.school_email }}</p>{% endif %}
                        {% block school_extra_header_info %}{% endblock school_extra_header_info %}
                    {% else %}
                        <h2>{{ tenant.name|default:"School Name" }}</h2>
                        <p>School profile details not available.</p>
                    {% endif %}
                </td>
                <td style="width: 40%; text-align: right;" class="document-info">
                    {% if school_profile and school_profile.logo and school_profile.logo.path %}
                        <img src="{{ school_profile.logo.path }}" alt="Logo" class="logo">
                    {% else %}
                        <div style="height: 60px;"> </div> {# Placeholder for logo height #}
                    {% endif %}
                    <h1>{% block document_name_header %}DOCUMENT{% endblock document_name_header %}</h1>
                    {% block document_specific_header_info %}
                        {# e.g., Invoice No, Date, Due Date - to be filled by child #}
                    {% endblock document_specific_header_info %}
                </td>
            </tr>
        </table>
        <hr style="margin-top: 2px; margin-bottom: 2px; border: none; border-top: 0.5px solid #555;">
        {% endblock pdf_header_content %}
    </div>

    {# Fixed Footer Content Container #}
    <div id="page_footer_content_container">
        {% block pdf_footer_content %}
        <p>
            Page <span class="page-number"></span>
            | {{ school_profile.school_name_on_reports|default:school_profile.school_name_override|default:tenant.name|default:"School Name" }}
            | Generated: {% now "F d, Y, H:i" %}
        </p>
        {% endblock pdf_footer_content %}
    </div>

    {# Main Content Area - This div itself is not strictly needed by xhtml2pdf if frames are used, #}
    {# but child templates will target pdf_main_content. Content here flows into content_frame. #}
    {% block pdf_main_content %}
        <p>Error: Main content block ('pdf_main_content') not defined in the specific document template.</p>
    {% endblock pdf_main_content %}
</body>
</html>

