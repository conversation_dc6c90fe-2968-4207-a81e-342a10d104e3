import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
from django_tenants.utils import get_tenant_model
from decimal import Decimal

# Get the tenant
TenantModel = get_tenant_model()
tenant = TenantModel.objects.get(schema_name='alpha')

# Switch to tenant schema
connection.set_tenant(tenant)

# Test the Invoice model
from apps.fees.models import Invoice
from apps.students.models import Student
from apps.schools.models import AcademicYear, Term
from django.utils import timezone
from datetime import timedelta

try:
    # Get required objects for testing
    student = Student.objects.first()
    academic_year = AcademicYear.objects.first()
    term = Term.objects.first()
    
    if not all([student, academic_year]):
        print('Missing required objects for testing')
        print(f'Student: {student}')
        print(f'Academic Year: {academic_year}')
        print(f'Term: {term}')
    else:
        print(f'Using student: {student}')
        print(f'Using academic year: {academic_year}')
        print(f'Using term: {term}')
        
        # Test creating an invoice
        invoice = Invoice.objects.create(
            student=student,
            academic_year=academic_year,
            term=term,
            issue_date=timezone.now().date(),
            due_date=timezone.now().date() + timedelta(days=30),
            subtotal_amount=Decimal('150.00'),
            total_concession_amount=Decimal('15.00'),
            amount_paid=Decimal('0.00'),
            notes_to_parent='Test invoice with fixed invoice number generation'
        )
        
        print(f'✓ Invoice created successfully: {invoice}')
        print(f'  - Invoice Number: {invoice.invoice_number}')
        print(f'  - Student: {invoice.student}')
        print(f'  - Subtotal: {invoice.subtotal_amount}')
        print(f'  - Concessions: {invoice.total_concession_amount}')
        print(f'  - Amount Paid: {invoice.amount_paid}')
        print(f'  - Notes: {invoice.notes_to_parent}')
        
        # Test creating another invoice to ensure no duplicate invoice numbers
        invoice2 = Invoice.objects.create(
            student=student,
            academic_year=academic_year,
            term=term,
            issue_date=timezone.now().date(),
            due_date=timezone.now().date() + timedelta(days=30),
            subtotal_amount=Decimal('200.00'),
            total_concession_amount=Decimal('20.00'),
            amount_paid=Decimal('0.00'),
            notes_to_parent='Second test invoice'
        )
        
        print(f'✓ Second invoice created successfully: {invoice2}')
        print(f'  - Invoice Number: {invoice2.invoice_number}')
        
        # Clean up
        invoice.delete()
        invoice2.delete()
        print('✓ Test invoices cleaned up successfully')
        
except Exception as e:
    print(f'Error testing Invoice model: {e}')
    import traceback
    traceback.print_exc()
