# D:\school_fees_saas_v2\apps\announcements\models.py
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.utils.encoding import force_str
from django.conf import settings
from django.contrib.auth.models import Group
from django.core.exceptions import ValidationError

import logging
logger = logging.getLogger(__name__)

STAFF_USER_MODEL_STRING = getattr(settings, 'TENANT_STAFF_USER_MODEL', 'schools.StaffUser')
# Try to import for type hinting, but rely on the string for migrations
try:
    StaffUserClass = settings.AUTH_USER_MODEL # For platform admin announcements
    if STAFF_USER_MODEL_STRING != settings.AUTH_USER_MODEL: # If tenant staff is different
        from django.apps import apps
        app_label, model_name = STAFF_USER_MODEL_STRING.split('.')
        StaffUserClassTenant = apps.get_model(app_label, model_name)
    else:
        StaffUserClassTenant = None # Indicates same as public user
except Exception:
    StaffUserClass = None
    StaffUserClassTenant = None
    logger.warning("Could not dynamically load user models for type hinting in announcements.models.")


class Announcement(models.Model):
    TARGET_AUDIENCE_STAFF_CHOICES = [
        ('ALL_STAFF', _('All Staff in School')),
        ('SPECIFIC_STAFF_GROUPS', _('Specific Staff Groups/Roles')),
    ]
    TARGET_AUDIENCE_PARENT_CHOICES = [
        ('ALL_PARENTS', _('All Parents in School')),
        ('SPECIFIC_PARENT_GROUPS', _('Specific Parent Groups')), # If you implement parent groups
        ('PARENTS_OF_SPECIFIC_CLASSES', _('Parents of Specific Classes')), # If needed
    ]
    # For platform-wide announcements:
    TARGET_AUDIENCE_PLATFORM_CHOICES = [
        ('ALL_TENANT_ADMINS', _('All School Admins/Owners')),
        ('ALL_PLATFORM_USERS', _('All Platform Users (Rare)')),
    ]

    title = models.CharField(_("Title"), max_length=255)
    content = models.TextField(_("Content")) # Consider RichTextField if you install one

    # For tenant-specific announcements (is_global=False)
    tenant = models.ForeignKey(
        settings.TENANT_MODEL, # e.g., 'tenants.School'
        on_delete=models.CASCADE,
        null=True, # REQUIRED if is_global can be True
        blank=True,
        related_name='announcements',
        help_text=_("The specific school this announcement is for. Leave blank if platform-wide.")
    )
    # Author can be a platform admin (public User) or a tenant staff member
    author = models.ForeignKey(
        settings.AUTH_USER_MODEL, # Usually the public User model for platform-wide or if staff uses it
        on_delete=models.SET_NULL,
        null=True, 
        blank=True, # Allow system/unattributed announcements
        related_name='authored_announcements',
        verbose_name=_("Author")
    )

    # Targeting for Tenant-Specific Announcements
    target_all_tenant_staff = models.BooleanField(
        _("Target All Staff in this School"), 
        default=False, 
    )
    target_tenant_staff_groups = models.ManyToManyField(
        Group, 
        blank=True, 
        related_name='group_targeted_announcements',
        verbose_name=_("Target Specific Staff Groups in this School"),
        help_text=_("Select if not targeting all staff in this school.")
    )
    target_all_tenant_parents = models.BooleanField(
        _("Target All Parents in this School"), 
        default=False, 
    )
    # target_tenant_parent_classes = models.ManyToManyField('schools.SchoolClass', ...) # If targeting parents by class

    # Targeting for Platform-Wide (Global) Announcements (is_global=True, tenant=None)
    is_global = models.BooleanField(
        _("Platform-Wide Announcement"),
        default=False,
        help_text=_("Check if this announcement is for multiple/all schools or platform admins.")
    )
    target_global_audience_type = models.CharField(
        _("Global Target Audience"),
        max_length=50,
        choices=TARGET_AUDIENCE_PLATFORM_CHOICES,
        null=True, blank=True,
        help_text=_("Select audience if this is a platform-wide announcement.")
    )
    
    publish_date = models.DateTimeField(
        _("Publish Date"),
        default=timezone.now,
        db_index=True,
        help_text=_("Announcement becomes visible from this date/time.")
    )
    expiry_date = models.DateTimeField(
        _("Expiry Date"),
        null=True,
        blank=True,
        db_index=True,
        help_text=_("Optional: Announcement will be hidden after this date/time.")
    )
    
    is_published = models.BooleanField(
        _("Is Published"),
        default=True,
        db_index=True,
        help_text=_("Uncheck to keep as a draft. Only published announcements are shown.")
    )
    is_sticky = models.BooleanField(
        _("Is Sticky/Pinned"),
        default=False,
        db_index=True,
        help_text=_("Sticky announcements may appear more prominently or at the top.")
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-is_sticky', '-publish_date', '-created_at']
        verbose_name = _("Announcement") # Generic name
        verbose_name_plural = _("Announcements")

    def __str__(self):
        return self.title

    @property
    def is_currently_visible(self):
        now = timezone.now()
        if not self.is_published: return False
        if self.publish_date > now: return False
        if self.expiry_date and self.expiry_date < now: return False
        return True

    @classmethod
    def get_visible_announcements(cls):
        """Get all currently visible announcements"""
        now = timezone.now()
        return cls.objects.filter(
            is_published=True,
            publish_date__lte=now
        ).filter(
            models.Q(expiry_date__isnull=True) | models.Q(expiry_date__gt=now)
        ).order_by('-is_sticky', '-publish_date')

    @classmethod
    def get_staff_announcements(cls, limit=5):
        """Get announcements visible to staff members"""
        try:
            return cls.get_visible_announcements().filter(
                models.Q(target_all_tenant_staff=True) |
                models.Q(target_tenant_staff_groups__isnull=False)
            ).distinct()[:limit]
        except Exception as e:
            # Log the error and return empty queryset to prevent crashes
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error fetching staff announcements: {e}")
            return cls.objects.none()

    @classmethod
    def get_parent_announcements(cls, limit=5):
        """Get announcements visible to parents"""
        try:
            return cls.get_visible_announcements().filter(
                target_all_tenant_parents=True
            )[:limit]
        except Exception as e:
            # Log the error and return empty queryset to prevent crashes
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error fetching parent announcements: {e}")
            return cls.objects.none()

    def get_audience_display(self):
        from django.utils.encoding import force_str 
        audiences = []
        if self.is_global:
            if self.target_global_audience_type:
                audiences.append(self.get_target_global_audience_type_display())
            else: audiences.append(force_str(_("Platform-Wide (General)")))
        elif self.tenant:
            if self.target_all_tenant_staff:
                audiences.append(force_str(_("All Staff")))
            # Check if target_tenant_staff_groups exists on self (it's a manager)
            # and if self.pk exists (meaning it's saved) before calling .exists() on M2M
            elif hasattr(self, 'target_tenant_staff_groups') and self.pk and self.target_tenant_staff_groups.exists():
                groups = ", ".join([g.name for g in self.target_tenant_staff_groups.all().order_by('name')])
                audiences.append(f"{force_str(_('Staff Groups'))}: {groups}")
            if self.target_all_tenant_parents:
                if audiences and not self.target_all_tenant_staff : audiences.append(force_str(_("and")))
                audiences.append(force_str(_("All Parents")))
            if not audiences: audiences.append(force_str(_("General School Announcement")))
        else: audiences.append(force_str(_("Undefined Audience")))
        return " ".join(audiences) if audiences else force_str(_("General"))


    def clean(self):
        super().clean()
        # Validation for global vs. tenant
        if self.is_global and self.tenant:
            raise ValidationError(
                _("A platform-wide (global) announcement cannot be assigned to a specific school. Clear the 'Tenant' field or uncheck 'Is Platform-Wide'.")
            )
        if not self.is_global and not self.tenant:
            raise ValidationError(
                _("A non-global (school-specific) announcement must be assigned to a school. Select a 'Tenant' or check 'Is Platform-Wide'.")
            )
        if self.is_global and not self.target_global_audience_type:
            raise ValidationError(
                _("A platform-wide announcement must have a 'Global Target Audience' specified.")
            )

        # For tenant-specific audience validation (target_all_tenant_staff, target_tenant_staff_groups, target_all_tenant_parents)
        # This part is tricky in model's clean() for M2M fields on NEW instances because the instance isn't saved yet.
        # The ModelForm's clean method is a better place for M2M related validation if it depends on form input.
        # However, if you want to check the boolean flags here, that's fine.
        if not self.is_global and self.tenant:
            # We can check the boolean flags directly from self.
            # The M2M field self.target_tenant_staff_groups CANNOT be reliably queried with .exists() here if self.pk is None.
            # This validation should primarily be for ensuring at least one boolean target is set if no groups are selected yet.
            # The actual check for "if no groups AND no booleans" is better in the form's clean method.
            
            # For now, let's just log if no specific targets are set for a tenant announcement.
            # The form's clean method will handle ensuring that *if* groups are intended, they are valid.
            if not (self.target_all_tenant_staff or self.target_all_tenant_parents):
                # This doesn't check self.target_tenant_staff_groups effectively here for new instances.
                logger.info(
                    f"Announcement '{self.title}' for tenant '{self.tenant.name}' "
                    f"does not target 'All Staff' or 'All Parents'. "
                    f"Specific group targeting will be handled by the form."
                )
                
                

