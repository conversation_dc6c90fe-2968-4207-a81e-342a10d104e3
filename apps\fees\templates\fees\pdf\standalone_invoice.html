<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Invoice {{ invoice.invoice_number_display }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 10pt;
            margin: 20px;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .invoice-info {
            background-color: #f8f9fa;
            padding: 10px;
            margin-bottom: 20px;
        }
        .bill-to {
            background-color: #f8f9fa;
            padding: 15px;
            margin-bottom: 20px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .items-table th {
            background-color: #007bff;
            color: white;
            padding: 8px;
            border: 1px solid #007bff;
        }
        .items-table td {
            padding: 8px;
            border: 1px solid #ccc;
        }
        .summary-table {
            width: 50%;
            margin-left: 50%;
            border-collapse: collapse;
        }
        .summary-table td {
            padding: 8px;
            border: 1px solid #ccc;
        }
        .text-right {
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>INVOICE</h1>
        <h2>{{ school_profile.school_name_on_reports|default:tenant.name }}</h2>
    </div>

    <div class="invoice-info">
        <p><strong>Invoice Number:</strong> {{ invoice.invoice_number_display }}</p>
        <p><strong>Status:</strong> {{ invoice.get_status_display }}</p>
        <p><strong>Issue Date:</strong> {{ invoice.issue_date|date:"F d, Y" }}</p>
        {% if invoice.due_date %}<p><strong>Due Date:</strong> {{ invoice.due_date|date:"F d, Y" }}</p>{% endif %}
    </div>

    <div class="bill-to">
        <h3>Bill To</h3>
        {% if invoice.student %}
            <p><strong>{{ invoice.student.get_full_name }}</strong></p>
            {% if invoice.student.admission_number %}
                <p>Admission No: {{ invoice.student.admission_number }}</p>
            {% endif %}
            {% if invoice.student.current_class %}
                <p>Class: {{ invoice.student.current_class.name }}{% if invoice.student.current_section %} - {{ invoice.student.current_section.name }}{% endif %}</p>
            {% endif %}
        {% endif %}
    </div>

    <h3>Invoice Items</h3>
    <table class="items-table">
        <thead>
            <tr>
                <th>Description</th>
                <th>Quantity</th>
                <th class="text-right">Unit Price</th>
                <th class="text-right">Total</th>
            </tr>
        </thead>
        <tbody>
            {% for item in charge_items %}
            <tr>
                <td>
                    {% if item.fee_head %}
                        {{ item.fee_head.name }}
                    {% else %}
                        {{ item.description }}
                    {% endif %}
                </td>
                <td>{{ item.quantity|default:"1" }}</td>
                <td class="text-right">${{ item.unit_price|floatformat:2 }}</td>
                <td class="text-right">${{ item.amount|floatformat:2 }}</td>
            </tr>
            {% endfor %}
            
            {% for item in concession_lines %}
            <tr style="background-color: #f0f9ff;">
                <td>
                    {% if item.concession_type %}
                        {{ item.concession_type.name }} (Discount)
                    {% else %}
                        {{ item.description }} (Discount)
                    {% endif %}
                </td>
                <td>-</td>
                <td class="text-right"></td>
                <td class="text-right">(${{ item.amount|abs|floatformat:2 }})</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <table class="summary-table">
        <tr>
            <td><strong>Subtotal:</strong></td>
            <td class="text-right">${{ display_subtotal|floatformat:2 }}</td>
        </tr>
        {% if display_total_concessions > 0 %}
        <tr>
            <td><strong>Total Discounts:</strong></td>
            <td class="text-right">-${{ display_total_concessions|floatformat:2 }}</td>
        </tr>
        {% endif %}
        {% if display_amount_paid > 0 %}
        <tr>
            <td><strong>Amount Paid:</strong></td>
            <td class="text-right">-${{ display_amount_paid|floatformat:2 }}</td>
        </tr>
        {% endif %}
        <tr style="background-color: #28a745; color: white;">
            <td><strong>BALANCE DUE:</strong></td>
            <td class="text-right"><strong>${{ display_balance_due|floatformat:2 }}</strong></td>
        </tr>
    </table>

    {% if invoice.notes_to_parent %}
    <div style="margin-top: 30px; padding: 15px; background-color: #f8f9fa;">
        <h4>Notes</h4>
        <p>{{ invoice.notes_to_parent }}</p>
    </div>
    {% endif %}

    <div style="margin-top: 30px; text-align: center; font-size: 8pt; color: #666;">
        <p>Thank you for your business!</p>
        {% if invoice.due_date %}
        <p><strong>Payment due by {{ invoice.due_date|date:"F d, Y" }}</strong></p>
        {% endif %}
    </div>
</body>
</html>
