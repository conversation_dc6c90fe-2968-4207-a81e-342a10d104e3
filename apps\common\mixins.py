# D:\school_fees_saas_V2\apps\common\mixins.py

from django import forms
from django.urls import reverse_lazy # Ensure this is imported if used
from django.conf import settings # For LOGIN_URL, TENANT_LOGIN_URL etc.
from django.shortcuts import redirect, reverse # reverse for get_login_url in ParentLoginRequiredMixin
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ImproperlyConfigured
from django.contrib.auth.mixins import AccessMixin # Base for your auth mixins

from django.utils import timezone


from django.views.generic.list import MultipleObjectMixin 

import logging
logger = logging.getLogger(__name__)



# Import your ParentUser model - CRITICAL: ADJUST THIS PATH
# Assuming ParentUser is in apps.users.models or apps.students.models
# Based on your logs, it seems to be in apps.students.models
try:
    from apps.students.models import ParentUser
except ImportError:
    # Fallback or raise an error if ParentUser is essential for these mixins
    # This helps in startup if apps are not fully loaded yet for checks but might hide issues.
    # A better approach is to ensure apps are correctly configured in settings.py
    ParentUser = None 
    # Consider logging a warning here during app loading if ParentUser is None

# Import your feature checking function - CRITICAL: ADJUST THIS PATH
try:
    from apps.common.features import tenant_has_feature_for_parent_portal
    # If you use the generic tenant_has_feature directly with PARENT_PORTAL_ACCESS_CODE:
    # from apps.common.features import tenant_has_feature, PARENT_PORTAL_ACCESS_CODE
except ImportError:
    # Fallback if the import fails during initial loading or checks
    def tenant_has_feature_for_parent_portal(tenant): return False # Default to False
    # def tenant_has_feature(tenant, code): return False
    # PARENT_PORTAL_ACCESS_CODE = "PARENT_PORTAL_ACCESS" # Define a fallback
    # Consider logging a warning here

import logging # Import logging
logger = logging.getLogger(__name__)


# --- Your Existing Mixins ---
class TenantMixinBase:
    # ... (your existing TenantMixinBase code - no changes needed here) ...
    login_url = None
    permission_denied_message = _("You do not have permission to access this page.") # Use _ for translation
    raise_exception = False

    def get_tenant_login_url(self):
        # TENANT_LOGIN_URL should be defined in settings.py
        # Example: TENANT_LOGIN_URL = 'schools:staff_login'
        # Using reverse_lazy ensures URLs are resolved when needed, not at import time
        return str(reverse_lazy(settings.TENANT_LOGIN_URL))

    def get_public_login_url(self):
        return str(settings.LOGIN_URL)


class TenantLoginRequiredMixin(TenantMixinBase, AccessMixin):
    # ... (your existing TenantLoginRequiredMixin code - no changes needed here) ...
    def get_login_url(self):
        if hasattr(self.request, 'tenant') and self.request.tenant:
            # Using MY_PUBLIC_SCHEMA_NAME from settings for robustness
            public_schema_name = getattr(settings, 'MY_PUBLIC_SCHEMA_NAME', 'public')
            if self.request.tenant.schema_name != public_schema_name:
                return self.get_tenant_login_url()
        return self.get_public_login_url()

    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        return super().dispatch(request, *args, **kwargs)


class TenantPermissionRequiredMixin(TenantMixinBase, AccessMixin):
    # ... (your existing TenantPermissionRequiredMixin code - slight adjustment for redirect) ...
    permission_required = None

    def get_permission_required(self):
        if self.permission_required is None:
            raise ImproperlyConfigured(
                f"{self.__class__.__name__} is missing the permission_required attribute. "
                f"Define {self.__class__.__name__}.permission_required, or override "
                f"{self.__class__.__name__}.get_permission_required()."
            )
        if isinstance(self.permission_required, str):
            perms = (self.permission_required,)
        else:
            perms = self.permission_required
        return perms

    def has_permission(self):
        perms = self.get_permission_required()
        user_to_check = getattr(self.request, 'effective_tenant_user', self.request.user)
        # Ensure user_to_check is not None and has has_perms method
        if user_to_check and hasattr(user_to_check, 'has_perms'):
            return user_to_check.has_perms(perms)
        return False


    def get_login_url(self):
        if hasattr(self.request, 'tenant') and self.request.tenant:
            public_schema_name = getattr(settings, 'MY_PUBLIC_SCHEMA_NAME', 'public')
            if self.request.tenant.schema_name != public_schema_name:
                return self.get_tenant_login_url()
        return self.get_public_login_url()
        
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        if not self.has_permission():
            messages.error(request, self.permission_denied_message)
            # Determine appropriate redirect based on context
            if hasattr(request, 'tenant') and request.tenant:
                public_schema_name = getattr(settings, 'MY_PUBLIC_SCHEMA_NAME', 'public')
                if request.tenant.schema_name != public_schema_name:
                    # Tenant context, redirect to tenant dashboard or a tenant-specific access denied page
                    # Ensure 'schools:dashboard' or your tenant dashboard URL is correct
                    try:
                        # Attempt to redirect to a specific tenant dashboard if user is a staff
                        # or a parent dashboard if user is a parent
                        if hasattr(request.user, 'is_staff_member') and request.user.is_staff_member: # hypothetical attribute
                            return redirect(reverse_lazy(settings.TENANT_DASHBOARD_URL)) # e.g. 'schools:dashboard'
                        elif isinstance(request.user, ParentUser) and ParentUser is not None:
                            return redirect(reverse_lazy(settings.PARENT_DASHBOARD_URL)) # e.g. 'parent_portal:dashboard'
                        else: # Fallback tenant redirect
                            return redirect(reverse_lazy(settings.TENANT_DASHBOARD_URL))

                    except AttributeError: # If settings vars not defined, fallback
                        return redirect(reverse_lazy('public_site:home')) # Fallback further
                else:
                    # Public context, redirect to public home or a public access denied page
                    return redirect(reverse_lazy('public_site:home'))
            else:
                # No tenant context, likely public site
                return redirect(reverse_lazy('public_site:home'))
        return super().dispatch(request, *args, **kwargs)


# --- NEW ParentLoginRequiredMixin ---
class ParentLoginRequiredMixin(AccessMixin): # Inherits from AccessMixin directly
    """
    Verify that the current user is authenticated, is an instance of ParentUser,
    and that the Parent Portal feature is enabled for the current tenant.
    """
    login_url_name = 'parent_portal:login' # URL name to redirect to for login
    permission_denied_redirect_url_name = 'public_site:home' # Fallback redirect if feature denied

    def dispatch(self, request, *args, **kwargs):
        # 1. Check if user is authenticated
        if not request.user.is_authenticated:
            logger.debug(f"ParentLoginRequiredMixin: User not authenticated. Redirecting to login: {self.get_login_url()}")
            return self.handle_no_permission() # Redirects to get_login_url()

        # 2. Check if the authenticated user is a ParentUser
        # Ensure ParentUser is not None (meaning it was imported correctly)
        if ParentUser is None:
            logger.critical("ParentLoginRequiredMixin: ParentUser model is None (import failed). Denying access.")
            messages.error(request, _("Parent portal system error. Please try again later."))
            return redirect(reverse_lazy(self.permission_denied_redirect_url_name))

        if not isinstance(request.user, ParentUser):
            logger.info(f"ParentLoginRequiredMixin: User '{request.user}' is not a ParentUser (type: {type(request.user)}). Redirecting from parent portal area.")
            # Log them out from current session and redirect to parent login,
            # as they might be a staff or admin on a parent URL.
            from django.contrib.auth import logout
            logout(request)
            messages.info(request, _("Please log in with your parent credentials to access this area."))
            return redirect(self.get_login_url())

        # 3. Check for tenant context
        tenant = getattr(request, 'tenant', None)
        if not tenant:
            logger.error("ParentLoginRequiredMixin: Tenant context not found on request. Denying access to parent portal area.")
            messages.error(request, _("School context not found. Unable to access parent portal."))
            return redirect(reverse_lazy(self.permission_denied_redirect_url_name)) # Redirect to a safe public page

        # 4. Check if the Parent Portal feature is enabled for this tenant
        # This now uses the specific function from apps.common.features
        if not tenant_has_feature_for_parent_portal(tenant):
            logger.warning(f"ParentLoginRequiredMixin: PARENT_PORTAL feature disabled for tenant '{tenant.name}'. Access denied for parent '{request.user.email}'.")
            messages.error(request, _("The Parent Portal is not currently enabled for this school. Please contact school administration."))
            return redirect(reverse_lazy(self.permission_denied_redirect_url_name)) # Or a specific "feature_disabled" page for this tenant
        
        logger.debug(f"ParentLoginRequiredMixin: Access GRANTED for ParentUser '{request.user.email}' to tenant '{tenant.name}' parent portal area.")
        return super().dispatch(request, *args, **kwargs)

    def get_login_url(self):
        """
        Returns the login URL for parents.
        """
        return str(reverse(self.login_url_name)) # Use reverse here

    def handle_no_permission(self):
        """
        Handles the case where the user does not have permission.
        For unauthenticated users, it redirects to the login page.
        """
        messages.info(self.request, _("Please log in to access the parent portal.")) # More specific message
        return redirect(self.get_login_url())



# D:\school_fees_saas_v2\apps\common\mixins.py
from django import forms
from django.urls import reverse_lazy
from django.conf import settings
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.mixins import AccessMixin # For TenantPermissionRequiredMixin etc.
# For BaseReportViewMixin to interact with ListView if needed
from django.views.generic.list import MultipleObjectMixin 
# Import SchoolProfile if get_school_profile is used
# from apps.schools.models import SchoolProfile 

import logging
logger = logging.getLogger(__name__)


# D:\school_fees_saas_v2\apps\common\mixins.py

# import logging
# from django.utils import timezone
# from django.utils.translation import gettext_lazy as _
# from django.http import HttpResponseRedirect
# from django.urls import reverse
# from django.contrib import messages

# logger = logging.getLogger(__name__)

# class BaseReportViewMixin:
#     report_title_text = _("Report")
#     report_code = None
#     filter_form_class = None
    
#     form = None # Will store the instantiated filter form
#     # report_data = None # Let get_context_data set this based on get_report_data output

#     def get_report_title(self):
#         return str(self.report_title_text)

#     def get_filter_form_initial_data(self):
#         return {}

#     def get_filter_form_kwargs(self):
#         kwargs = {'request': self.request}
#         if hasattr(self.request, 'tenant') and self.request.tenant:
#             kwargs['tenant'] = self.request.tenant
        
#         initial_data = self.get_filter_form_initial_data()
#         if initial_data:
#             kwargs['initial'] = initial_data
#         return kwargs

#     # This method MUST be implemented by the child report view AND IT EXPECTS THE FORM
#     def get_report_data(self, processed_filter_form): # <<<< REVERTED SIGNATURE
#         raise NotImplementedError(
#             f"{self.__class__.__name__} must implement get_report_data(self, processed_filter_form)."
#         )

#     def get_common_report_context(self):
#         context = {}
#         if hasattr(self.request, 'tenant') and self.request.tenant:
#             context['report_tenant_name'] = self.request.tenant.name
#         return context
    
    
#     def get(self, request, *args, **kwargs):
#         # 1. Instantiate and process the filter form
#         if self.filter_form_class:
#             form_kwargs = self.get_filter_form_kwargs()
#             self.form = self.filter_form_class(request.GET or None, **form_kwargs)
            
#             # We validate here to decide if exports are allowed and for logging
#             if self.form.is_valid():
#                 logger.debug(f"{self.__class__.__name__}.get(): Filter form is valid. Cleaned data: {self.form.cleaned_data}")
#                 self.report_data = self.get_report_data() # CALLS CHILD, child uses self.form
#             elif not self.form.is_bound and self.form.initial: # Unbound form with initial data
#                 logger.debug(f"{self.__class__.__name__}.get(): Filter form unbound with initial data: {self.form.initial}")
#                 self.report_data = self.get_report_data() # CALLS CHILD, child uses self.form (with initial)
#             else: # Form is bound but invalid
#                 logger.warning(f"{self.__class__.__name__}.get(): Filter form is bound but invalid. Errors: {self.form.errors}")
#                 # self.report_data remains {} or child's get_report_data handles invalid form
        
#         else: # No filter form defined for this report
#             self.form = None
#             logger.debug(f"{self.__class__.__name__}.get(): No filter_form_class defined.")
        
#         # 2. Handle export requests
#         export_type = request.GET.get('export')
#         if export_type:
#             if self.form and self.form.is_bound and not self.form.is_valid(): # Can't export with invalid filters
#                 messages.warning(request, _("Invalid filters. Please correct them before exporting."))
#                 query_params = request.GET.copy(); query_params.pop('export', None)
#                 return HttpResponseRedirect(f"{request.path}?{query_params.urlencode()}")

#             queryset_for_export = self.get_export_queryset() # Child implements this, uses self.form

#             if export_type == 'csv': return self.export_to_csv(queryset_for_export, request)
#             if export_type == 'excel': return self.export_to_excel(queryset_for_export, request)
#             if export_type == 'pdf': 
#                 pdf_context = self.get_context_data_for_pdf(queryset_for_export)
#                 return self.export_to_pdf(queryset_for_export, request, pdf_context)
        
#         # 3. If not exporting, proceed with standard CBV behavior (e.g., ListView's get)
#         # This will call self.get_queryset() (for ListView) and then self.get_context_data()
#         return super().get(request, *args, **kwargs) 
    
    
#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
        
#         # self.form should be instantiated by the get() method before this is called
#         context['filter_form'] = self.form 

#         context['view_title'] = self.get_report_title()
#         context.update(self.get_common_report_context())
#         context['report_code'] = getattr(self, 'report_code', self.__class__.__name__.lower().replace("view", ""))
        
#         # Call child's get_report_data with self.form
#         # self.form here would have been processed (bound & validated or unbound with initials) in get()
#         report_specific_data = self.get_report_data(self.form) # <<<< PASSING self.form
        
#         if report_specific_data: # Check if it returned anything
#             context.update(report_specific_data)

#         if self.request.GET:
#             query_params = self.request.GET.copy()
#             if 'page' in query_params: del query_params['page']
#             context['filter_params'] = query_params.urlencode()
#         else:
#             context['filter_params'] = ''
        
#         context.setdefault('report_generated_at', timezone.now())
#         logger.debug(f"{self.__class__.__name__}: Final context. Keys: {list(context.keys())}")
#         return context

#     # --- Export method stubs ---
#     def get_export_queryset(self):
#         raise NotImplementedError(f"{self.__class__.__name__} must implement get_export_queryset(self).")

#     def export_to_csv(self, queryset, request): 
#         raise NotImplementedError(f"{self.__class__.__name__} must implement export_to_csv.")
    
#     def export_to_excel(self, queryset, request): 
#         raise NotImplementedError(f"{self.__class__.__name__} must implement export_to_excel.")

#     def get_pdf_template_name(self):
#         if not self.report_code:
#             raise ImproperlyConfigured("ReportView requires a 'report_code' for default PDF template.")
#         return f"reporting/pdf/{self.report_code}_pdf.html"

#     def get_context_data_for_pdf(self, queryset): 
#         # This base implementation can be used by children or overridden
#         context = {
#             'report_items': queryset, 
#             'report_title': self.get_report_title(),
#             'report_generated_at': timezone.now(),
#             'filter_form_data': self.form.cleaned_data if self.form and self.form.is_valid() else {},
#             'request': self.request,
#             'school_profile': getattr(self.request, 'school_profile_for_reports', None),
#         }
#         # If child's get_report_data populates self.report_data with summaries:
#         # (This assumes get_report_data was called in get() if form was valid)
#         # It's safer for child's get_context_data_for_pdf to call its own get_report_data
#         # if it needs fresh summary data based on the export queryset.
#         # For now, let's assume summary data isn't automatically passed here unless child does it.
#         return context
        
#     def export_to_pdf(self, queryset, request, pdf_context): 
#         # from apps.common.utils import render_to_pdf # Ensure this is imported where used
#         raise NotImplementedError(f"{self.__class__.__name__} must implement export_to_pdf or ensure render_to_pdf works.")
    

# D:\school_fees_saas_v2\apps\common\mixins.py

import logging
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.http import HttpResponseRedirect
from django.urls import reverse # For redirect
from django.contrib import messages
# from django.core.exceptions import ImproperlyConfigured # If needed

logger = logging.getLogger(__name__)

class BaseReportViewMixin:
    report_title_text = _("Report")
    report_code = None
    filter_form_class = None
    
    form = None 
    report_data = None # This will be populated by self.get_report_data() in the get() method

    def get_report_title(self):
        return str(self.report_title_text)

    def get_filter_form_initial_data(self):
        return {}

    def get_filter_form_kwargs(self):
        kwargs = {'request': self.request}
        if hasattr(self.request, 'tenant') and self.request.tenant:
            kwargs['tenant'] = self.request.tenant
        initial_data = self.get_filter_form_initial_data()
        if initial_data:
            kwargs['initial'] = initial_data
        return kwargs

    def get_report_data(self): # SIGNATURE: takes only self
        """
        Child view implements this. Uses self.form (set in get()).
        Returns a dictionary of report-specific data.
        """
        raise NotImplementedError(
            f"{self.__class__.__name__} must implement get_report_data(self). "
            f"Access the processed filter form via self.form."
        )

    def get_common_report_context(self):
        context = {}
        if hasattr(self.request, 'tenant') and self.request.tenant:
            context['report_tenant_name'] = self.request.tenant.name
        return context
    
    def get(self, request, *args, **kwargs): # <<< PRIMARY ORCHESTRATION
        self.report_data = {} # Initialize/reset report_data for this request

        if self.filter_form_class:
            form_kwargs = self.get_filter_form_kwargs()
            self.form = self.filter_form_class(request.GET or None, **form_kwargs)
            
            if self.form.is_valid():
                logger.debug(f"{self.__class__.__name__}.get(): Filter form is valid. Cleaned data: {self.form.cleaned_data}")
                self.report_data = self.get_report_data() # CALLS CHILD'S get_report_data
            elif not self.form.is_bound and self.form.initial: 
                logger.debug(f"{self.__class__.__name__}.get(): Filter form unbound with initial data: {self.form.initial}")
                self.report_data = self.get_report_data() # CALLS CHILD'S get_report_data
            else: # Form is bound but invalid
                logger.warning(f"{self.__class__.__name__}.get(): Filter form is bound but invalid. Errors: {self.form.errors}")
                # self.report_data remains {} or child's get_report_data handles invalid form gracefully
                # (e.g., by returning empty structures if self.form is invalid)
                # For safety, ensure get_report_data is called to allow child to provide default empty state
                self.report_data = self.get_report_data() 
        else: 
            self.form = None
            self.report_data = self.get_report_data() # CALLS CHILD'S get_report_data (self.form will be None)
        
        export_type = request.GET.get('export')
        if export_type:
            if self.form and self.form.is_bound and not self.form.is_valid():
                messages.warning(request, _("Invalid filters. Please correct them before exporting."))
                query_params = request.GET.copy(); query_params.pop('export', None)
                return HttpResponseRedirect(f"{request.path}?{query_params.urlencode()}")
            
            queryset_for_export = self.get_export_queryset()
            if export_type == 'csv': return self.export_to_csv(queryset_for_export, request)
            if export_type == 'excel': return self.export_to_excel(queryset_for_export, request)
            if export_type == 'pdf': 
                pdf_context = self.get_context_data_for_pdf(queryset_for_export)
                return self.export_to_pdf(queryset_for_export, request, pdf_context)
        
        return super().get(request, *args, **kwargs) # Calls ListView/TemplateView get

    def get_context_data(self, **kwargs): # Called by ListView.get() or TemplateView.get()
        context = super().get_context_data(**kwargs)
        
        context['filter_form'] = self.form 
        context['report_title'] = self.get_report_title()
        context.update(self.get_common_report_context())
        context['report_code'] = getattr(self, 'report_code', self.__class__.__name__.lower().replace("view", ""))
        
        # self.report_data was ALREADY SET in the get() method
        if self.report_data is not None: 
            context.update(self.report_data) # Merge the pre-calculated report data
        else:
            logger.warning(f"{self.__class__.__name__}.get_context_data: self.report_data is None. Check get() method logic.")

        if self.request.GET:
            query_params = self.request.GET.copy()
            if 'page' in query_params: del query_params['page']
            context['filter_params'] = query_params.urlencode()
        else:
            context['filter_params'] = ''
        context.setdefault('report_generated_at', timezone.now())
        logger.debug(f"{self.__class__.__name__}.get_context_data: Final context. Keys: {list(context.keys())}")
        return context

    # ... (export method stubs: get_export_queryset, export_to_csv, export_to_excel, get_pdf_template_name, get_context_data_for_pdf, export_to_pdf) ...
    # Ensure these stubs are also consistent if they call get_report_data, or better, use self.report_data if it's already populated.
    # For example, get_context_data_for_pdf should use self.report_data if it contains summaries.
    def get_export_queryset(self):
        raise NotImplementedError(...)
    def export_to_csv(self, queryset, request): raise NotImplementedError(...)
    def export_to_excel(self, queryset, request): raise NotImplementedError(...)
    def get_context_data_for_pdf(self, queryset):
        context = {
            'report_items': queryset, 
            'report_title': self.get_report_title(),
            'report_generated_at': timezone.now(),
            'filter_form_data': self.form.cleaned_data if self.form and self.form.is_valid() else {},
            'request': self.request,
            'school_profile': getattr(self.request, 'school_profile_for_reports', None),
        }
        if hasattr(self, 'report_data') and self.report_data: # Use already computed summary
            context.update(self.report_data) 
        return context
    def export_to_pdf(self, queryset, request, pdf_context): raise NotImplementedError(...)

# import logging
# from django.utils import timezone
# from django.utils.translation import gettext_lazy as _
# from django.http import HttpResponseRedirect # For redirecting after failed export attempt
# from django.urls import reverse # For redirecting after failed export attempt
# from django.contrib import messages # For messages

# logger = logging.getLogger(__name__)

# class BaseReportViewMixin:
#     # Attributes to be set by child views
#     report_title_text = _("Report")  # Default static title
#     report_code = None               # Unique code for the report
#     filter_form_class = None         # Child view provides a Django Form class
    
#     # Internal attributes, set during request processing
#     form = None                      # Instance of the filter_form_class (renamed from processed_filter_form for consistency)
#     report_data = None               # Data returned by child's get_report_data

#     def get_report_title(self):
#         """Returns the title for the report. Child views can override for dynamic titles."""
#         # Child views can override this or simply set self.report_title_text
#         return str(self.report_title_text) # Ensure it's a string (for _lazy objects)

#     def get_filter_form_initial_data(self):
#         """Provides initial data dictionary for the filter form. Override in child views if needed."""
#         return {}

#     def get_filter_form_kwargs(self):
#         """Prepares kwargs for instantiating the filter form."""
#         kwargs = {
#             'request': self.request, # Pass request for potential use in form __init__
#             'initial': self.get_filter_form_initial_data() # Get initial data
#         }
#         if hasattr(self.request, 'tenant') and self.request.tenant:
#             kwargs['tenant'] = self.request.tenant
        
#         # Remove 'initial' if it's empty, as some forms might not expect an empty 'initial' dict
#         if not kwargs['initial']:
#             kwargs.pop('initial')
            
#         return kwargs

#     def get_report_data(self):
#         """
#         This method MUST be implemented by the child report view.
#         It should use self.form (the processed filter form instance) to fetch/calculate
#         and return a dictionary of data specific to the report.
#         Example: {'summary_totals': ..., 'detailed_items': ...}
#         """
#         raise NotImplementedError(
#             f"{self.__class__.__name__} must implement get_report_data(self). "
#             f"Access the filter form via self.form."
#         )

#     def get_common_report_context(self): # Removed **kwargs as it's not using them
#         """
#         Provides common context variables that might be shared across many reports,
#         like school profile or tenant name for report headers.
#         """
#         context = {}
#         if hasattr(self.request, 'tenant') and self.request.tenant:
#             # Example: making tenant name available for report headers
#             context['report_tenant_name'] = self.request.tenant.name
#             # If you have a standard way to get school profile for reports:
#             # if hasattr(self.request, 'school_profile_for_reports'):
#             #     context['school_profile_for_reports'] = self.request.school_profile_for_reports
#         return context
    
#     def get_context_data(self, **kwargs):
#         """
#         Standard get_context_data, extended by this mixin.
#         ListView calls this with object_list, paginator, page_obj etc. in kwargs.
#         TemplateView calls this with just kwargs.
#         """
#         context = super().get_context_data(**kwargs) # From TemplateView, ListView, etc.
        
#         # 1. Filter form (should have been instantiated in get() or by ListView's machinery)
#         context['filter_form'] = self.form # self.form is set in get()

#         # 2. Common report title elements
#         context['view_title'] = self.get_report_title()
#         context['report_title_prefix'] = context.get('report_tenant_name', '') # From get_common_report_context
#         context.update(self.get_common_report_context()) # Add other common context
#         context['report_code'] = getattr(self, 'report_code', self.__class__.__name__.lower())
        
#         # 3. Report-specific data
#         # self.report_data should have been populated by get() method after form validation
#         if self.report_data is not None:
#             context.update(self.report_data)
#         else:
#             # This might happen if get_report_data wasn't called or returned None
#             # (e.g. form invalid in get() before calling get_report_data)
#             logger.warning(f"{self.__class__.__name__}: self.report_data was not set. Child's get_report_data might not have been called or returned None.")

#         # 4. Filter params for pagination/export links
#         if self.request.GET:
#             query_params = self.request.GET.copy()
#             if 'page' in query_params: del query_params['page']
#             context['filter_params'] = query_params.urlencode()
#         else:
#             context['filter_params'] = ''
        
#         context.setdefault('report_generated_at', timezone.now())

#         logger.debug(f"{self.__class__.__name__}: Final context for template. Keys: {list(context.keys())}")
#         return context

#     def get(self, request, *args, **kwargs):
#         """
#         Handles GET requests, instantiates filter form, processes report data, and handles exports.
#         This method will be called by both ListView and TemplateView.
#         """
#         form_kwargs = self.get_filter_form_kwargs() # Gets initial, request, tenant
        
#         if self.filter_form_class:
#             # Bind GET data if present, otherwise form is unbound but with initials
#             self.form = self.filter_form_class(request.GET or None, **form_kwargs)
            
#             # It's generally better for get_report_data to expect a validated form or handle unvalidated.
#             # For exports, we definitely want validated data. For display, sometimes unvalidated is shown.
#             if self.form.is_valid():
#                 logger.debug(f"{self.__class__.__name__}: Filter form is valid. Cleaned data: {self.form.cleaned_data}")
#                 self.report_data = self.get_report_data() # Call child's implementation
#             else:
#                 logger.debug(f"{self.__class__.__name__}: Filter form is not bound or not valid. Errors: {self.form.errors}")
#                 # Provide empty or default report data if form is not valid
#                 # Child's get_report_data() might still be called with an invalid form,
#                 # so it needs to handle that gracefully or self.report_data remains None.
#                 self.report_data = {} # Or some default structure expected by template
#         else:
#             self.form = None
#             self.report_data = self.get_report_data() # Call child's implementation (form will be None)
#             logger.debug(f"{self.__class__.__name__}: No filter_form_class defined. Calling get_report_data without form.")
        
#         export_type = request.GET.get('export')
#         if export_type:
#             if not self.form or not self.form.is_valid():
#                 messages.warning(request, _("Please apply valid filters to generate the report before exporting."))
#                 query_params = request.GET.copy()
#                 query_params.pop('export', None) # Remove export param to avoid loop
#                 # Redirect to the current path with remaining GET params
#                 return HttpResponseRedirect(f"{request.path}?{query_params.urlencode()}")

#             # self.report_data should be populated from the valid form case above
#             # The export methods now primarily need the full filtered queryset
#             # which child views will typically provide within their get_report_data() or a helper.
            
#             # For ListView based reports, self.object_list is the paginated list.
#             # For exports, we usually need the *unpaginated* filtered list.
#             # The child view's get_report_data should ideally return a dictionary
#             # that includes this full_queryset if export methods need it directly.
#             # Or export methods call a helper like get_export_queryset().

#             queryset_for_export = self.get_export_queryset() # Child view must implement this

#             if export_type == 'csv': 
#                 return self.export_to_csv(queryset_for_export, request)
#             if export_type == 'excel': 
#                 return self.export_to_excel(queryset_for_export, request)
#             if export_type == 'pdf': 
#                 # PDF context might need more than just the queryset
#                 pdf_context = self.get_context_data_for_pdf(queryset_for_export)
#                 return self.export_to_pdf(queryset_for_export, request, pdf_context)

#         # If not exporting, let the CBV (ListView, TemplateView) continue its normal get process
#         # This will call self.get_context_data() which now has access to self.form and self.report_data
#         return super().get(request, *args, **kwargs) 

#     # --- Export method stubs ---
#     # Child views MUST implement these if they offer these export options.
#     # They should operate on a queryset (typically the full filtered queryset).

#     def get_export_queryset(self):
#         """Child views should override this to return the full, filtered queryset for export."""
#         logger.warning(f"{self.__class__.__name__} did not override get_export_queryset. Export might be empty or use default ListView queryset.")
#         # For ListView children, self.get_queryset() is already filtered.
#         # But it might be called again if get() didn't store its result globally on self.
#         # A robust approach is for child's get_queryset to store the full filtered qs on self.
#         if hasattr(self, 'object_list') and hasattr(self.object_list, 'all'): # If it's a queryset from ListView
#             return self.object_list # This is paginated if get() already ran fully. Careful.
#         return self.model.objects.none() if hasattr(self, 'model') and self.model else None


#     def export_to_csv(self, queryset, request): 
#         raise NotImplementedError(f"{self.__class__.__name__} must implement export_to_csv.")
    
#     def export_to_excel(self, queryset, request): 
#         raise NotImplementedError(f"{self.__class__.__name__} must implement export_to_excel.")

#     def get_context_data_for_pdf(self, queryset): 
#         """Child views should override to provide full context for PDF template."""
#         # This base implementation provides the queryset and some common items.
#         # It assumes self.form is available and processed.
#         return {
#             'report_items': queryset, 
#             'report_title': self.get_report_title(),
#             'report_generated_at': timezone.now(),
#             'filter_form_data': self.form.cleaned_data if self.form and self.form.is_valid() else {},
#             'request': self.request, # For domain/tenant info in PDF template
#             # Add school_profile if available and needed
#             'school_profile': getattr(self.request, 'school_profile_for_reports', None),
#             # Add summary data if available on self (populated by get_report_data)
#             'report_summary': getattr(self.report_data, 'get', lambda k,d: d)('summary',{}) # Example
#         }
        
#     def export_to_pdf(self, queryset, request, pdf_context): 
#         raise NotImplementedError(f"{self.__class__.__name__} must implement export_to_pdf or get_pdf_template_name.")

#     # Optional: a helper for PDF template name
#     # def get_pdf_template_name(self):
#     #     return f"reporting/pdf/{self.report_code}_pdf.html" # Default convention
    
    
    

# class BaseReportViewMixin:
#     # Attributes to be set by child views
#     report_title_text = _("Report") # Default static title
#     report_code = None
#     filter_form_class = None # Child view provides a Django Form class
    
#     # Internal attribute
#     processed_filter_form = None

#     def get_report_title(self):
#         """Returns the title for the report. Child views can override for dynamic titles."""
#         # Example dynamic title based on processed form (if form is set and valid)
#         # form = getattr(self, 'processed_filter_form', None)
#         # if form and form.is_valid():
#         #     # Construct title from form.cleaned_data
#         #     pass
#         return self.report_title_text # Fallback to class attribute

#     def get_filter_form_initial_data(self):
#         """Provides initial data for the filter form."""
#         return {}

#     def get_filter_form_kwargs(self):
#         kwargs = {'request': self.request} # Always pass request
#         if hasattr(self.request, 'tenant') and self.request.tenant:
#             kwargs['tenant'] = self.request.tenant
        
#         # If there's a method to get specific initial data, call it here
#         if hasattr(self, 'get_filter_form_initial_data'):
#             initial_data = self.get_filter_form_initial_data()
#             if initial_data: # Only add 'initial' key if there's data
#                 kwargs['initial'] = initial_data
#         return kwargs
    
#     # def get_filter_form_kwargs(self):
#     #     """Provides kwargs for instantiating the filter form."""
#     #     kwargs = {'request': self.request}
#     #     if hasattr(self.request, 'tenant'):
#     #         kwargs['tenant'] = self.request.tenant
#     #     return kwargs

#     # This method MUST be implemented by the child report view
#     def get_report_data(self, processed_filter_form):
#         raise NotImplementedError(
#             f"{self.__class__.__name__} must implement get_report_data(self, processed_filter_form)"
#         )

#     def get_common_report_context(self, **kwargs): # THIS METHOD MUST BE HERE
#         context = {}
#         if hasattr(self.request, 'tenant') and self.request.tenant:
#             context['report_title_prefix'] = self.request.tenant.name
#         # Example: Add school_profile if consistently needed
#         # if hasattr(self, 'get_school_profile'):
#         #    context['school_profile'] = self.get_school_profile()
#         return context

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs) # From TemplateView, ListView, etc.
        
#         # 1. Common report title elements
#         context['view_title'] = self.get_report_title() # Calls child's or uses default
#         context.update(self.get_common_report_context(**kwargs)) # ADDS 'report_title_prefix' etc.
#         context['report_code'] = getattr(self, 'report_code', None)

#         # 2. Filter form (already processed in get() method)
#         context['filter_form'] = getattr(self, 'processed_filter_form', None)
        
#         # 3. Report-specific data (by calling the child's get_report_data)
#         # self.processed_filter_form should be set by the get() method
#         report_specific_data = self.get_report_data(getattr(self, 'processed_filter_form', None))
#         context.update(report_specific_data)

#         # 4. Filter params for pagination/export links
#         if self.request.GET:
#             query_params = self.request.GET.copy()
#             if 'page' in query_params: del query_params['page']
#             context['filter_params'] = query_params.urlencode()
#         else:
#             context['filter_params'] = ''
        
#         if 'report_generated_at' not in context: # Avoid overriding if set by child
#             context['report_generated_at'] = timezone.now()

#         logger.debug(f"{self.__class__.__name__}: Final context. Keys: {list(context.keys())}")
#         return context

#     def get(self, request, *args, **kwargs):
#         form_kwargs = self.get_filter_form_kwargs()
#         current_filter_form_class = getattr(self, 'filter_form_class', None)
        
#         if current_filter_form_class:
#             if request.GET: # If GET data exists, bind the form
#                 self.processed_filter_form = current_filter_form_class(request.GET, **form_kwargs)
#                 # is_valid() will be checked by get_report_data in child view if needed
#             else: # No GET data, show unbound form with initial data
#                 data_for_form = self.request.GET if self.request.GET else None
                
#                 self.processed_filter_form = self.filter_form_class(data_for_form, **form_kwargs)
#         else:
#             self.processed_filter_form = None
#             logger.debug(f"{self.__class__.__name__}: No filter_form_class defined.")
        
#         export_type = request.GET.get('export')
#         if export_type:
#             # get_report_data needs the processed_filter_form
#             report_data_for_export = self.get_report_data(self.processed_filter_form)
#             if report_data_for_export.get('report_generated', False):
#                 if export_type == 'csv': return self.export_to_csv(report_data_for_export, request)
#                 if export_type == 'excel': return self.export_to_excel(report_data_for_export, request)
#                 if export_type == 'pdf': 
#                     pdf_context = self.get_context_data_for_pdf(report_data_for_export)
#                     return self.export_to_pdf(report_data_for_export, request, pdf_context)
#             else: # Report not generated (e.g. form invalid or no data for filters)
#                 messages.warning(request, _("Please apply valid filters to generate the report before exporting."))
#                 query_params = request.GET.copy(); query_params.pop('export', None)
#                 return redirect(f"{request.path}?{query_params.urlencode()}")

#         # This will call self.get_context_data()
#         return super().get(request, *args, **kwargs) 

#     # Default export methods (children should override)
#     def export_to_csv(self, report_data_dict, request): raise NotImplementedError
#     def export_to_excel(self, report_data_dict, request): raise NotImplementedError
#     def export_to_pdf(self, report_data_dict, request, html_context): raise NotImplementedError
#     def get_context_data_for_pdf(self, report_data_dict): return {'report_data': report_data_dict} # Basic
#     # def get_school_profile(self): ... (if needed by PDF context)
    
    
    
# D:\school_fees_saas_v2\apps\common\mixins.py
from django.contrib.auth.mixins import AccessMixin # Use AccessMixin for more control
from django.shortcuts import redirect, reverse
from django.urls import reverse_lazy
from django.contrib import messages
from django.utils.translation import gettext_lazy as _

# --- Make sure StaffUser is imported correctly and safely ---
_StaffUser = None
try:
    from apps.schools.models import StaffUser as _SUModel
    _StaffUser = _SUModel
except ImportError:
    # This logger might not be configured yet if settings haven't fully loaded
    # logging.getLogger(__name__).error("CRITICAL: StaffUser model could not be imported in common.mixins.")
    pass # Handle the None case in dispatch

class StaffLoginRequiredMixin(AccessMixin):
    """
    Verify that the current user is authenticated and is an instance of StaffUser.
    """
    login_url = reverse_lazy('schools:staff_login') # Default staff login URL
    permission_denied_message = _("You must be logged in as staff to access this page.")

    def dispatch(self, request, *args, **kwargs):
        if _StaffUser is None: # Check if model failed to import
            messages.error(request, _("System error: Staff authentication module not available."))
            # Redirect to a generic error page or public home
            return redirect(reverse_lazy('public_site:home')) 

        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        if not isinstance(request.user, _StaffUser):
            messages.error(request, _("Access Denied. This area is for staff accounts only."))
            # Optionally log out the user if they are the wrong type
            # from django.contrib.auth import logout
            # logout(request)
            return redirect(self.get_login_url()) # Redirect to staff login
        
        if not request.user.is_active: # Check if the StaffUser is active
            messages.error(request, _("Your staff account is inactive. Please contact an administrator."))
            return redirect(self.get_login_url())

        return super().dispatch(request, *args, **kwargs)

    # get_login_url and handle_no_permission are provided by AccessMixin
    # but you can override them if needed.
    # def get_login_url(self):
    #     return str(self.login_url)

    # def handle_no_permission(self):
    #     messages.error(self.request, self.permission_denied_message)
    #     return redirect(self.get_login_url())

# ... your other existing mixins (TenantLoginRequiredMixin, TenantPermissionRequiredMixin, etc.) ...




# apps/common/decorators.py (New file or add to mixins.py)
from functools import wraps
from django.contrib.auth.decorators import user_passes_test
from django.core.exceptions import PermissionDenied
from django.shortcuts import redirect
from django.urls import reverse_lazy
from django.conf import settings
from django.contrib import messages # For messages

def tenant_permission_required(permission_required, login_url=None, raise_exception=False):
    """
    Decorator for views that checks that the user is logged in and has
    a particular permission in the tenant context.
    Uses request.effective_tenant_user if available.
    """
    if login_url is None:
        login_url = getattr(settings, 'TENANT_LOGIN_URL', reverse_lazy('schools:staff_login')) # Default

    def check_perms(user, perms):
        user_to_check = user # By default use request.user
        # If you have a convention to set effective_tenant_user on request for owners acting as staff
        if hasattr(user, '_request_for_perms_check') and \
           hasattr(user._request_for_perms_check, 'effective_tenant_user'):
            user_to_check = user._request_for_perms_check.effective_tenant_user
        
        if isinstance(perms, str):
            perms = (perms,)
        return user_to_check.has_perms(perms)

    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            # Attach request to user temporarily if effective_tenant_user needs it for context
            # This is a bit of a hack if effective_tenant_user relies on request attributes
            # not directly on the user object itself for permission checking.
            # A cleaner way is if effective_tenant_user itself is the fully capable user object.
            if hasattr(request.user, 'is_authenticated') and request.user.is_authenticated:
                # Attach request to user object if check_perms needs it for effective_tenant_user
                request.user._request_for_perms_check = request 
                
                if check_perms(request.user, permission_required):
                    del request.user._request_for_perms_check # Clean up
                    return view_func(request, *args, **kwargs)
                
                if hasattr(request.user, '_request_for_perms_check'): # Clean up always
                    del request.user._request_for_perms_check

                if raise_exception:
                    raise PermissionDenied("You do not have permission to access this page.")
                messages.error(request, "You do not have permission to access this page.")
                # Redirect based on tenant context
                public_schema_name = getattr(settings, 'MY_PUBLIC_SCHEMA_NAME', 'public')
                if hasattr(request, 'tenant') and request.tenant and request.tenant.schema_name != public_schema_name:
                    return redirect(getattr(settings, 'TENANT_DASHBOARD_URL', reverse_lazy('schools:dashboard')))
                else:
                    return redirect(getattr(settings, 'PUBLIC_SITE_HOME_URL', reverse_lazy('public_site:home')))
            
            # If not authenticated, redirect to login
            from django.contrib.auth.views import redirect_to_login
            path = request.build_absolute_uri()
            return redirect_to_login(path, login_url) # Uses login_url passed or default
        return _wrapped_view
    return decorator



