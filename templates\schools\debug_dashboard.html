<!DOCTYPE html>
<html>
<head>
    <title>Debug Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-info { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .debug-item { margin: 10px 0; }
        .debug-label { font-weight: bold; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>🔧 Login Debug Information</h1>
    
    <div class="debug-info">
        <h2>Session Debug Info</h2>
        
        <div class="debug-item">
            <span class="debug-label">User:</span> {{ debug_info.user }}
        </div>
        
        <div class="debug-item">
            <span class="debug-label">User Type:</span> {{ debug_info.user_type }}
        </div>
        
        <div class="debug-item">
            <span class="debug-label">Authenticated:</span> 
            <span class="{% if debug_info.authenticated %}success{% else %}error{% endif %}">
                {{ debug_info.authenticated }}
            </span>
        </div>
        
        <div class="debug-item">
            <span class="debug-label">Session Keys:</span> {{ debug_info.session_keys }}
        </div>
        
        <div class="debug-item">
            <span class="debug-label">Session Auth User ID:</span> {{ debug_info.session_auth_user_id }}
        </div>
        
        <div class="debug-item">
            <span class="debug-label">Session Auth Backend:</span> {{ debug_info.session_auth_user_backend }}
        </div>
        
        {% if debug_info.is_staff_user is not None %}
        <div class="debug-item">
            <span class="debug-label">Is Staff User:</span> 
            <span class="{% if debug_info.is_staff_user %}success{% else %}error{% endif %}">
                {{ debug_info.is_staff_user }}
            </span>
        </div>
        {% endif %}
    </div>
    
    <div style="margin-top: 20px;">
        <h3>🔄 Try Again</h3>
        <p><a href="/portal/staff/login/">Go back to Staff Login</a></p>
        <p><a href="/parents/login/">Go to Parent Login</a></p>
    </div>
    
    <div style="margin-top: 20px; font-size: 12px; color: #666;">
        <p><strong>Note:</strong> This is a temporary debug page to help diagnose the login session issue.</p>
    </div>
</body>
</html>
