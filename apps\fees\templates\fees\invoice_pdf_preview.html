{% extends "tenant_base.html" %}
{% load static humanize fees_tags %}

{% block title %}{{ view_title }} - {{ request.tenant.name }}{% endblock %}

{% block extra_tenant_css %}
<style>
    .pdf-preview-card {
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    .pdf-option-btn {
        min-height: 120px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        border: 2px solid transparent;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
    }
    .pdf-option-btn:hover {
        border-color: var(--bs-primary);
        background-color: var(--bs-light);
        text-decoration: none;
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    .pdf-option-icon {
        font-size: 2.5rem;
        margin-bottom: 0.5rem;
    }
    .invoice-summary {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'fees:invoice_list' %}">Invoices</a></li>
                    <li class="breadcrumb-item"><a href="{{ invoice.get_absolute_url }}">{{ invoice.invoice_number_display }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">PDF Options</li>
                </ol>
            </nav>

            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                    PDF Options
                </h1>
                <a href="{{ invoice.get_absolute_url }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-1"></i> Back to Invoice
                </a>
            </div>

            {% include "partials/_messages.html" %}

            <!-- Invoice Summary -->
            <div class="invoice-summary">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4 class="mb-2">
                            <i class="bi bi-file-earmark-text text-primary me-2"></i>
                            Invoice {{ invoice.invoice_number_display }}
                        </h4>
                        <div class="row">
                            <div class="col-sm-6">
                                <p class="mb-1"><strong>Student:</strong> {{ invoice.student.full_name }}</p>
                                <p class="mb-1"><strong>Academic Year:</strong> {{ invoice.academic_year.name }}</p>
                            </div>
                            <div class="col-sm-6">
                                <p class="mb-1"><strong>Issue Date:</strong> {{ invoice.issue_date|date:"M d, Y" }}</p>
                                <p class="mb-1"><strong>Due Date:</strong> {{ invoice.due_date|date:"M d, Y" }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="badge bg-{% if invoice.status == 'DRAFT' %}secondary{% elif invoice.status == 'SENT' %}primary{% elif invoice.status == 'PAID' %}success{% elif invoice.status == 'OVERDUE' %}danger{% else %}info{% endif %} fs-6 mb-2">
                            {{ invoice.get_status_display }}
                        </div>
                        <h5 class="mb-0 text-primary">
                            Total: ${{ invoice.net_billable_amount|floatformat:2|intcomma }}
                        </h5>
                    </div>
                </div>
            </div>

            <!-- PDF Options -->
            <div class="row g-4">
                <!-- View PDF Option -->
                <div class="col-md-6">
                    <div class="pdf-preview-card h-100">
                        <div class="card-body p-4">
                            <a href="{% url 'fees:invoice_pdf' invoice.pk %}" 
                               target="_blank" 
                               class="pdf-option-btn w-100 h-100 text-primary">
                                <i class="bi bi-eye pdf-option-icon"></i>
                                <h5 class="mb-2">View PDF</h5>
                                <p class="text-muted mb-0 text-center">
                                    Open the PDF in a new tab for viewing.<br>
                                    Perfect for reviewing before printing.
                                </p>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Print PDF Option -->
                <div class="col-md-6">
                    <div class="pdf-preview-card h-100">
                        <div class="card-body p-4">
                            <a href="{% url 'fees:invoice_pdf' invoice.pk %}?print=1" 
                               target="_blank" 
                               class="pdf-option-btn w-100 h-100 text-success"
                               onclick="setTimeout(() => window.print(), 1000);">
                                <i class="bi bi-printer pdf-option-icon"></i>
                                <h5 class="mb-2">Print PDF</h5>
                                <p class="text-muted mb-0 text-center">
                                    Open PDF and automatically show print dialog.<br>
                                    Ready for immediate printing.
                                </p>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Options -->
            <div class="row g-4 mt-2">
                <!-- Download PDF Option -->
                <div class="col-md-6">
                    <div class="pdf-preview-card h-100">
                        <div class="card-body p-4">
                            <a href="{% url 'fees:invoice_pdf' invoice.pk %}?download=1" 
                               class="pdf-option-btn w-100 h-100 text-info">
                                <i class="bi bi-download pdf-option-icon"></i>
                                <h5 class="mb-2">Download PDF</h5>
                                <p class="text-muted mb-0 text-center">
                                    Download the PDF file to your computer.<br>
                                    Save for records or email to parents.
                                </p>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Email PDF Option -->
                <div class="col-md-6">
                    <div class="pdf-preview-card h-100">
                        <div class="card-body p-4">
                            <a href="#" 
                               class="pdf-option-btn w-100 h-100 text-warning"
                               onclick="alert('Email functionality coming soon!'); return false;">
                                <i class="bi bi-envelope pdf-option-icon"></i>
                                <h5 class="mb-2">Email PDF</h5>
                                <p class="text-muted mb-0 text-center">
                                    Send the PDF directly to parent's email.<br>
                                    <small class="text-warning">(Coming Soon)</small>
                                </p>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card border-0 bg-light">
                        <div class="card-body py-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">Quick Actions</h6>
                                    <small class="text-muted">Common tasks for this invoice</small>
                                </div>
                                <div class="btn-group" role="group">
                                    {% if invoice.is_editable and perms.fees.change_invoice %}
                                        <a href="{% url 'fees:invoice_update' invoice.pk %}" class="btn btn-outline-primary btn-sm">
                                            <i class="bi bi-pencil-square me-1"></i> Edit Invoice
                                        </a>
                                    {% endif %}
                                    {% if invoice.is_payable %}
                                        <a href="{% url 'payments:record_payment' %}?invoice={{ invoice.pk }}" class="btn btn-outline-success btn-sm">
                                            <i class="bi bi-cash-coin me-1"></i> Record Payment
                                        </a>
                                    {% endif %}
                                    <a href="{{ invoice.get_absolute_url }}" class="btn btn-outline-secondary btn-sm">
                                        <i class="bi bi-eye me-1"></i> View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block page_specific_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects and analytics tracking if needed
    const pdfOptions = document.querySelectorAll('.pdf-option-btn');
    
    pdfOptions.forEach(option => {
        option.addEventListener('click', function(e) {
            // Add loading state
            const icon = this.querySelector('i');
            const originalClass = icon.className;
            icon.className = 'bi bi-hourglass-split pdf-option-icon';
            
            // Restore icon after a delay
            setTimeout(() => {
                icon.className = originalClass;
            }, 2000);
        });
    });
});
</script>
{% endblock %}
