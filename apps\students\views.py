# D:\school_fees_saas_v2\apps\students\views.py
from django.shortcuts import render, redirect, get_object_or_404
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin # For login and permission protection
from django.contrib.messages.views import SuccessMessageMixin
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.db.models import Q, Prefetch, ExpressionWrapper, F, DecimalField # For complex lookups and annotations
from django.db import transaction, connection
from django.http import JsonResponse # For AJAX

from .models import Student # Assuming ParentProfile is handled separately or not directly in Student CRUD
from .forms import StudentForm #, StudentFilterForm # StudentFilterForm for ListView

from apps.fees.models import AcademicYear

from apps.schools.models import SchoolClass, Section # For form querysets and filtering
# Import models for fee allocation and concession display if needed on detail page
from apps.fees.models import StudentFeeAllocation, StudentConcession, Invoice, FeeStructure, ConcessionType
from apps.fees.forms import StudentFeeAllocationForm, StudentConcessionForm

from .forms import StudentForm, ParentUserChangeForm # StudentFilterForm,

from datetime import date

from django.utils.translation import gettext_lazy as _
from apps.common.mixins import TenantLoginRequiredMixin, TenantPermissionRequiredMixin 

# from django.db.models import Prefetch # Only if still needed elsewhere
from datetime import date # For age calculation
import logging # For logging
from apps.fees.models import Invoice
from .models import Student
from .filters import StudentFilterForm


from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, UpdateView, CreateView
from django.contrib.auth.mixins import LoginRequiredMixin # Use your StaffLoginRequiredMixin
from apps.common.mixins import StaffLoginRequiredMixin, TenantPermissionRequiredMixin # Your actual mixins
from .models import Student, ParentUser # Assuming ParentUser is here for now
from .forms import StudentForm, StudentParentLinkForm # We'll define StudentParentLinkForm soon


from django.db import models

logger = logging.getLogger(__name__) # Logger for this view file

# Helper function for age calculation
def calculate_student_age(birth_date):
    if not birth_date:
        return None # Or "N/A"
    today = date.today()
    try:
        age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
        return age
    except AttributeError: # In case birth_date is not a proper date object
        logger.warning(f"Could not calculate age for birth_date: {birth_date} (type: {type(birth_date)})")
        return None



# D:\school_fees_saas_v2\apps\students\views.py

import logging
from decimal import Decimal
from datetime import date # For calculate_student_age

from django import forms
from django.db import models # For isinstance(filtered_qs, models.QuerySet)
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from apps.common.mixins import StaffLoginRequiredMixin, TenantPermissionRequiredMixin # Use these if they are your standard
from django.contrib.messages.views import SuccessMessageMixin
from django.db.models import Prefetch
from django.http import HttpResponseForbidden # Or your custom permission denied handling
from django.shortcuts import redirect, get_object_or_404
from django.urls import reverse_lazy, reverse
from django.utils.translation import gettext_lazy as _
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView

# Model & Form Imports
from .models import Student, ParentUser # Assuming ParentUser is defined here or in users app
from .filters import StudentFilterForm
from .forms import StudentForm, StudentParentLinkForm # StudentParentLinkForm to be created
# from apps.schools.models import AcademicYear, SchoolClass, Section # For type hints and choices if needed
# from apps.fees.models import Invoice, StudentFeeAllocation, StudentConcession, FeeStructure, ConcessionType
# from apps.common.utils import calculate_student_age # If in common utils

logger = logging.getLogger(__name__)

# --- Utility Functions (if not imported) ---
def calculate_student_age(dob):
    if not dob:
        return None
    today = date.today()
    try:
        age = today.year - dob.year - ((today.month, today.day) < (dob.month, dob.day))
        return age
    except AttributeError: # dob might not be a date object
        return None

# --- Student CRUD Views ---

class StudentListView(LoginRequiredMixin, PermissionRequiredMixin, ListView): # Replace with StaffLoginRequiredMixin, TenantPermissionRequiredMixin
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'students.view_student'
    model = Student
    template_name = 'students/student_list.html'
    context_object_name = 'students'
    paginate_by = 20
    filterset_class = StudentFilterForm 

    def get_queryset(self):
        logger.debug(f"StudentListView ({self.__class__.__name__}): get_queryset called. Request GET: {self.request.GET}")
        
        base_queryset = super().get_queryset().filter(is_active=True).select_related( # Default to active students
            'current_class', 'current_section'
        ).prefetch_related(
            Prefetch('parents', queryset=ParentUser.objects.filter(is_active=True)) # Prefetch active parents
        )

        self.filterset = self.filterset_class(
            self.request.GET,
            queryset=base_queryset,
            request=self.request
        )
        
        logger.debug(f"StudentListView: Filterset instance: {self.filterset}, Form type: {type(self.filterset.form)}")
        logger.debug(f"StudentListView: Is self.filterset.form a Form instance? {isinstance(self.filterset.form, forms.Form)}")

        filtered_qs = self.filterset.qs.order_by(
            'current_class__name', 'current_section__name', 'last_name', 'first_name'
        )
        
        logger.debug(f"StudentListView: Filtered queryset count: {filtered_qs.count() if isinstance(filtered_qs, models.QuerySet) else 'N/A'}")
        return filtered_qs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Manage Students")

        if hasattr(self, 'filterset'):
            context['filter_form'] = self.filterset.form
            if not isinstance(context['filter_form'], forms.Form):
                logger.error(f"StudentListView: CRITICAL - context['filter_form'] IS NOT A DJANGO FORM! Type: {type(context['filter_form'])}")
            else:
                logger.info(f"StudentListView: context['filter_form'] IS a Django Form instance.")
        else:
            logger.error("StudentListView: self.filterset NOT available in get_context_data. Recreating (this is a fallback).")
            base_qs_for_context = super().get_queryset().filter(is_active=True).select_related('current_class', 'current_section').prefetch_related('parents')
            self.filterset = self.filterset_class(self.request.GET, queryset=base_qs_for_context, request=self.request)
            context['filter_form'] = self.filterset.form
        
        students_on_page = context.get(self.context_object_name) 
        if students_on_page:
            for student_obj in students_on_page: # Renamed to avoid conflict with model name
                if student_obj.date_of_birth:
                    student_obj.calculated_age = calculate_student_age(student_obj.date_of_birth)
                else:
                    student_obj.calculated_age = None
        
        if self.request.GET:
            query_params = self.request.GET.copy()
            if 'page' in query_params:
                del query_params['page']
            context['filter_params'] = query_params.urlencode()
            
        logger.debug(f"StudentListView: Final context keys: {list(context.keys())}")
        return context

class StudentDetailView(LoginRequiredMixin, PermissionRequiredMixin, DetailView): # Replace with StaffLoginRequiredMixin, TenantPermissionRequiredMixin
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'students.view_student'
    model = Student
    template_name = 'students/student_detail.html'
    context_object_name = 'student'

    def get_queryset(self):
        # Your existing comprehensive queryset with select_related and prefetch_related
        # is good. Ensure all related models (StudentFeeAllocation, Invoice, ConcessionType etc.)
        # are imported at the top of the file if you uncomment these prefetches.
        return super().get_queryset().select_related(
            'current_class', 'current_section', 'created_by'
        ).prefetch_related(
            Prefetch('parents', queryset=ParentUser.objects.filter(is_active=True).only('id', 'email', 'first_name', 'last_name', 'phone_number')),
            # Prefetch('assigned_fees', queryset=StudentFeeAllocation.objects.select_related(
            #     'fee_structure__academic_year', 'fee_structure__term', 
            #     'academic_year', 'term'
            # ).order_by('-academic_year__start_date', '-term__start_date')),
            # Prefetch('concessions', queryset=StudentConcession.objects.select_related(
            #     'concession_type', 'academic_year', 'specific_fee_head'
            # ).order_by('-academic_year__start_date', 'concession_type__name')),
            # Prefetch('invoices', queryset=Invoice.objects.filter(
            #     status__in=[Invoice.InvoiceStatus.SENT, Invoice.InvoiceStatus.PARTIALLY_PAID, Invoice.InvoiceStatus.OVERDUE]
            # ).select_related(
            #     'academic_year', 'term', 'fee_structure'
            # ).order_by('-issue_date')) 
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        student = self.object
        context['view_title'] = f"Student Details: {student.get_full_name()}"
        context['page_title'] = student.get_full_name()
        context['linked_parents'] = student.parents.all() # For display

        # Example for current AY for other potential forms on detail page
        # from apps.schools.models import AcademicYear # Ensure import
        # current_academic_year = AcademicYear.objects.filter(is_current=True, tenant_id=self.request.tenant.id).first() \
        #                         or AcademicYear.objects.filter(tenant_id=self.request.tenant.id).order_by('-start_date').first()
        # context['current_academic_year_for_forms'] = current_academic_year
        
        # If you have forms for adding allocations/concessions directly on this page:
        # common_initial = {}
        # if current_academic_year:
        #     common_initial['academic_year'] = current_academic_year.pk
        # context['fee_allocation_form'] = StudentFeeAllocationForm(student_instance=student, initial=common_initial.copy())
        # context['concession_form'] = StudentConcessionForm(student_instance=student, initial=common_initial.copy())
        # context['active_fee_structures'] = FeeStructure.objects.filter(is_active=True, academic_year=current_academic_year).select_related('term')
        # context['active_concession_types'] = ConcessionType.objects.filter(is_active=True)

        # context['outstanding_balance'] = student.calculate_outstanding_balance() # Assuming method exists
        logger.debug(f"StudentDetailView: Context prepared for student {student.pk}.")
        return context

class StudentCreateView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, CreateView): # Replace with Staff mixins
    model = Student
    form_class = StudentForm
    template_name = 'students/student_form.html'
    permission_required = 'students.add_student'
    success_url = reverse_lazy('students:student_list')
    success_message = _("Student '%(first_name)s %(last_name)s' (%(admission_number)s) created successfully.")

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant'] = self.request.tenant # Pass tenant to form if needed
        return kwargs

    def form_valid(self, form):
        # --- USAGE LIMIT ENFORCEMENT ---
        # from apps.subscriptions.models import Subscription # Ensure import
        subscription = getattr(self.request.tenant, 'subscription', None)
        if subscription and subscription.is_usable and hasattr(subscription.plan, 'max_students') and subscription.plan.max_students is not None:
            # Count active students for the current tenant
            current_student_count = Student.objects.filter(is_active=True).count() 
            if current_student_count >= subscription.plan.max_students:
                error_message = _(
                    "Cannot add new student. You have reached the maximum of %(max_students)s active students allowed for your current plan (%(plan_name)s). "
                    "Please archive inactive students or upgrade your plan."
                ) % {'max_students': subscription.plan.max_students, 'plan_name': subscription.plan.name}
                messages.error(self.request, error_message)
                logger.warning(f"Tenant '{self.request.tenant.schema_name}': Student creation blocked. Max students ({subscription.plan.max_students}) reached for plan '{subscription.plan.name}'. Current: {current_student_count}.")
                return self.form_invalid(form)
        # --- END USAGE LIMIT ENFORCEMENT ---

        # Set created_by
        # from apps.schools.models import StaffUser # Ensure import
        if StaffUser and isinstance(self.request.user, StaffUser):
            form.instance.created_by = self.request.user
            logger.info(f"Student '{form.cleaned_data.get('first_name')} {form.cleaned_data.get('last_name')}' being created by STAFF: '{self.request.user.email}' in tenant '{self.request.tenant.schema_name}'.")
        else:
            logger.warning(f"StudentCreateView: created_by not set. request.user is not StaffUser or StaffUser model not loaded. User: {self.request.user}")
        
        self.object = form.save() # Save the object
        # SuccessMessageMixin will use self.object for formatting the message
        return super().form_valid(form) # This calls form.save() again, ensure form_class.save() is idempotent or handle it.
                                        # A common pattern is:
                                        # self.object = form.save()
                                        # return HttpResponseRedirect(self.get_success_url())
                                        # But SuccessMessageMixin hooks into form_valid.

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Add New Student")
        context['form_mode'] = "create"
        subscription = getattr(self.request.tenant, 'subscription', None)
        if subscription and subscription.is_usable and hasattr(subscription.plan, 'max_students') and subscription.plan.max_students is not None:
            context['current_student_count'] = Student.objects.filter(is_active=True).count()
            context['max_students_limit'] = subscription.plan.max_students
        logger.debug("StudentCreateView: Context prepared.")
        return context

class StudentUpdateView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, UpdateView): # Replace with Staff mixins
    model = Student
    form_class = StudentForm
    template_name = 'students/student_form.html'
    context_object_name = 'student'
    permission_required = 'students.change_student'
    success_message = _("Student '%(first_name)s %(last_name)s' (%(admission_number)s) updated successfully.")

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant'] = self.request.tenant
        return kwargs

    def get_success_url(self):
        return reverse('students:student_detail', kwargs={'pk': self.object.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Edit Student: {self.object.get_full_name()}"
        context['form_mode'] = "update"
        logger.debug(f"StudentUpdateView: Context for editing student {self.object.pk}.")
        return context

class StudentDeleteView(LoginRequiredMixin, PermissionRequiredMixin, DeleteView): # Replace with Staff mixins
    model = Student
    template_name = 'students/student_confirm_delete.html'
    success_url = reverse_lazy('students:student_list')
    context_object_name = 'student'
    permission_required = 'students.delete_student'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Confirm Delete: {self.object.get_full_name()}"
        return context

    def form_valid(self, form): # Overriding form_valid to add message *before* deletion by super()
        student_name = self.object.get_full_name() # Get name before object is deleted
        # Add pre-delete checks here:
        # if self.object.invoices.exists(): # Ensure Invoice related_name is 'invoices'
        #     messages.error(self.request, _(f"Cannot delete student '{student_name}' as they have associated invoices. Please clear or reassign invoices first."))
        #     return redirect('students:student_detail', pk=self.object.pk)
        
        response = super().form_valid(form) # Performs the delete
        messages.success(self.request, _(f"Student '{student_name}' deleted successfully."))
        logger.info(f"Student '{student_name}' (PK: {self.object.pk if hasattr(self, 'object') else 'UnknownPK_deleted'}) DELETED by user '{self.request.user.email}' in tenant '{self.request.tenant.schema_name}'.")
        return response

# --- View for Managing Student-Parent Links ---
class StudentManageParentsView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, UpdateView): # Replace with Staff mixins
    model = Student
    form_class = StudentParentLinkForm 
    template_name = 'students/student_manage_parents.html' 
    context_object_name = 'student'
    permission_required = 'students.change_student' # Or a more specific permission
    # For success_message to work with M2M changes, often need to ensure form.save() triggers it correctly
    # or set it manually in form_valid before super().
    success_message = _("Parent links for student '%(full_name)s' updated successfully.")
    # Let's try to make it work with the object's name.
    
    def get_success_message(self, cleaned_data):
        return _("Parent links for student '%(full_name)s' updated successfully.") % {'full_name': self.object.get_full_name()}


    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['request_get'] = self.request.GET # Pass GET params to form for search query
        return kwargs

    def get_success_url(self):
        return reverse('students:student_detail', kwargs={'pk': self.object.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Manage Parents for: {self.object.get_full_name()}"
        # The form instance (self.get_form()) will have the search_query populated
        # if it was in request.GET, because get_form_kwargs passes request.GET to form.
        return context

    def form_valid(self, form):
        # When form_class is a ModelForm and Meta.fields includes the M2M field ('parents'),
        # super().form_valid(form) calls form.save(), which handles the M2M update.
        logger.info(f"Updating parent links for student '{self.object.get_full_name()}'.")
        return super().form_valid(form)
    
    
# TODO:
# - ParentUser CRUD views (ParentUserListView, ParentUserCreateView, etc.)
# - ParentUserManageChildrenView (mirror of StudentManageParentsView, from Parent's perspective)
# - Refine StudentParentLinkForm and its template for better UX (e.g., Select2, search for parents)



# # apps/students/views.py
# import logging
# from django.views.generic import ListView
# from django.urls import reverse_lazy
# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django import forms # For isinstance check
# import django_filters # For isinstance check

# from .models import Student # Assuming calculate_student_age is also in models or utils
# from .filters import StudentFilterForm # Your FilterSet class

# logger = logging.getLogger(__name__) # Or your specific view logger: logging.getLogger('apps.students.views')

# # Assuming calculate_student_age is defined somewhere, e.g., in models.py or a utils.py
# # from .utils import calculate_student_age 
# # For now, let's define a placeholder if it's not imported:
# from datetime import date
# def calculate_student_age(dob):
#     if not dob:
#         return None
#     today = date.today()
#     age = today.year - dob.year - ((today.month, today.day) < (dob.month, dob.day))
#     return age

# class StudentListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
#     login_url = reverse_lazy('schools:staff_login')
#     permission_required = 'students.view_student'
#     model = Student
#     template_name = 'students/student_list.html'
#     context_object_name = 'students'
#     paginate_by = 20 # Good default

#     # Use a different attribute name for the FilterSet instance, e.g., self.filterset
#     filterset_class = StudentFilterForm 

#     def get_queryset(self):
#         logger.debug(f"StudentListView ({self.__class__.__name__}): get_queryset called. Request GET: {self.request.GET}")
        
#         # Start with the base queryset for the model
#         base_queryset = super().get_queryset().select_related(
#             'current_class', 'current_section'
#         ).prefetch_related('parents') # Keep prefetch if parent info displayed

#         # Instantiate the filterset (FilterSet instance)
#         self.filterset = self.filterset_class( # Use self.filterset_class
#             self.request.GET,
#             queryset=base_queryset,
#             request=self.request # Pass request if your filter needs it
#         )
        
#         logger.debug(f"StudentListView ({self.__class__.__name__}): Filterset INSTANCE created: {self.filterset}")
#         logger.debug(f"StudentListView ({self.__class__.__name__}): Type of self.filterset: {type(self.filterset)}")
#         logger.debug(f"StudentListView ({self.__class__.__name__}): Type of self.filterset.form: {type(self.filterset.form)}")
#         logger.debug(f"StudentListView ({self.__class__.__name__}): Is self.filterset.form a Form instance? {isinstance(self.filterset.form, forms.Form)}")

#         # Apply ordering after filtering
#         # The .qs attribute of the filterset is the filtered queryset
#         filtered_qs = self.filterset.qs.order_by(
#             'current_class__name', 
#             'current_section__name', 
#             'last_name', 
#             'first_name'
#         )
        
#         logger.debug(f"StudentListView ({self.__class__.__name__}): Filtered queryset count (before pagination): {filtered_qs.count() if isinstance(filtered_qs, models.QuerySet) else 'N/A_not_queryset'}")
#         return filtered_qs

#     def get_context_data(self, **kwargs):
#         logger.debug(f"StudentListView ({self.__class__.__name__}): get_context_data called.")
#         context = super().get_context_data(**kwargs)
#         context['view_title'] = "Manage Students"

#         # Ensure self.filterset exists (it should be set by get_queryset)
#         if not hasattr(self, 'filterset'):
#             logger.error(f"StudentListView ({self.__class__.__name__}): CRITICAL - self.filterset was NOT available in get_context_data. Recreating. This should be investigated.")
#             # Fallback logic if get_queryset didn't run or set self.filterset
#             base_qs_for_context = super().get_queryset().select_related('current_class', 'current_section').prefetch_related('parents')
#             self.filterset = self.filterset_class(
#                 self.request.GET,
#                 queryset=base_qs_for_context,
#                 request=self.request
#             )
        
#         # --- THE CRITICAL FIX: Pass the .form attribute ---
#         context['filter_form'] = self.filterset.form 
#         # --- END CRITICAL FIX ---
        
#         logger.debug(f"StudentListView ({self.__class__.__name__}): Value assigned to context['filter_form']: {context['filter_form']}")
#         logger.debug(f"StudentListView ({self.__class__.__name__}): Type of context['filter_form']: {type(context['filter_form'])}")
        
#         if not isinstance(context['filter_form'], forms.Form) and \
#             not isinstance(context['filter_form'], django_filters.forms.FilterForm):
#             logger.error(f"StudentListView ({self.__class__.__name__}): CRITICAL ERROR - context['filter_form'] IS NOT A DJANGO FORM INSTANCE! It is: {type(context['filter_form'])}")
#         else:
#             logger.info(f"StudentListView ({self.__class__.__name__}): SUCCESS - context['filter_form'] IS a Django Form instance.")

#         # Calculate and add age for students on the CURRENT PAGE
#         students_on_page = context.get(self.context_object_name) 
#         if students_on_page:
#             logger.debug(f"StudentListView ({self.__class__.__name__}): Calculating age for {len(students_on_page)} students on current page.")
#             for student in students_on_page:
#                 student.calculated_age = calculate_student_age(student.date_of_birth) # Make sure this function is accessible
#         else:
#             logger.debug(f"StudentListView ({self.__class__.__name__}): No students on current page to calculate age for.")
        
#         # is_valid() should be called on the form, not the filterset, if checking for display
#         # However, the filterset applies filters regardless of form validity for GET requests.
#         # For displaying errors on the form, you'd check context['filter_form'].is_bound and context['filter_form'].errors
#         logger.debug(f"StudentListView ({self.__class__.__name__}): Context prepared. Filter form is bound: {context['filter_form'].is_bound}, Filter form errors: {context['filter_form'].errors}")

#         # Preserve filter parameters in pagination links
#         if self.request.GET:
#             query_params = self.request.GET.copy()
#             if 'page' in query_params:
#                 del query_params['page']
#             context['filter_params'] = query_params.urlencode()
            
#         logger.debug(f"StudentListView ({self.__class__.__name__}): Final context keys: {list(context.keys())}")
#         return context

# from django.db.models import Prefetch, ExpressionWrapper, F, DecimalField # Ensure these are imported
# class StudentDetailView(LoginRequiredMixin, PermissionRequiredMixin, DetailView):
#     login_url = reverse_lazy('schools:staff_login') # Staff login for tenant-specific views
#     permission_required = 'students.view_student' # Ensure this permission exists and is assigned
#     model = Student
#     template_name = 'students/student_detail.html'
#     context_object_name = 'student'

#     def get_queryset(self):
#         """
#         Optimize database queries by selecting related objects and prefetching M2M.
#         """
#         return super().get_queryset().select_related(
#             'current_class',    # Assuming Student has an FK to SchoolClass named current_class
#             'current_section'   # Assuming Student has an FK to Section named current_section
#         ).prefetch_related(
#             'parents',          # Assuming a M2M or reverse FK from Parent to Student
            
#             Prefetch('assigned_fees', queryset=StudentFeeAllocation.objects.select_related(
#                 'fee_structure__academic_year', # FeeStructure -> AcademicYear
#                 'fee_structure__term',          # FeeStructure -> Term (if exists)
#                 'academic_year',                # Direct FK: StudentFeeAllocation -> AcademicYear (if exists)
#                 'term'                          # Direct FK: StudentFeeAllocation -> Term (if exists)
#             ).order_by('-academic_year__start_date', '-term__start_date')), # Ensure 'term' is accessible for ordering

#             Prefetch('concessions', queryset=StudentConcession.objects.select_related(
#                 'concession_type',
#                 'academic_year',
#                 'specific_fee_head' # Assuming StudentConcession -> FeeHead (or similar)
#                 # 'term', # Only if StudentConcession has a direct FK to Term
#             ).order_by('-academic_year__start_date', 'concession_type__name')),

#             Prefetch('invoices', queryset=Invoice.objects.filter(
#                 # Consider if Invoice.STATUS_PAID should also be included or if this is for outstanding only
#                 status__in=[Invoice.InvoiceStatus.OVERDUE, Invoice.InvoiceStatus.PARTIALLY_PAID] #, Invoice.InvoiceStatus.OVERDUE]
#             ).select_related(
#                 'academic_year', # Invoice -> AcademicYear
#                 'term',          # Invoice -> Term
#                 'fee_structure'  # Invoice -> FeeStructure
#             ).order_by('-issue_date')) 
#         )

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         student = self.object # self.object is the Student instance after super() call

#         context['view_title'] = f"Student Details: {student.get_full_name}" # Use get_full_name()
#         context['page_title'] = student.get_full_name()
        
#         # Determine the current or most relevant academic year
#         current_academic_year = AcademicYear.objects.filter(is_current=True).first()
#         if not current_academic_year:
#             # Fallback to the latest academic year if none is marked 'is_current'
#             current_academic_year = AcademicYear.objects.order_by('-start_date').first()
#         context['current_academic_year_for_forms'] = current_academic_year

#         # Forms for adding new allocations/concessions
#         # Ensure your forms' __init__ methods can handle `student_instance` and `initial`
#         # The `initial` data helps pre-populate form fields.
        
#         common_initial = {}
#         if current_academic_year:
#             common_initial['academic_year'] = current_academic_year.pk # Pass PK for ModelChoiceField

#         context['fee_allocation_form'] = StudentFeeAllocationForm(
#             student_instance=student,
#             initial=common_initial.copy() # Use .copy() if you modify it later for another form
#         )
#         context['concession_form'] = StudentConcessionForm(
#             student_instance=student,
#             initial=common_initial.copy()
#         )
        
#         # Optional: Provide choices for form fields if they are not dynamically handled within the form
#         # This is useful if choices depend on the current_academic_year or other context
#         if current_academic_year:
#             context['active_fee_structures'] = FeeStructure.objects.filter(
#                 is_active=True, 
#                 academic_year=current_academic_year
#             ).select_related('term') # If FeeStructure links to Term
            
#         context['active_concession_types'] = ConcessionType.objects.filter(is_active=True)

#         # Example: Get outstanding balance (this might be better as a method on Student model)
#         # context['outstanding_balance'] = student.calculate_outstanding_balance() 

#         return context

# class StudentCreateView(TenantLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, CreateView):
#     model = Student
#     form_class = StudentForm
#     template_name = 'students/student_form.html' # Or your generic form template
#     permission_required = 'students.add_student'
#     success_url = reverse_lazy('students:student_list')
#     success_message = _("Student '%(first_name)s %(last_name)s' created successfully.")

#     def get_form_kwargs(self):
#         kwargs = super().get_form_kwargs()
#         kwargs['tenant'] = self.request.tenant # Pass tenant to form if needed (e.g. for filtering choices)
#         return kwargs

#     def form_valid(self, form):
#         # --- USAGE LIMIT ENFORCEMENT ---
#         subscription = getattr(self.request.tenant, 'subscription', None)
#         limit_exceeded = False

#         if subscription and subscription.is_usable and subscription.plan.max_students is not None:
#             # Count active students specifically, or all students depending on your definition
#             current_student_count = Student.objects.filter(is_active=True).count() 
#             if current_student_count >= subscription.plan.max_students:
#                 limit_exceeded = True
#                 error_message = _(
#                     "You have reached the maximum of %(max_students)s active students allowed for your current plan (%(plan_name)s). "
#                     "Please archive inactive students or upgrade your plan to add more."
#                 ) % {'max_students': subscription.plan.max_students, 'plan_name': subscription.plan.name}
                
#                 messages.error(self.request, error_message)
#                 logger.warning(f"Tenant '{self.request.tenant.schema_name}': Student creation blocked. Max students ({subscription.plan.max_students}) reached for plan '{subscription.plan.name}'. Current: {current_student_count}.")
                
#                 # Add a specific error to a form field or non_field_errors if you want it displayed near the form
#                 # form.add_error(None, error_message) # Adds to non_field_errors
                
#                 return self.form_invalid(form)
#         # --- END USAGE LIMIT ENFORCEMENT ---

#         # If limit not exceeded, proceed to save
#         # Assuming created_by is a field on Student model and StaffUser is correctly imported
#         try:
#             from apps.schools.models import StaffUser
#             if StaffUser and isinstance(self.request.user, StaffUser):
#                 form.instance.created_by = self.request.user
#         except ImportError:
#             logger.warning("StudentCreateView: Could not import StaffUser to set created_by.")

#         logger.info(f"Student '{form.instance.first_name} {form.instance.last_name}' being created by STAFF: '{self.request.user.get_full_name()}' (Email: {self.request.user.email}) in tenant '{self.request.tenant.schema_name}'.")            
#         return super().form_valid(form)

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context['view_title'] = _("Add New Student")
#         context['form_mode'] = "create"
#         # Optionally, display current usage vs limit
#         subscription = getattr(self.request.tenant, 'subscription', None)
#         if subscription and subscription.is_usable and subscription.plan.max_students is not None:
#             context['current_student_count'] = Student.objects.filter(is_active=True).count()
#             context['max_students_limit'] = subscription.plan.max_students
#         return context

# class StudentUpdateView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, UpdateView):
#     login_url = reverse_lazy('schools:staff_login')
#     permission_required = 'students.change_student'
#     model = Student
#     form_class = StudentForm
#     template_name = 'students/student_form.html'
#     context_object_name = 'student'
#     success_message = "Student '%(first_name)s %(last_name)s' updated successfully."

#     def get_form_kwargs(self): # Pass tenant to form
#         kwargs = super().get_form_kwargs()
#         kwargs['tenant'] = self.request.tenant
#         return kwargs

#     def get_success_url(self):
#         return reverse_lazy('students:student_detail', kwargs={'pk': self.object.pk})

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         # self.object is the Student instance being edited
#         if self.object: 
#             context['view_title'] = f"Edit Student: {self.object.get_full_name()}" # <<< CORRECTED
#             context['page_title'] = f"Edit {self.object.get_full_name()}" # Example for another title if needed
#         else:
#             # This case (self.object being None in UpdateView's get_context_data) is rare
#             # as UpdateView is typically for an existing object.
#             context['view_title'] = "Edit Student"
#             context['page_title'] = "Edit Student"
        
#         # Add other context variables if needed
#         # context['some_other_variable'] = "Some Value"
#         return context

#     # form_valid handled by SuccessMessageMixin and UpdateView superclass

# class StudentDeleteView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, DeleteView):
#     login_url = reverse_lazy('schools:staff_login')
#     permission_required = 'students.delete_student'
#     model = Student
#     template_name = 'students/student_confirm_delete.html'
#     success_url = reverse_lazy('students:student_list')
#     # success_message is set by SuccessMessageMixin using model's __str__ or verbose_name
#     context_object_name = 'student'

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context['view_title'] = f"Confirm Delete: {self.object.full_name}"
#         return context

#     def form_valid(self, form): # Use form_valid for SuccessMessageMixin
#         # Use self.object before it's deleted for the message
#         success_message = f"Student '{self.object.full_name}' deleted successfully."
#         messages.success(self.request, success_message)
#         return super().form_valid(form)

#     # def post(self, request, *args, **kwargs): # Alternative for custom checks
#     #     student = self.get_object()
#     #     if student.invoices.exists(): # Example related check
#     #         messages.error(request, f"Cannot delete {student.full_name}, they have associated invoices.")
#     #         return redirect('students:student_detail', pk=student.pk)
#     #     return super().post(request, *args, **kwargs)


# --- AJAX view to load sections based on selected class ---
# This view does not need login protection if it only returns public data
# However, since it's tenant data, it should be protected if sensitive
# For AJAX, LoginRequiredMixin/decorators redirect, which AJAX doesn't handle well.
# Custom check or different auth might be needed for AJAX if login is required.
# For now, let's assume it's okay or called from pages already requiring login.
def load_sections_for_class(request):
    class_id = request.GET.get('class_id')
    sections_data = []
    if class_id:
        # This query will be tenant-scoped automatically
        sections = Section.objects.filter(school_class_id=class_id).order_by('name')
        sections_data = list(sections.values('id', 'name'))
    return JsonResponse(sections_data, safe=False)



from django.http import HttpResponse, HttpResponseRedirect
from django.contrib.auth.decorators import login_required, permission_required
from django.urls import reverse
from django.contrib import messages

@login_required
@permission_required('students.delete_student', raise_exception=True) # Example permission
def student_bulk_delete_view(request):
    if request.method == 'POST':
        student_ids = request.POST.getlist('student_ids_to_delete') # Assuming checkboxes have this name
        if student_ids:
            # students_to_delete = Student.objects.filter(pk__in=student_ids, school=request.tenant.school) # Example: ensure tenant scope
            # count = students_to_delete.count()
            # students_to_delete.delete()
            # messages.success(request, f"Successfully deleted {count} students.")
            messages.info(request, "Bulk delete functionality (placeholder): Would delete students.") # Placeholder
            return HttpResponseRedirect(reverse('students:student_list'))
        else:
            messages.warning(request, "No students selected for deletion.")
    # Redirect back to list if not POST or no IDs
    return HttpResponseRedirect(reverse('students:student_list'))




from .forms import StudentParentLinkForm # Your form
from .models import Student # Your Student model

class StudentManageParentsView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    model = Student
    form_class = StudentParentLinkForm 
    template_name = 'students/student_manage_parents.html' 
    context_object_name = 'student'
    permission_required = 'students.change_student' 
    
    # This class attribute is now just a fallback or not used if get_success_message is overridden
    # success_message = _("Parent links for student '%(full_name)s' updated successfully.") 

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        return kwargs

    def get_success_url(self):
        return reverse('students:student_detail', kwargs={'pk': self.object.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Manage Parents for: {self.object.get_full_name()}"
        return context

    def form_valid(self, form):
        logger.info(
            f"StudentManageParentsView: form_valid for student '{self.object.get_full_name()}' (PK: {self.object.pk}). "
            f"User '{self.request.user.email}' submitted new parent links: "
            f"{[p.email for p in form.cleaned_data.get('parents', [])]}"
        )
        # super().form_valid(form) calls form.save() and sets self.object
        # It then calls get_success_message() and adds the message.
        return super().form_valid(form)

    def get_success_message(self, cleaned_data):
        # self.object is the Student instance that was just saved by form.save()
        # which is called within super().form_valid()
        student_name = self.object.get_full_name() # Assuming Student model has get_full_name()
        return _("Parent links for student '%(student_name)s' updated successfully.") % {'student_name': student_name}

# class StudentManageParentsView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, UpdateView):
#     model = Student
#     form_class = StudentParentLinkForm 
#     template_name = 'students/student_manage_parents.html' 
#     context_object_name = 'student'
#     permission_required = 'students.change_student' # Or your specific permission
#     success_message = _("Parent links for student '%(full_name)s' updated successfully.")

#     def get_form_kwargs(self):
#         kwargs = super().get_form_kwargs()
#         # Your current StudentParentLinkForm __init__ expects 'instance', which UpdateView provides.
#         # If you add search functionality that needs GET params:
#         # kwargs['request_get'] = self.request.GET 
#         return kwargs

#     def get_success_url(self):
#         return reverse('students:student_detail', kwargs={'pk': self.object.pk})

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         # self.object is the Student instance being edited
#         context['view_title'] = _(f"Manage Parents for: {self.object.get_full_name()}")
#         logger.debug(f"StudentManageParentsView: Preparing context for student PK {self.object.pk}. Form instance: {context.get('form')}")
#         return context

#     def form_valid(self, form):
#         # `form` here is an instance of your StudentParentLinkForm.
#         # `self.object` is the Student instance being updated.
        
#         logger.info(
#             f"StudentManageParentsView: form_valid called for student '{self.object.get_full_name()}' (PK: {self.object.pk}). "
#             f"User '{self.request.user.email}' attempting to update parent links."
#         )
        
#         # Call the custom save method of your StudentParentLinkForm.
#         # This save method already takes care of the M2M update.
#         # It also receives the student instance because the form was initialized with it.
#         saved_student_instance = form.save(commit=True) # commit=True is default but good to be explicit
        
#         # UpdateView expects self.object to be the saved instance for SuccessMessageMixin
#         # and for get_success_url if it uses self.object.
#         # Since your form's save() returns the student instance, we can assign it.
#         self.object = saved_student_instance 
        
#         logger.info(
#             f"Parent links for student '{self.object.get_full_name()}' updated. "
#             f"New linked parents (from form.cleaned_data): {[p.email for p in form.cleaned_data.get('parents_to_link', [])]}"
#         )
        
#         # SuccessMessageMixin will use self.object and self.success_message.
#         # We don't call super().form_valid(form) here if the form's save() did everything.
#         # Instead, we need to construct the redirect response after showing the success message.
        
#         success_url = self.get_success_url()
#         # SuccessMessageMixin adds the message if you call super().form_valid() OR if you manually add it
#         # Let's manually add it to be sure it uses the updated self.object for formatting
#         if hasattr(self, 'get_success_message'): # If using the method
#             message = self.get_success_message(form.cleaned_data)
#             messages.success(self.request, message)
#         elif self.success_message: # If using the attribute
#             messages.success(self.request, self.success_message % self.object.__dict__) # May need get_full_name if that's what %(full_name)s expects

#         return redirect(success_url)

#         # OLD way that relies on ModelForm's default save within super().form_valid():
#         # return super().form_valid(form) 
#         # This would call form.save() again. If StudentParentLinkForm was a standard ModelForm
#         # with 'parents' in Meta.fields, this would be correct.
#         # But since you have a custom save() in the form, we call it directly.
    
    
    
    
# apps/students/views.py
class ParentUserListView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, ListView):
    model = ParentUser
    template_name = 'students/parentuser_list.html' # You'll create this
    context_object_name = 'parents'
    paginate_by = 20
    permission_required = 'students.view_parentuser' # Or a general user management perm

    def get_queryset(self):
        return ParentUser.objects.filter(is_active=True).order_by('last_name', 'first_name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Manage Parent Accounts"
        return context
    
    
    
# apps/students/views.py

from .forms import ParentUser # You'll need to create this ModelForm for ParentUser
from .forms import StudentForm, StudentFilterForm, ParentUserChangeForm, ParentUserCreationForm
# D:\school_fees_saas_v2\apps\students\views.py

# ... (Standard Django imports: CreateView, SuccessMessageMixin, messages, redirect, reverse, reverse_lazy, _)
# ... (Your custom mixins: StaffLoginRequiredMixin, TenantPermissionRequiredMixin)
# ... (Models: ParentUser, Student)
# ... (Forms: ParentUserCreationForm)
import logging # Ensure logging is imported
logger = logging.getLogger(__name__)

class ParentUserCreateView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, CreateView):
    model = ParentUser
    form_class = ParentUserCreationForm
    template_name = 'students/parentuser_form.html' # Ensure this template exists and is correctly structured
    permission_required = 'students.add_parentuser' # Make sure this permission exists and is assigned to relevant staff roles
    success_message = _("Parent account for '%(email)s' created successfully.")
    # login_url can be inherited from StaffLoginRequiredMixin if defined there, or set explicitly:
    # login_url = reverse_lazy('schools:staff_login')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Create New Parent Account")
        context['form_mode'] = "create" # Useful for the template to adapt (e.g., button text)

        student_pk_to_link = self.request.GET.get('student_pk_to_link')
        if student_pk_to_link:
            try:
                # Ensure student exists and belongs to the current tenant if relevant
                # For now, just fetching by PK. Add tenant check if Student has FK to tenant.
                context['linking_to_student'] = Student.objects.get(pk=student_pk_to_link)
                logger.debug(f"ParentUserCreateView: Context - Will attempt to link to student PK {student_pk_to_link}")
            except Student.DoesNotExist:
                logger.warning(f"ParentUserCreateView: Context - Student PK {student_pk_to_link} provided in GET params not found.")
                messages.warning(self.request, _("The student specified for linking could not be found."))
            except ValueError: # If student_pk_to_link is not a valid integer
                logger.warning(f"ParentUserCreateView: Context - Invalid Student PK '{student_pk_to_link}' provided in GET params.")
                messages.warning(self.request, _("Invalid student identifier provided for linking."))

        return context

    def form_valid(self, form):
        logger.info(
            f"ParentUserCreateView: Form IS VALID. Parent account for '{form.cleaned_data.get('email')}' "
            f"being CREATED by staff '{self.request.user.email}' in tenant '{self.request.tenant.schema_name}'."
        )
        # The form's save() method (specifically the overridden one in ParentUserCreationForm)
        # should handle password hashing and setting any default flags like is_active.
        self.object = form.save() # This will call ParentUserCreationForm.save()

        # SuccessMessageMixin will display self.success_message after redirect.
        # The auto-linking logic is now moved to get_success_url, which is cleaner as
        # form_valid should primarily focus on saving the main object.
        return super().form_valid(form) # This calls get_success_url indirectly

    def form_invalid(self, form):
        logger.warning(f"ParentUserCreateView: Form IS INVALID. Errors: {form.errors.as_json()}")
        messages.error(self.request, _("Please correct the errors below to create the parent account."))
        return super().form_invalid(form)


    def get_success_url(self):
        # self.object is the newly created ParentUser instance, set by form_valid
        next_url_param = self.request.GET.get('next')
        student_pk_to_link = self.request.GET.get('student_pk_to_link')

        # Auto-link student if parameters are present and valid
        if student_pk_to_link and self.object and self.object.pk:
            try:
                student_pk_int = int(student_pk_to_link) # Ensure it's an integer
                student_to_link = Student.objects.get(pk=student_pk_int)
                
                # Add parent to student's parents M2M
                # Assuming Student.parents is the M2M field to ParentUser
                student_to_link.parents.add(self.object)
                logger.info(f"ParentUserCreateView: Successfully auto-linked Parent '{self.object.email}' to Student '{student_to_link.get_full_name()}'.")
                messages.success(self.request, _(f"Parent '{self.object.email}' has been successfully linked to student '{student_to_link.get_full_name()}'."))
                
                # If a 'next' URL was provided (e.g., back to student_manage_parents), prioritize it
                if next_url_param:
                    logger.debug(f"ParentUserCreateView: Redirecting to 'next' URL after linking: {next_url_param}")
                    return next_url_param
                
                # Otherwise, redirect to the student's detail page after linking
                logger.debug(f"ParentUserCreateView: Redirecting to student detail page after linking: Student PK {student_to_link.pk}")
                return reverse('students:student_detail', kwargs={'pk': student_to_link.pk})

            except Student.DoesNotExist:
                messages.warning(self.request, _(f"Could not auto-link parent: Student with ID {student_pk_to_link} not found."))
                logger.warning(f"ParentUserCreateView: Student.DoesNotExist for PK {student_pk_to_link} during auto-linking.")
            except ValueError:
                messages.warning(self.request, _(f"Could not auto-link parent: Invalid student ID '{student_pk_to_link}'."))
                logger.warning(f"ParentUserCreateView: ValueError for student PK '{student_pk_to_link}' during auto-linking.")
            except Exception as e:
                messages.error(self.request, _(f"An error occurred while auto-linking parent to student: {e}"))
                logger.error(f"ParentUserCreateView: Error auto-linking Parent '{self.object.email}' to Student PK {student_pk_to_link}: {e}", exc_info=True)
        
        # If no specific student linking or 'next' URL, fall back to general 'next' or parent list
        if next_url_param:
            logger.debug(f"ParentUserCreateView: Redirecting to general 'next' URL: {next_url_param}")
            return next_url_param
            
        logger.debug("ParentUserCreateView: Fallback redirect to parent user list.")
        return reverse_lazy('students:parentuser_list') # Default fallback
    
    
    
    
    
from .forms import ParentUserChangeForm # Import the change form

# ... (Your existing Student views and ParentUserCreateView) ...

class ParentUserUpdateView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    model = ParentUser
    form_class = ParentUserChangeForm
    template_name = 'students/parentuser_form.html' # Re-use the same form template as create, or make a specific one
    context_object_name = 'parent_user_obj' # Name for the object in template context
    permission_required = 'students.change_parentuser' # Ensure this perm exists and is assigned
    success_message = _("Parent account '%(email)s' updated successfully.")

    # login_url can be inherited from StaffLoginRequiredMixin or set explicitly if needed
    # login_url = reverse_lazy('schools:staff_login') 

    def get_queryset(self):
        # Ensure we are fetching ParentUser from the current tenant's schema.
        # ListView/DetailView/UpdateView/DeleteView automatically filter by PK from URL,
        # but if ParentUser model itself doesn't have a tenant FK (common if it's a user model
        # placed in a tenant app), queries are naturally scoped by schema.
        # If ParentUser *does* have an FK to School (tenant), you'd filter here:
        # return super().get_queryset().filter(tenant=self.request.tenant)
        return super().get_queryset() # Relies on schema context

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # kwargs['tenant'] = self.request.tenant # Pass tenant to form if form's __init__ needs it
        return kwargs

    def get_success_url(self):
        # Redirect to a parent user list or detail page
        # For now, let's assume a parent user list view will exist
        # If you have a ParentUserDetailView:
        # return reverse('students:parentuser_detail', kwargs={'pk': self.object.pk})
        return reverse_lazy('students:parentuser_list') # Needs ParentUserListView

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # self.object is the ParentUser instance being edited
        context['view_title'] = _(f"Edit Parent Account: {self.object.get_full_name() or self.object.email}")
        context['form_mode'] = "update" # For template to adapt (e.g., button text)
        logger.debug(f"ParentUserUpdateView: Context prepared for editing ParentUser {self.object.pk}.")
        return context

    def form_valid(self, form):
        logger.info(f"ParentUser '{form.instance.email}' (PK: {form.instance.pk}) being UPDATED by user '{self.request.user.email}' in tenant '{self.request.tenant.schema_name}'.")
        # SuccessMessageMixin handles the message
        return super().form_valid(form)
    
    


