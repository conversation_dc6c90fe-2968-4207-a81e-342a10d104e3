{# D:\school_fees_saas_v2\templates\fees\fee_structure_form.html #}
{% extends "tenant_base.html" %}

{% load static %}
{# Load crispy_forms_tags if you prefer it, otherwise remove #}
{# {% load crispy_forms_tags %} #}

{% block title %}
    {{ view_title|default:"Manage Fee Structure" }} - {{ request.tenant.name }}
{% endblock %}

{% block extra_css %} {# Renamed from extra_tenant_css for consistency with base #}
    <style>
        .formset-item {
            border: 1px solid #e0e0e0;
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 0.25rem;
            background-color: #f9f9f9;
        }
        .formset-item legend {
            font-size: 1rem;
            font-weight: 500;
            margin-bottom: 0.75rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
        }
        /* Style the delete checkbox if present */
        .formset-item input[type="checkbox"][name$="-DELETE"] + label {
            color: #dc3545; /* Bootstrap danger color */
            font-weight: bold;
        }
    </style>
{% endblock %}


{% block content %}
<div class="container mt-4">
    <div class="row">
        {# Adjust column sizing for better responsiveness if needed #}
        <div class="col-lg-10 offset-lg-1">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h4 class="mb-0">{{ view_title }}</h4>
                </div>
                <div class="card-body p-4">
                    {% include "partials/_messages.html" %} {# Assuming you have this partial #}

                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <h5>Fee Structure Details</h5>
                        <hr class="mb-3">
                        
                        {# Main Form Fields - Example manual rendering for control #}
                        {% for field in form %}
                            {% if field.is_hidden %}
                                {{ field }}
                            {% else %}
                                <div class="mb-3 row">
                                    <label for="{{ field.id_for_label }}" class="col-sm-3 col-form-label text-sm-end">{{ field.label }}</label>
                                    <div class="col-sm-9">
                                        {% if field.name == 'is_active' %}
                                            <div class="form-check form-switch mt-2">
                                                {{ field }}
                                                <label class="form-check-label" for="{{ field.id_for_label }}"></label>
                                            </div>
                                        {% else %}
                                            {{ field }}
                                        {% endif %}
                                        {% if field.help_text %}
                                            <small class="form-text text-muted d-block mt-1">{{ field.help_text }}</small>
                                        {% endif %}
                                        {% for error in field.errors %}
                                            <div class="invalid-feedback d-block">{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                </div>
                            {% endif %}
                        {% endfor %}
                        {% for error in form.non_field_errors %}
                            <div class="alert alert-danger py-2">{{ error }}</div>
                        {% endfor %}


                        <h5 class="mt-4">Fee Structure Items</h5>
                        <hr class="mb-3">
                        
                        {# Formset Management Form - CRITICAL #}
                        {{ item_formset.management_form }}

                        {# Display Formset Non-Form Errors (e.g., min_num validation) #}
                        {% if item_formset.non_form_errors %}
                            <div class="alert alert-danger py-2">
                                {% for error in item_formset.non_form_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        
                        <div id="fee-items-formset-container"> {# Container for dynamic forms #}
                            {% for item_form in item_formset %}
                                <div class="formset-item" id="{{ item_form.prefix }}-row">
                                    {{ item_form.id }} {# Hidden ID field for existing items #}
                                    <fieldset>
                                        <legend class="small">
                                            Item #<span class="formset-num">{{ forloop.counter }}</span>
                                            {% if item_formset.can_delete and item_form.instance.pk %}
                                                <span class="float-end">
                                                    {{ item_form.DELETE.label_tag }} {{ item_form.DELETE }}
                                                </span>
                                            {% endif %}
                                        </legend>

                                        {% if item_form.non_field_errors %}
                                            <div class="alert alert-danger py-1">
                                                {% for error in item_form.non_field_errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}

                                        <div class="row g-3">
                                            <div class="col-md-5 mb-2">
                                                {{ item_form.fee_head.label_tag }}
                                                {{ item_form.fee_head }}
                                                {% for error in item_form.fee_head.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                            </div>
                                            <div class="col-md-3 mb-2">
                                                {{ item_form.amount.label_tag }}
                                                {{ item_form.amount }}
                                                {% for error in item_form.amount.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                            </div>
                                            <div class="col-md-4 mb-2">
                                                {{ item_form.concession_type.label_tag }}
                                                {{ item_form.concession_type }}
                                                {% for error in item_form.concession_type.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                            </div>
                                        </div>
                                        <div class="row g-3">
                                            <div class="col-md-12">
                                                <div class="form-check">
                                                    {{ item_form.is_optional }}
                                                    {{ item_form.is_optional.label_tag }}
                                                </div>
                                                {% for error in item_form.is_optional.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                            </div>
                                        </div>
                                    </fieldset>
                                </div>
                            {% endfor %}
                        </div> {# End fee-items-formset-container #}

                        <button type="button" id="add-item-form" class="btn btn-outline-secondary btn-sm mt-2 mb-3">
                            <i class="bi bi-plus-circle"></i> Add Another Fee Item
                        </button>
                        
                        <hr class="my-4">
                        <div class="d-flex justify-content-end">
                            <a href="{% url 'fees:fee_structure_list' %}" class="btn btn-outline-secondary me-2">Cancel</a>
                            <button type="submit" class="btn btn-success px-4">
                                {% if object %}Update{% else %}Save{% endif %} Fee Structure
                            </button>
                        </div>
                    </form>
                </div> {# End card-body #}
            </div> {# End card #}
        </div> {# End col #}
    </div> {# End row #}
</div> {# End container #}

{# Empty form template for JavaScript (hidden) #}
<div id="empty-item-form-template" style="display: none;">
    <div class="formset-item" id="items-__prefix__-row"> {# Use items prefix #}
        {{ item_formset.empty_form.id }}
        <fieldset>
            <legend class="small">New Item <span class="text-danger small" style="cursor:pointer;" onclick="this.closest('.formset-item').remove(); updateFormsetIndexes();">(Remove)</span></legend>
            <div class="row g-3">
                <div class="col-md-5 mb-2">
                    {{ item_formset.empty_form.fee_head.label_tag }}
                    {{ item_formset.empty_form.fee_head }}
                </div>
                <div class="col-md-3 mb-2">
                    {{ item_formset.empty_form.amount.label_tag }}
                    {{ item_formset.empty_form.amount }}
                </div>
                <div class="col-md-4 mb-2">
                    {{ item_formset.empty_form.concession_type.label_tag }}
                    {{ item_formset.empty_form.concession_type }}
                </div>
            </div>
            <div class="row g-3">
                <div class="col-md-12">
                    <div class="form-check">
                        {{ item_formset.empty_form.is_optional }}
                        {{ item_formset.empty_form.is_optional.label_tag }}
                    </div>
                </div>
            </div>
        </fieldset>
    </div>
</div>
{% endblock content %}

{% block page_specific_js %} {# Renamed from extra_js #}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const formsetContainer = document.getElementById('fee-items-formset-container');
    const addButton = document.getElementById('add-item-form');
    const emptyFormTemplateDiv = document.getElementById('empty-item-form-template');
    const totalFormsInput = document.querySelector('input[name="items-TOTAL_FORMS"]'); // Use prefix
    const formsetPrefix = 'items'; // Defined in view

    let formNum = parseInt(totalFormsInput.value); // Number of forms already rendered + empty ones (extra)

    if (addButton && emptyFormTemplateDiv && totalFormsInput) {
        addButton.addEventListener('click', function() {
            // Create a new div from the template's innerHTML
            let newFormHtml = emptyFormTemplateDiv.innerHTML;
            // Replace the prefix placeholder with the new form number
            newFormHtml = newFormHtml.replace(/__prefix__/g, formNum);
            
            // Create a temporary div to parse the HTML string
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = newFormHtml;
            
            // Append the actual formset item (which is the first child of the template div)
            const newFormElement = tempDiv.firstElementChild;
            if (newFormElement) {
                formsetContainer.appendChild(newFormElement);
                totalFormsInput.value = formNum + 1; // Increment total forms
                formNum++; // Increment for the next form to be added
            }
        });
    }
    
    // Function to update indexes if forms are removed (more advanced, but good for future)
    // This function would need to be called by the "(Remove)" link's onclick
    window.updateFormsetIndexes = function() {
        let currentForms = formsetContainer.querySelectorAll('.formset-item');
        formNum = currentForms.length; // Recalculate formNum based on visible forms
        totalFormsInput.value = formNum;
        
        currentForms.forEach((formDiv, index) => {
            // Update legend
            let legendNumSpan = formDiv.querySelector('legend .formset-num');
            if (legendNumSpan) legendNumSpan.textContent = index + 1;

            // Update IDs and names of inputs, selects, textareas within this formDiv
            formDiv.querySelectorAll('input, select, textarea, label').forEach(el => {
                ['id', 'name', 'for'].forEach(attr => {
                    if (el.hasAttribute(attr)) {
                        el.setAttribute(attr, el.getAttribute(attr).replace(/items-\d+-/g, `items-${index}-`));
                    }
                });
            });
            formDiv.id = `items-${index}-row`; // Update row id
        });
    };

    // Initial call to set numbers if using dynamic removal from start
    // updateFormsetIndexes(); // Call if needed, but not essential for add-only
});
</script>
{% endblock %}