{# templates/reporting/outstanding_fees_report.html #}
{% extends "tenant_base.html" %}

{% load humanize core_tags %} {# Ensure both are loaded #}

{% block title %}{{ view_title|default:"Outstanding Fees Report" }}{% endblock %}

{% block content %}

<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-collection-fill" %} {# Define icon for this specific report #}
        {% include "partials/_report_header.html" %}
    {% endwith %}
{% comment %} <div class="container-fluid mt-4"> {# Use fluid for wide table #}
    <h1>{{ view_title|default:"Outstanding Fees Report" }}</h1> {% endcomment %}

    {# --- Export Links --- #}
    <div class="mb-3">
        <a href="?{{ request.GET.urlencode }}&export=csv" class="btn btn-success btn-sm">
            <i class="bi bi-file-earmark-spreadsheet"></i> Export CSV
        </a>
        <a href="?{{ request.GET.urlencode }}&export=excel" class="btn btn-success btn-sm ms-1">
            <i class="bi bi-file-earmark-excel"></i> Export Excel
        </a>
        <a href="?{{ request.GET.urlencode }}&export=pdf" target="_blank" class="btn btn-danger btn-sm ms-1">
            <i class="bi bi-file-earmark-pdf"></i> Export PDF
        </a>
    </div>

    {# --- Filter Form --- #}
    <form method="get" class="filter-form card card-body bg-light mb-3 p-2">
        <div class="row gx-2 gy-2 align-items-end">
            <div class="col-auto">
                <label for="class_filter" class="col-form-label col-form-label-sm">Class:</label>
            </div>
            <div class="col-md-4 col-lg-3">
                {# Assumes view passes 'available_classes' #}
                <select name="class" id="class_filter" class="form-select form-select-sm">
                    <option value="">-- All Classes --</option>
                    {% for class_obj in available_classes %}
                        <option value="{{ class_obj.pk }}" {% if class_obj.pk|stringformat:"s" == current_class_filter %}selected{% endif %}>
                            {{ class_obj.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-auto">
                <label for="min_due_filter" class="col-form-label col-form-label-sm">Min Amt Due:</label>
            </div>
            <div class="col-md-2">
                {# Assumes view passes 'current_min_due_filter' #}
                <input type="number" step="0.01" min="0" name="min_due" id="min_due_filter" value="{{ current_min_due_filter|default:'' }}" class="form-control form-control-sm" placeholder="e.g., 10.00">
            </div>
            <div class="col-auto">
                <button type="submit" class="btn btn-primary btn-sm">
                    <i class="bi bi-funnel-fill"></i> Apply Filters
                </button>
            </div>
            {% if current_class_filter or current_min_due_filter %}
            <div class="col-auto">
                <a href="{% url 'reporting:outstanding_fees_report' %}" class="btn btn-secondary btn-sm">
                    <i class="bi bi-x-circle"></i> Clear Filters
                </a>
            </div>
            {% endif %}
        </div>
    </form>

    {# --- Summary Section --- #}
    {# Assumes view passes overall_total_due and students_report (queryset) #}
    <div class="alert alert-info" role="alert">
        <strong>Total Outstanding (Filtered): {{ school_profile.currency_symbol|default:'$' }}{{ overall_total_due|default:'0.00'|floatformat:2|intcomma }}</strong>
        ({{ students_report.count|default:0|intcomma }} Student{{ students_report.count|pluralize }} Found)
    </div>

    {# --- Report Table --- #}
    {% if students_report %}
        <div class="card shadow-sm">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped table-hover table-sm align-middle">
                        <thead class="table-light">
                            <tr>
                                {# Headers should match data below #}
                                <th>Student Name</th>
                                <th>Admission #</th>
                                <th>Class</th>
                                <th>Parent Name</th>
                                <th>Parent Phone</th>
                                <th class="text-end">Total Billed</th>
                                <th class="text-end">Total Discount</th>
                                <th class="text-end">Total Paid</th>
                                <th class="text-end">Amount Due Now</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for student in students_report %}
                            <tr>
                                <td><a href="{% url 'students:student_detail' student.pk %}">{{ student.full_name }}</a></td>
                                <td>{{ student.admission_number }}</td>
                                <td>{{ student.school_class.name|default:"-" }} {% if student.section %}- {{ student.section.name }}{% endif %}</td>
                                <td>{{ student.parent.full_name|default:"-" }}</td>
                                <td>{{ student.parent.phone_number|default:"-" }}</td>
                                {# --- Access CORRECT ANNOTATED fields --- #}
                                <td class="text-end">{{ student.total_billed_agg|currency:school_profile.currency_symbol }}</td>
                                <td class="text-end">
                                    {% if student.total_discount_agg and student.total_discount_agg > 0 %}
                                        {{ student.total_discount_agg|currency:school_profile.currency_symbol }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td class="text-end">{{ student.total_paid_agg|currency:school_profile.currency_symbol }}</td>
                                <td class="text-end fw-bold">{{ student.amount_due_calculated|currency:school_profile.currency_symbol }}</td>
                                {# --- End Annotated Fields --- #}
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-light">
                            <tr>
                                <td colspan="8" class="text-end fw-bold">Grand Total Outstanding:</td>
                                <td class="text-end fw-bold">{{ overall_total_due|currency:school_profile.currency_symbol }}</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
        {# Pagination placeholder #}
        {% comment %}
        {% if is_paginated %} <div class="mt-3"> {% include "includes/_pagination.html" with page_obj=page_obj query_params=request.GET.urlencode %} </div> {% endif %}
        {% endcomment %}
    {% else %}
        <div class="alert alert-warning">No students with outstanding fees found matching the criteria.</div>
        {% if current_class_filter or current_min_due_filter %}
            <p><a href="{% url 'reporting:outstanding_fees_report' %}" class="btn btn-secondary btn-sm">Show All Students</a></p>
        {% endif %}
    {% endif %}

    {# --- Back Button --- #}
    <div class="footer-actions mt-3">
        <a href="{% url 'schools:dashboard' %}" class="btn btn-secondary">
            <i class="bi bi-arrow-left-circle"></i> Back to Dashboard
        </a>
    </div>

</div> {# End container #}
{% endblock %}





{% comment %} {% extends "tenant_base.html" %}
{% load humanize static %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block extra_tenant_css %}
<style>
    .table th, .table td { vertical-align: middle; }
</style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="h3 mb-0 text-gray-800">{{ view_title }}</h1>
    </div>

    {% include "partials/_messages.html" %}

    {% include "./_report_filter_export_card.html" %}

    <div class="card shadow-sm">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Student Balances</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped table-hover" id="outstandingFeesTable" width="100%" cellspacing="0">
                    <thead class="table-light">
                        <tr>
                            <th>Admission No.</th>
                            <th>Student Name</th>
                            <th>Class</th>
                            <th>Section</th>
                            <th>Parent Name</th>
                            <th class="text-end">Outstanding Balance</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for student in students_report %} {# context_object_name #}
                        <tr>
                            <td>{{ student.admission_number }}</td>
                            <td><a href="#">{{ student.full_name }}</a></td> {# Link to student detail later #}
                            <td>{{ student.current_class.name|default:"N/A" }}</td>
                            <td>{{ student.current_section.name|default:"N/A" }}</td>
                            <td>{{ student.parent_profile.full_name|default:"N/A" }}</td>
                            <td class="text-end fw-bold">{{ student.outstanding_balance|default:"0.00"|floatformat:2|intcomma }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center py-4">No students found with outstanding fees matching your criteria.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    {% if students_report %}
                    <tfoot>
                        <tr class="table-light">
                            <th colspan="5" class="text-end fw-bold">Total Outstanding (All Filtered Pages):</th>
                            <th class="text-end fw-bolder fs-5">{{ total_outstanding_all_pages|default:"0.00"|floatformat:2|intcomma }}</th>
                        </tr>
                    </tfoot>
                    {% endif %}
                </table>
            </div>

            {% if is_paginated %}
                <nav aria-label="Page navigation">
                    <ul class="pagination pagination-sm justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item"><a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">« First</a></li>
                            <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">Previous</a></li>
                        {% endif %}
                        <li class="page-item disabled"><span class="page-link">Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}.</span></li>
                        {% if page_obj.has_next %}
                            <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">Next</a></li>
                            <li class="page-item"><a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">Last »</a></li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} {% endcomment %}











{% comment %} {% extends "tenant_base.html" %}
{% load static core_tags humanize %}

{% block title %}{{ view_title|default:"Outstanding Fees Report" }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>{{ view_title|default:"Outstanding Fees Report" }}</h1>
    <p class="lead">Report content will be displayed here.</p>
    {# Add filter form and table later #}
    <hr>
    <a href="{% url 'schools:dashboard' %}" class="btn btn-secondary">Back to Dashboard</a>
</div>
{% endblock %}
 {% endcomment %}
