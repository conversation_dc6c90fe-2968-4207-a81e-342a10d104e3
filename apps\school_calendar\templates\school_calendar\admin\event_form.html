{% extends "base.html" %}
{% load static i18n widget_tweaks %}

{% block title %}
    {% if object %}
        {% trans "Edit Event" %} - {{ object.title }}
    {% else %}
        {% trans "Create New Event" %}
    {% endif %}
{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .section-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #e9ecef;
    }
    
    .form-check-group {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .time-inputs {
        display: none;
    }
    
    .time-inputs.show {
        display: block;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'school_calendar:calendar' %}">{% trans "Calendar" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'school_calendar:admin_event_list' %}">{% trans "Manage Events" %}</a></li>
            <li class="breadcrumb-item active">
                {% if object %}{% trans "Edit Event" %}{% else %}{% trans "Create Event" %}{% endif %}
            </li>
        </ol>
    </nav>
    
    <div class="row">
        <div class="col-lg-8">
            <form method="post" novalidate>
                {% csrf_token %}
                
                <!-- Basic Information -->
                <div class="form-section">
                    <h4 class="section-title">
                        <i class="bi bi-info-circle me-2"></i>
                        {% trans "Basic Information" %}
                    </h4>
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label for="{{ form.title.id_for_label }}" class="form-label">
                                {{ form.title.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.title|add_class:"form-control" }}
                            {% if form.title.errors %}
                                <div class="text-danger small mt-1">{{ form.title.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                {{ form.description.label }}
                            </label>
                            {{ form.description|add_class:"form-control" }}
                            {% if form.description.errors %}
                                <div class="text-danger small mt-1">{{ form.description.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.category.id_for_label }}" class="form-label">
                                {{ form.category.label }}
                            </label>
                            {{ form.category|add_class:"form-select" }}
                            {% if form.category.errors %}
                                <div class="text-danger small mt-1">{{ form.category.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.event_type.id_for_label }}" class="form-label">
                                {{ form.event_type.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.event_type|add_class:"form-select" }}
                            {% if form.event_type.errors %}
                                <div class="text-danger small mt-1">{{ form.event_type.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.priority.id_for_label }}" class="form-label">
                                {{ form.priority.label }}
                            </label>
                            {{ form.priority|add_class:"form-select" }}
                            {% if form.priority.errors %}
                                <div class="text-danger small mt-1">{{ form.priority.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Date and Time -->
                <div class="form-section">
                    <h4 class="section-title">
                        <i class="bi bi-calendar-event me-2"></i>
                        {% trans "Date & Time" %}
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.start_date.id_for_label }}" class="form-label">
                                {{ form.start_date.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.start_date|add_class:"form-control" }}
                            {% if form.start_date.errors %}
                                <div class="text-danger small mt-1">{{ form.start_date.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.end_date.id_for_label }}" class="form-label">
                                {{ form.end_date.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.end_date|add_class:"form-control" }}
                            {% if form.end_date.errors %}
                                <div class="text-danger small mt-1">{{ form.end_date.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-12 mb-3">
                            <div class="form-check">
                                {{ form.is_all_day|add_class:"form-check-input" }}
                                <label for="{{ form.is_all_day.id_for_label }}" class="form-check-label">
                                    {{ form.is_all_day.label }}
                                </label>
                            </div>
                        </div>
                        
                        <div class="time-inputs {% if not form.is_all_day.value %}show{% endif %}">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.start_time.id_for_label }}" class="form-label">
                                        {{ form.start_time.label }}
                                    </label>
                                    {{ form.start_time|add_class:"form-control" }}
                                    {% if form.start_time.errors %}
                                        <div class="text-danger small mt-1">{{ form.start_time.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.end_time.id_for_label }}" class="form-label">
                                        {{ form.end_time.label }}
                                    </label>
                                    {{ form.end_time|add_class:"form-control" }}
                                    {% if form.end_time.errors %}
                                        <div class="text-danger small mt-1">{{ form.end_time.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Location -->
                <div class="form-section">
                    <h4 class="section-title">
                        <i class="bi bi-geo-alt me-2"></i>
                        {% trans "Location" %}
                    </h4>
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label for="{{ form.location.id_for_label }}" class="form-label">
                                {{ form.location.label }}
                            </label>
                            {{ form.location|add_class:"form-control" }}
                            {% if form.location.errors %}
                                <div class="text-danger small mt-1">{{ form.location.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ form.venue_details.id_for_label }}" class="form-label">
                                {{ form.venue_details.label }}
                            </label>
                            {{ form.venue_details|add_class:"form-control" }}
                            {% if form.venue_details.errors %}
                                <div class="text-danger small mt-1">{{ form.venue_details.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Visibility & Permissions -->
                <div class="form-section">
                    <h4 class="section-title">
                        <i class="bi bi-eye me-2"></i>
                        {% trans "Visibility & Permissions" %}
                    </h4>
                    
                    <div class="form-check-group">
                        <div class="form-check">
                            {{ form.is_public|add_class:"form-check-input" }}
                            <label for="{{ form.is_public.id_for_label }}" class="form-check-label">
                                {{ form.is_public.label }}
                            </label>
                        </div>
                        
                        <div class="form-check">
                            {{ form.visible_to_parents|add_class:"form-check-input" }}
                            <label for="{{ form.visible_to_parents.id_for_label }}" class="form-check-label">
                                {{ form.visible_to_parents.label }}
                            </label>
                        </div>
                        
                        <div class="form-check">
                            {{ form.visible_to_staff|add_class:"form-check-input" }}
                            <label for="{{ form.visible_to_staff.id_for_label }}" class="form-check-label">
                                {{ form.visible_to_staff.label }}
                            </label>
                        </div>
                        
                        <div class="form-check">
                            {{ form.visible_to_students|add_class:"form-check-input" }}
                            <label for="{{ form.visible_to_students.id_for_label }}" class="form-check-label">
                                {{ form.visible_to_students.label }}
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- RSVP & Contact -->
                <div class="form-section">
                    <h4 class="section-title">
                        <i class="bi bi-person-check me-2"></i>
                        {% trans "RSVP & Contact Information" %}
                    </h4>
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <div class="form-check">
                                {{ form.requires_rsvp|add_class:"form-check-input" }}
                                <label for="{{ form.requires_rsvp.id_for_label }}" class="form-check-label">
                                    {{ form.requires_rsvp.label }}
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.max_attendees.id_for_label }}" class="form-label">
                                {{ form.max_attendees.label }}
                            </label>
                            {{ form.max_attendees|add_class:"form-control" }}
                            <div class="form-text">{{ form.max_attendees.help_text }}</div>
                            {% if form.max_attendees.errors %}
                                <div class="text-danger small mt-1">{{ form.max_attendees.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.contact_person.id_for_label }}" class="form-label">
                                {{ form.contact_person.label }}
                            </label>
                            {{ form.contact_person|add_class:"form-control" }}
                            {% if form.contact_person.errors %}
                                <div class="text-danger small mt-1">{{ form.contact_person.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.contact_email.id_for_label }}" class="form-label">
                                {{ form.contact_email.label }}
                            </label>
                            {{ form.contact_email|add_class:"form-control" }}
                            {% if form.contact_email.errors %}
                                <div class="text-danger small mt-1">{{ form.contact_email.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.contact_phone.id_for_label }}" class="form-label">
                                {{ form.contact_phone.label }}
                            </label>
                            {{ form.contact_phone|add_class:"form-control" }}
                            {% if form.contact_phone.errors %}
                                <div class="text-danger small mt-1">{{ form.contact_phone.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Form Actions -->
                <div class="form-section">
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'school_calendar:admin_event_list' %}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left me-1"></i>{% trans "Cancel" %}
                        </a>
                        
                        <div>
                            {% if object %}
                            <a href="{% url 'school_calendar:event_detail' object.pk %}" class="btn btn-outline-primary me-2">
                                <i class="bi bi-eye me-1"></i>{% trans "View Event" %}
                            </a>
                            {% endif %}
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg me-1"></i>
                                {% if object %}{% trans "Update Event" %}{% else %}{% trans "Create Event" %}{% endif %}
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="bi bi-lightbulb me-2"></i>
                        {% trans "Tips" %}
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success me-2"></i>
                            {% trans "Use clear, descriptive titles" %}
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success me-2"></i>
                            {% trans "Include location details for better attendance" %}
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success me-2"></i>
                            {% trans "Set appropriate visibility for your audience" %}
                        </li>
                        <li class="mb-0">
                            <i class="bi bi-check-circle text-success me-2"></i>
                            {% trans "Enable RSVP for events with limited capacity" %}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const allDayCheckbox = document.getElementById('{{ form.is_all_day.id_for_label }}');
    const timeInputs = document.querySelector('.time-inputs');
    
    if (allDayCheckbox && timeInputs) {
        allDayCheckbox.addEventListener('change', function() {
            if (this.checked) {
                timeInputs.classList.remove('show');
                // Clear time values
                const timeFields = timeInputs.querySelectorAll('input[type="time"]');
                timeFields.forEach(field => field.value = '');
            } else {
                timeInputs.classList.add('show');
            }
        });
    }
    
    // Auto-set end date to start date if not set
    const startDateField = document.getElementById('{{ form.start_date.id_for_label }}');
    const endDateField = document.getElementById('{{ form.end_date.id_for_label }}');
    
    if (startDateField && endDateField) {
        startDateField.addEventListener('change', function() {
            if (!endDateField.value) {
                endDateField.value = this.value;
            }
        });
    }
});
</script>
{% endblock %}
