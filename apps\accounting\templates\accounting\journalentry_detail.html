{# D:\school_fees_saas_v2\apps\accounting\templates\accounting\journalentry_detail.html #}
{% extends "tenant_base.html" %}
{% load static i18n humanize %}

{% block tenant_page_title %}{{ view_title|default:_("Journal Entry Details") }}{% endblock tenant_page_title %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        .je-detail-header { margin-bottom: 1.5rem; }
        .je-detail-dl dt { font-weight: 600; }
        .status-badge-je-draft { background-color: var(--bs-warning); color: var(--bs-dark) !important; }
        .status-badge-je-posted { background-color: var(--bs-success); }
        /* Align action buttons better if they wrap */
        .action-buttons > * { margin-bottom: 0.5rem !important; } 
        .action-buttons > *:not(:last-child) { margin-right: 0.5rem !important; }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="d-flex flex-wrap justify-content-between align-items-center mb-4 je-detail-header"> {# Added flex-wrap #}
    <div class="mb-2 mb-md-0"> {# Margin bottom for smaller screens if buttons wrap below #}
        <h1 class="display-6 mb-0">{{ view_title }}</h1>
        <small class="text-muted">{% trans "JE ID" %}: JE-{{ journal_entry.pk|stringformat:"05d" }}</small>
    </div>
    <div class="action-buttons text-md-end"> {# Wrapper for buttons, text-md-end for larger screens #}
        {% if can_post %} {# can_post is from view context #}
        <form method="post" action="{% url 'accounting:journalentry_post' pk=journal_entry.pk %}" class="d-inline needs-confirmation" data-confirm-message="{% trans 'Are you sure you want to post this journal entry? This action affects account balances and cannot be easily undone.' %}">
            {% csrf_token %}
            <button type="submit" class="btn btn-success">
                <i class="bi bi-check-circle-fill me-1"></i> {% trans "Post Journal Entry" %}
            </button>
        </form>
        {% endif %}

        {% if can_edit_draft %} {# can_edit_draft from view context #}
            <a href="{% url 'accounting:journalentry_update' pk=journal_entry.pk %}" class="btn btn-primary"><i class="bi bi-pencil-fill me-1"></i> {% trans "Edit Draft" %}</a>
        {% endif %}

        {# --- ADDED DELETE DRAFT BUTTON --- #}
        {% if can_delete_draft %} {# can_delete_draft from view context #}
            <a href="{% url 'accounting:journalentry_delete' pk=journal_entry.pk %}" 
                class="btn btn-danger" 
                title="{% trans 'Delete this Draft Journal Entry' %}">
                <i class="bi bi-trash-fill me-1"></i> {% trans "Delete Draft" %}
            </a>
        {% endif %}
        {# --- END OF ADDED DELETE DRAFT BUTTON --- #}
        
        {% if journal_entry.can_be_reversed and perms.accounting.add_journalentry %} {# Use the model method #}
                <a href="{% url 'accounting:journalentry_confirm_reverse' pk=journal_entry.pk %}" 
                    class="btn btn-warning ms-2" 
                    title="{% trans 'Reverse this Posted Journal Entry' %}">
                    <i class="bi bi-arrow-repeat me-1"></i> {% trans "Reverse Entry" %}
                </a>
            {% endif %}
            {% if journal_entry.reversed_by_entry %}
                <span class="ms-2 badge bg-info">{% blocktrans with rev_pk=journal_entry.reversed_by_entry.pk|stringformat:"05d" %}Reversed by <a href="{% url 'accounting:journalentry_detail' pk=journal_entry.reversed_by_entry.pk %}" class="text-white">JE-{{ rev_pk }}</a>{% endblocktrans %}</span>
            {% endif %}
            {% if journal_entry.is_reversing_entry and journal_entry.reverses_entry %}
                <span class="ms-2 badge bg-info">{% blocktrans with orig_pk=journal_entry.reverses_entry.pk|stringformat:"05d" %}Reversal of <a href="{% url 'accounting:journalentry_detail' pk=journal_entry.reverses_entry.pk %}" class="text-white">JE-{{ orig_pk }}</a>{% endblocktrans %}</span>
            {% endif %}

        <a href="{% url 'accounting:journalentry_list' %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left-circle me-1"></i> {% trans "Back to List" %}
        </a>
    </div>
</div>

{# ... rest of your card for Entry Summary and card for Journal Entry Lines ... #}
{# (No changes needed for the content below this point from your provided template) #}

<div class="card shadow-sm mb-4">
    <div class="card-header">
        <h5 class="mb-0">{% trans "Entry Summary" %}</h5>
    </div>
    <div class="card-body">
        <dl class="row je-detail-dl">
            <dt class="col-sm-3">{% trans "Date" %}</dt>
            <dd class="col-sm-9">{{ journal_entry.date|date:"D, d M Y" }}</dd>

            <dt class="col-sm-3">{% trans "Description" %}</dt>
            <dd class="col-sm-9">{{ journal_entry.description|linebreaksbr }}</dd>

            <dt class="col-sm-3">{% trans "Reference No." %}</dt>
            <dd class="col-sm-9">{{ journal_entry.reference_number|default:"N/A" }}</dd>

            <dt class="col-sm-3">{% trans "Entry Type" %}</dt>
            <dd class="col-sm-9">{{ journal_entry.get_entry_type_display }}</dd>

            <dt class="col-sm-3">{% trans "Status" %}</dt>
            <dd class="col-sm-9">
                <span class="badge fs-6 rounded-pill status-badge-je-{{ journal_entry.status|lower }}">
                    {{ journal_entry.get_status_display }}
                </span>
            </dd>

            <dt class="col-sm-3">{% trans "Created By" %}</dt>
            <dd class="col-sm-9">{{ journal_entry.created_by.get_full_name|default:journal_entry.created_by.email|default:"System" }}</dd>
            
            <dt class="col-sm-3">{% trans "Created At" %}</dt>
            <dd class="col-sm-9">{{ journal_entry.created_at|date:"d M Y, H:i:s" }}</dd>

            {% if journal_entry.last_modified_by %}
            <dt class="col-sm-3">{% trans "Last Modified By" %}</dt>
            <dd class="col-sm-9">{{ journal_entry.last_modified_by.get_full_name|default:journal_entry.last_modified_by.email }}</dd>
            <dt class="col-sm-3">{% trans "Last Modified At" %}</dt>
            <dd class="col-sm-9">{{ journal_entry.updated_at|date:"d M Y, H:i:s" }}</dd>
            {% endif %}
        </dl>
    </div>
</div>

<div class="card shadow-sm">
    <div class="card-header">
        <h5 class="mb-0">{% trans "Journal Entry Lines" %}</h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-striped table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>{% trans "Account Code" %}</th>
                        <th>{% trans "Account Name" %}</th>
                        <th>{% trans "Line Description" %}</th>
                        <th class="text-end">{% trans "Debit" %}</th>
                        <th class="text-end">{% trans "Credit" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for line in journal_entry.lines.all %}
                    <tr>
                        <td>{{ line.account.account_code|default_if_none:"" }}</td>
                        <td>{{ line.account.name }}</td>
                        <td>{{ line.description|default:"" }}</td>
                        <td class="text-end">{{ school_profile.currency_symbol|default:"$" }}{{ line.debit_amount|floatformat:2|intcomma }}</td>
                        <td class="text-end">{{ school_profile.currency_symbol|default:"$" }}{{ line.credit_amount|floatformat:2|intcomma }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="text-center text-muted">{% trans "No lines found for this journal entry." %}</td>
                    </tr>
                    {% endfor %}
                </tbody>
                {% if journal_entry.lines.all %}
                <tfoot class="table-group-divider">
                    <tr class="table-secondary fw-bold">
                        <td colspan="3" class="text-end">{% trans "Totals:" %}</td>
                        <td class="text-end">{{ school_profile.currency_symbol|default:"$" }}{{ journal_entry.total_debits|floatformat:2|intcomma }}</td>
                        <td class="text-end">{{ school_profile.currency_symbol|default:"$" }}{{ journal_entry.total_credits|floatformat:2|intcomma }}</td>
                    </tr>
                    {% if not journal_entry.is_balanced %}
                    <tr class="table-danger fw-bold">
                        <td colspan="3" class="text-end text-danger">{% trans "Difference (Out of Balance):" %}</td>
                        <td colspan="2" class="text-center text-danger">
                            {{ school_profile.currency_symbol|default:"$" }}{{ journal_entry.total_debits|subtract:journal_entry.total_credits|floatformat:2|intcomma }}
                        </td>
                    </tr>
                    {% endif %}
                </tfoot>
                {% endif %}
            </table>
        </div>
    </div>
{% endblock tenant_specific_content %}

{% block extra_tenant_js %}
{{ block.super }}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const confirmationForms = document.querySelectorAll('.needs-confirmation');
        confirmationForms.forEach(form => {
            form.addEventListener('submit', function (event) {
                const message = event.target.dataset.confirmMessage || '{% trans "Are you sure you want to proceed?" %}'; // Added trans here
                if (!confirm(message)) {
                    event.preventDefault();
                }
            });
        });
    });
</script>
{% endblock %}












