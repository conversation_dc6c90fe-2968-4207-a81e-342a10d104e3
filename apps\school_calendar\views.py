from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib import messages
from django.urls import reverse_lazy, reverse
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse
from django.utils import timezone
from django.core.paginator import Paginator
from datetime import datetime, date, timedelta
import calendar
import json

from .models import SchoolEvent, EventCategory, EventAttendee
from .forms import SchoolEventForm, EventCategoryForm


class StaffRequiredMixin(UserPassesTestMixin):
    """Mixin to require staff permissions"""
    def test_func(self):
        return self.request.user.is_authenticated and (
            self.request.user.is_staff or 
            hasattr(self.request.user, 'is_school_admin') and self.request.user.is_school_admin
        )


# Public Calendar Views (for all users)
class CalendarView(LoginRequiredMixin, ListView):
    """Main calendar view showing monthly calendar"""
    model = SchoolEvent
    template_name = 'school_calendar/calendar.html'
    context_object_name = 'events'
    
    def get_queryset(self):
        year = int(self.request.GET.get('year', timezone.now().year))
        month = int(self.request.GET.get('month', timezone.now().month))
        return SchoolEvent.get_events_for_month(year, month, self.request.user)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get current year and month
        year = int(self.request.GET.get('year', timezone.now().year))
        month = int(self.request.GET.get('month', timezone.now().month))
        
        # Create calendar
        cal = calendar.monthcalendar(year, month)
        month_name = calendar.month_name[month]
        
        # Get events for the month
        events = self.get_queryset()
        
        # Organize events by date
        events_by_date = {}
        for event in events:
            # Handle multi-day events
            current_date = event.start_date
            while current_date <= event.end_date:
                if current_date not in events_by_date:
                    events_by_date[current_date] = []
                events_by_date[current_date].append(event)
                current_date += timedelta(days=1)
        
        # Navigation dates
        prev_month = month - 1 if month > 1 else 12
        prev_year = year if month > 1 else year - 1
        next_month = month + 1 if month < 12 else 1
        next_year = year if month < 12 else year + 1
        
        context.update({
            'calendar': cal,
            'year': year,
            'month': month,
            'month_name': month_name,
            'events_by_date': events_by_date,
            'prev_year': prev_year,
            'prev_month': prev_month,
            'next_year': next_year,
            'next_month': next_month,
            'today': timezone.now().date(),
            'today': timezone.now().date(),
            'categories': EventCategory.objects.filter(is_active=True),
            'can_manage': self.request.user.is_staff or (
                hasattr(self.request.user, 'is_school_admin') and 
                self.request.user.is_school_admin
            )
        })
        
        return context


class EventListView(LoginRequiredMixin, ListView):
    """List view of events with filtering"""
    model = SchoolEvent
    template_name = 'school_calendar/event_list.html'
    context_object_name = 'events'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = SchoolEvent.objects.filter(is_active=True)
        
        # Apply user-based filtering
        user = self.request.user
        if hasattr(user, 'is_staff') and user.is_staff:
            queryset = queryset.filter(visible_to_staff=True)
        elif hasattr(user, 'is_parent') and user.is_parent:
            queryset = queryset.filter(visible_to_parents=True)
        else:
            queryset = queryset.filter(is_public=True)
        
        # Apply filters
        event_type = self.request.GET.get('type')
        if event_type:
            queryset = queryset.filter(event_type=event_type)
        
        category = self.request.GET.get('category')
        if category:
            queryset = queryset.filter(category_id=category)
        
        # Date filtering
        date_filter = self.request.GET.get('date_filter')
        today = timezone.now().date()
        
        if date_filter == 'upcoming':
            queryset = queryset.filter(start_date__gte=today)
        elif date_filter == 'past':
            queryset = queryset.filter(end_date__lt=today)
        elif date_filter == 'this_month':
            start_of_month = today.replace(day=1)
            if today.month == 12:
                end_of_month = date(today.year + 1, 1, 1) - timedelta(days=1)
            else:
                end_of_month = date(today.year, today.month + 1, 1) - timedelta(days=1)
            queryset = queryset.filter(
                start_date__gte=start_of_month,
                start_date__lte=end_of_month
            )
        
        return queryset.select_related('category', 'created_by').order_by('start_date', 'start_time')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'event_types': SchoolEvent.EVENT_TYPES,
            'categories': EventCategory.objects.filter(is_active=True),
            'current_filters': {
                'type': self.request.GET.get('type', ''),
                'category': self.request.GET.get('category', ''),
                'date_filter': self.request.GET.get('date_filter', ''),
            },
            'can_manage': self.request.user.is_staff or (
                hasattr(self.request.user, 'is_school_admin') and 
                self.request.user.is_school_admin
            )
        })
        return context


class EventDetailView(LoginRequiredMixin, DetailView):
    """Detailed view of a single event"""
    model = SchoolEvent
    template_name = 'school_calendar/event_detail.html'
    context_object_name = 'event'
    
    def get_queryset(self):
        queryset = SchoolEvent.objects.filter(is_active=True)
        
        # Apply user-based filtering
        user = self.request.user
        if hasattr(user, 'is_staff') and user.is_staff:
            queryset = queryset.filter(visible_to_staff=True)
        elif hasattr(user, 'is_parent') and user.is_parent:
            queryset = queryset.filter(visible_to_parents=True)
        else:
            queryset = queryset.filter(is_public=True)
        
        return queryset.select_related('category', 'created_by')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        event = self.object
        
        # Check if user has RSVP'd
        user_rsvp = None
        if event.requires_rsvp and self.request.user.is_authenticated:
            try:
                user_rsvp = EventAttendee.objects.get(
                    event=event, 
                    user=self.request.user
                )
            except EventAttendee.DoesNotExist:
                pass
        
        # Get attendee counts
        attendee_counts = {}
        if event.requires_rsvp:
            attendee_counts = {
                'attending': event.attendees.filter(rsvp_status='ATTENDING').count(),
                'not_attending': event.attendees.filter(rsvp_status='NOT_ATTENDING').count(),
                'maybe': event.attendees.filter(rsvp_status='MAYBE').count(),
                'pending': event.attendees.filter(rsvp_status='PENDING').count(),
            }
        
        context.update({
            'user_rsvp': user_rsvp,
            'attendee_counts': attendee_counts,
            'can_manage': self.request.user.is_staff or (
                hasattr(self.request.user, 'is_school_admin') and 
                self.request.user.is_school_admin
            )
        })
        return context


# Admin Views (for staff only)
class EventCreateView(StaffRequiredMixin, CreateView):
    """Create new event (staff only)"""
    model = SchoolEvent
    form_class = SchoolEventForm
    template_name = 'school_calendar/admin/event_form.html'
    success_url = reverse_lazy('school_calendar:admin_event_list')
    
    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(self.request, _('Event created successfully!'))
        return super().form_valid(form)


class EventUpdateView(StaffRequiredMixin, UpdateView):
    """Update event (staff only)"""
    model = SchoolEvent
    form_class = SchoolEventForm
    template_name = 'school_calendar/admin/event_form.html'
    success_url = reverse_lazy('school_calendar:admin_event_list')
    
    def form_valid(self, form):
        messages.success(self.request, _('Event updated successfully!'))
        return super().form_valid(form)


class EventDeleteView(StaffRequiredMixin, DeleteView):
    """Delete event (staff only)"""
    model = SchoolEvent
    template_name = 'school_calendar/admin/event_confirm_delete.html'
    success_url = reverse_lazy('school_calendar:admin_event_list')
    
    def delete(self, request, *args, **kwargs):
        messages.success(request, _('Event deleted successfully!'))
        return super().delete(request, *args, **kwargs)


class AdminEventListView(StaffRequiredMixin, ListView):
    """Admin list view of all events"""
    model = SchoolEvent
    template_name = 'school_calendar/admin/event_list.html'
    context_object_name = 'events'
    paginate_by = 25
    
    def get_queryset(self):
        return SchoolEvent.objects.all().select_related(
            'category', 'created_by'
        ).order_by('-created_at')


# RSVP Views
@login_required
def rsvp_event(request, pk):
    """Handle RSVP for an event"""
    event = get_object_or_404(SchoolEvent, pk=pk, is_active=True, requires_rsvp=True)
    
    if request.method == 'POST':
        rsvp_status = request.POST.get('rsvp_status')
        notes = request.POST.get('notes', '')
        
        if rsvp_status in ['ATTENDING', 'NOT_ATTENDING', 'MAYBE']:
            attendee, created = EventAttendee.objects.get_or_create(
                event=event,
                user=request.user,
                defaults={'rsvp_status': rsvp_status, 'notes': notes}
            )
            
            if not created:
                attendee.rsvp_status = rsvp_status
                attendee.notes = notes
                attendee.save()
            
            messages.success(request, _('Your RSVP has been recorded!'))
        else:
            messages.error(request, _('Invalid RSVP status.'))
    
    return redirect('school_calendar:event_detail', pk=event.pk)


# API Views for AJAX
@login_required
def events_json(request):
    """Return events as JSON for calendar widgets"""
    year = int(request.GET.get('year', timezone.now().year))
    month = int(request.GET.get('month', timezone.now().month))
    
    events = SchoolEvent.get_events_for_month(year, month, request.user)
    
    events_data = []
    for event in events:
        events_data.append({
            'id': event.id,
            'title': event.title,
            'start': event.start_date.isoformat(),
            'end': event.end_date.isoformat(),
            'allDay': event.is_all_day,
            'color': event.category.color if event.category else '#007bff',
            'url': reverse('school_calendar:event_detail', args=[event.pk])
        })
    
    return JsonResponse(events_data, safe=False)
