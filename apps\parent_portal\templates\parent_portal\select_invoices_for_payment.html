{# D:\school_fees_saas_v2\apps\parent_portal\templates\parent_portal\select_invoices_for_payment.html #}
{% extends "parent_portal/parent_portal_base.html" %}
{% load static i18n humanize %}

{% block parent_portal_page_title %}{{ view_title|default:_("Pay School Fees") }}{% endblock %}

{% block extra_parent_portal_css %}
    {{ block.super }}
    <style>
        .invoice-selection-item {
            border: 1px solid #eee;
            border-radius: .375rem; /* Bootstrap's default border-radius */
            padding: 1rem 1.25rem;
            margin-bottom: 1rem;
            background-color: #fff;
            transition: box-shadow .15s ease-in-out;
        }
        .invoice-selection-item:hover {
            box-shadow: 0 .25rem .75rem rgba(0,0,0,.075);
        }
        .invoice-selection-item label {
            display: flex; /* Use flex to align checkbox and content */
            align-items: center;
            width: 100%;
            cursor: pointer;
        }
        .invoice-selection-item input[type="checkbox"] {
            flex-shrink: 0; /* Prevent checkbox from shrinking */
            margin-right: 1rem;
            transform: scale(1.3); /* Make checkbox slightly larger */
        }
        .invoice-details {
            flex-grow: 1; /* Allow details to take remaining space */
        }
        .invoice-amount {
            font-weight: bold;
            font-size: 1.1em;
        }
        .total-payment-summary {
            position: sticky;
            top: 80px; /* Adjust based on your sticky navbar height */
            z-index: 100;
            background-color: #f8f9fa;
            padding: 1.5rem;
            border-radius: .375rem;
            box-shadow: 0 .5rem 1rem rgba(0,0,0,.1);
        }
    </style>
{% endblock %}

{% block parent_portal_main_content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-6 mb-0">{{ view_title }}</h1>
        <a href="{% url 'parent_portal:dashboard' %}" class="btn btn-sm btn-outline-secondary">
            <i class="bi bi-arrow-left-circle me-1"></i>{% trans "Back to Dashboard" %}
        </a>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <form method="post">
        {% csrf_token %}
        <div class="row">
            <div class="col-lg-8">
                {% if form.selected_invoices.field.queryset.exists %}
                    <p class="text-muted">{% trans "Please select the invoices you wish to pay from the list below." %}</p>
                    {% for invoice_choice in form.selected_invoices %} {# Iterate through choices for custom rendering #}
                        <div class="invoice-selection-item">
                            <label for="{{ invoice_choice.id_for_label }}">
                                {{ invoice_choice.tag }} {# This renders the <input type="checkbox"> #}
                                <div class="invoice-details ms-2"> {# Added ms-2 for spacing from checkbox #}
                                    <div class="d-flex justify-content-between">
                                        <strong>{{ invoice_choice.choice_label.invoice_number }} - {{ invoice_choice.choice_label.student.full_name }}</strong>
                                        <span class="invoice-amount text-danger">
                                            {{ school_profile.currency_symbol|default:"$" }}{{ invoice_choice.choice_label.balance_due|floatformat:2|intcomma }}
                                        </span>
                                    </div>
                                    <small class="text-muted d-block">
                                        {% trans "Issued" %}: {{ invoice_choice.choice_label.issue_date|date:"d M Y" }} |
                                        {% trans "Due" %}: {{ invoice_choice.choice_label.due_date|date:"d M Y" }}
                                    </small>
                                    <small class="text-muted d-block">{{ invoice_choice.choice_label.description|truncatechars:80 }}</small>
                                </div>
                            </label>
                        </div>
                    {% endfor %}
                    {% if form.selected_invoices.errors %}
                        <div class="alert alert-danger mt-2">
                            {% for error in form.selected_invoices.errors %} {{ error }} {% endfor %}
                        </div>
                    {% endif %}
                {% else %}
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle-fill me-2"></i>{% trans "You have no outstanding invoices at this time." %}
                    </div>
                {% endif %}
            </div>

            <div class="col-lg-4">
                <div class="total-payment-summary">
                    <h5 class="mb-3">{% trans "Payment Summary" %}</h5>
                    <div class="d-flex justify-content-between mb-2">
                        <span>{% trans "Number of Invoices Selected:" %}</span>
                        <strong id="selected-count">0</strong>
                    </div>
                    <div class="d-flex justify-content-between mb-3">
                        <span>{% trans "Total Amount to Pay:" %}</span>
                        <strong id="total-amount">{{ school_profile.currency_symbol|default:"$" }}0.00</strong>
                    </div>
                    <button type="submit" class="btn btn-success btn-lg w-100" id="proceed-to-payment-btn" disabled>
                        <i class="bi bi-lock-fill me-2"></i>{% trans "Proceed to Payment" %}
                    </button>
                    {% for error in form.non_field_errors %}
                        <div class="alert alert-danger mt-2">{{ error }}</div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock parent_portal_main_content %}

{% block extra_parent_portal_js %}
    {{ block.super }}
    <script src="https://cdn.jsdelivr.net/npm/decimal.js@10.4.3/decimal.min.js"></script> 
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const checkboxes = document.querySelectorAll('input[name="selected_invoices"]');
        const selectedCountEl = document.getElementById('selected-count');
        const totalAmountEl = document.getElementById('total-amount');
        const proceedButton = document.getElementById('proceed-to-payment-btn');
        const currencySymbol = "{{ school_profile.currency_symbol|default:'$'|escapejs }}";

        function updateTotalSummary() {
            let count = 0;
            let total = Decimal(0);
            checkboxes.forEach(function(checkbox) {
                if (checkbox.checked) {
                    count++;
                    // Find the associated invoice item to get its balance_due
                    // This assumes the choice_label object has a balance_due attribute
                    // We need to access this data reliably. For now, let's parse from display if needed,
                    // or ideally store it in a data attribute.
                    // For simplicity, let's assume the balance is findable.
                    // A better way: add data-amount attribute to the checkbox input tag in the loop.
                    const listItem = checkbox.closest('.invoice-selection-item');
                    const amountText = listItem.querySelector('.invoice-amount').textContent;
                    // Simple parsing - remove currency symbol and commas
                    const balanceDue = Decimal(amountText.replace(currencySymbol, '').replace(/,/g, '') || 0);
                    total = total.plus(balanceDue);
                }
            });
            selectedCountEl.textContent = count;
            totalAmountEl.textContent = currencySymbol + total.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,'); // Add commas

            if (count > 0 && total.greaterThan(0)) {
                proceedButton.disabled = false;
            } else {
                proceedButton.disabled = true;
            }
        }

        checkboxes.forEach(function(checkbox) {
            checkbox.addEventListener('change', updateTotalSummary);
            // If using custom rendered checkboxes, ensure the label click also triggers change
            const label = checkbox.closest('label');
            if(label) {
                label.addEventListener('click', function(e) {
                    if (e.target !== checkbox) { // prevent double toggle if checkbox itself is clicked
                        // checkbox.checked = !checkbox.checked; // This might cause issues if checkbox is directly under label
                        // Manually trigger change if clicking label doesn't automatically do it for complex structures
                        // var event = new Event('change', { bubbles: true });
                        // checkbox.dispatchEvent(event);
                    }
                });
            }
        });
        updateTotalSummary(); // Initial calculation on page load
    });
    </script>
{% endblock %}




