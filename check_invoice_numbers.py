import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
from django_tenants.utils import get_tenant_model

# Get the tenant
TenantModel = get_tenant_model()
tenant = TenantModel.objects.get(schema_name='alpha')

# Switch to tenant schema
connection.set_tenant(tenant)

# Check invoice numbers directly in database
with connection.cursor() as cursor:
    cursor.execute("SELECT id, invoice_number, student_id FROM fees_invoice ORDER BY id;")
    results = cursor.fetchall()
    
    print(f'Total invoices in database: {len(results)}')
    for row in results:
        invoice_id, invoice_number, student_id = row
        print(f'ID: {invoice_id}, Number: "{invoice_number}", Student ID: {student_id}')
    
    # Check for empty invoice numbers
    cursor.execute("SELECT COUNT(*) FROM fees_invoice WHERE invoice_number = '';")
    empty_count = cursor.fetchone()[0]
    print(f'\nInvoices with empty invoice_number: {empty_count}')
    
    # Check for null invoice numbers  
    cursor.execute("SELECT COUNT(*) FROM fees_invoice WHERE invoice_number IS NULL;")
    null_count = cursor.fetchone()[0]
    print(f'Invoices with null invoice_number: {null_count}')
