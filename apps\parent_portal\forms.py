# apps/parent_portal/forms.py
from django import forms
from django.contrib.auth.forms import AuthenticationForm, PasswordChangeForm, PasswordResetForm, SetPasswordForm
from apps.students.models import ParentUser # Import ParentUser



class ParentLoginForm(AuthenticationForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['username'].label = "Your Email Address"
        self.fields['username'].widget.attrs.update({'placeholder': '<EMAIL>', 'class': 'form-control'})
        self.fields['password'].widget.attrs.update({'placeholder': 'Password', 'class': 'form-control'})

# class ParentLoginForm(AuthenticationForm):
#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)
#         self.fields['username'].label = "Your Email Address"
#         self.fields['username'].widget = forms.EmailInput(
#             attrs={'class': 'form-control form-control-lg mb-2', 'placeholder': '<EMAIL>', 'autocomplete': 'email'}
#         )
#         self.fields['password'].widget.attrs.update({'class': 'form-control form-control-lg', 'placeholder': 'Password', 'autocomplete': 'current-password'})


# For Parent Self-Registration (if implemented)
class ParentRegistrationForm(forms.ModelForm):
    password = forms.CharField(widget=forms.PasswordInput(attrs={'class': 'form-control form-control-lg', 'autocomplete': 'new-password'}), label="Password")
    password_confirm = forms.CharField(widget=forms.PasswordInput(attrs={'class': 'form-control form-control-lg', 'autocomplete': 'new-password'}), label="Confirm Password")
    # Add field for student linking code or similar verification
    # student_linking_code = forms.CharField(max_length=20, required=True, label="Student Linking Code")

    class Meta:
        model = ParentUser
        fields = ['email', 'first_name', 'last_name', 'phone_number', 'password', 'password_confirm'] # Add student_linking_code here
        widgets = {
            'email': forms.EmailInput(attrs={'class': 'form-control form-control-lg'}),
            'first_name': forms.TextInput(attrs={'class': 'form-control form-control-lg'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control form-control-lg'}),
            'phone_number': forms.TextInput(attrs={'class': 'form-control form-control-lg'}),
        }

    def clean_email(self):
        email = self.cleaned_data.get('email')
        # Check uniqueness within the current tenant (form needs tenant context or view handles)
        # For now, basic check. Tenant-specific check would be better in view.
        if ParentUser.objects.filter(email__iexact=email).exists():
            raise forms.ValidationError("An account with this email already exists.")
        return email
    
    def clean_password_confirm(self):
        pw1 = self.cleaned_data.get("password")
        pw2 = self.cleaned_data.get("password_confirm")
        if pw1 and pw2 and pw1 != pw2:
            raise forms.ValidationError("Passwords do not match.")
        return pw2

    def save(self, commit=True):
        user = super().save(commit=False)
        user.set_password(self.cleaned_data["password"])
        if commit:
            user.save()
        return user

class ParentProfileUpdateForm(forms.ModelForm): # Assuming this is what you had
    class Meta:
        model = ParentUser # <<<--- CORRECT USER MODEL ---<<<
        fields = ['first_name', 'last_name', 'phone_number', 
                'address_line1', 'city', 'profile_picture'] # Match fields in ParentUser
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'form-control'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control'}),
            'phone_number': forms.TextInput(attrs={'class': 'form-control'}),
            'address_line1': forms.TextInput(attrs={'class': 'form-control'}),
            'city': forms.TextInput(attrs={'class': 'form-control'}),
            'profile_picture': forms.ClearableFileInput(attrs={'class': 'form-control'}),
        }
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # If you want to make email read-only in the form:
        if 'email' in self.fields:
            self.fields['email'].widget.attrs['readonly'] = True
            self.fields['email'].widget.attrs['class'] = 'form-control' # Ensure styling
            self.fields['email'].help_text = "Email cannot be changed here. Contact support if needed."
        
        # You can add more custom styling or logic here if needed
        for field_name, field in self.fields.items():
            if not field.widget.attrs.get('class'): # Add form-control if not already set
                field.widget.attrs['class'] = 'form-control'
            field.widget.attrs['aria-describedby'] = f'{field_name}_help_text'


class ParentPasswordChangeForm(PasswordChangeForm):
    def __init__(self, user, *args, **kwargs):
        super().__init__(user, *args, **kwargs)
        for fieldname, field in self.fields.items():
            field.widget.attrs['class'] = 'form-control form-control-lg'
            field.help_text = '' # Remove default help text for cleaner look
            
            
            



# apps/parent_portal/forms.py
from django import forms
from django.utils.translation import gettext_lazy as _
from django.db.models import F, Q # Import Q
from decimal import Decimal

# Ensure correct import paths for your models
from apps.fees.models import Invoice
from apps.students.models import ParentUser, Student # Assuming ParentUser and Student are here

# Ensure INVOICE_MODEL_IMPORTED is defined (e.g., at module level or passed in)
# For simplicity, assuming Invoice model is always available here
# INVOICE_MODEL_IMPORTED = True # Or check via try-except for Invoice import

class SelectInvoicesForPaymentForm(forms.Form):
    selected_invoices = forms.ModelMultipleChoiceField(
        queryset=Invoice.objects.none(), # Populated dynamically
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-input'}),
        label=_("Select Invoices to Pay"),
        required=False # Will validate in view if at least one is selected
    )

    def __init__(self, *args, **kwargs):
        self.parent = kwargs.pop('parent', None) # Expect parent user to be passed
        super().__init__(*args, **kwargs)

        if not self.parent or not isinstance(self.parent, ParentUser):
            # No parent or invalid parent type, leave queryset empty or raise error
            self.fields['selected_invoices'].queryset = Invoice.objects.none()
            return

        try:
            children_pks = self.parent.children.filter(is_active=True).values_list('pk', flat=True)
            if children_pks:
                outstanding_statuses = [
                    Invoice.InvoiceStatus.SENT,
                    Invoice.InvoiceStatus.PARTIALLY_PAID,
                    Invoice.InvoiceStatus.OVERDUE
                ]
                # Query for invoices that have a balance due
                # balance_due = total_amount - amount_paid
                queryset = Invoice.objects.filter(
                    student_id__in=children_pks,
                    status__in=outstanding_statuses
                ).exclude( # Effectively where total_amount > amount_paid
                    Q(total_amount__lte=F('amount_paid')) 
                ).select_related('student').order_by('student__first_name', 'due_date')
                
                self.fields['selected_invoices'].queryset = queryset
            else:
                self.fields['selected_invoices'].queryset = Invoice.objects.none()
        except AttributeError: # If parent.children doesn't exist
            self.fields['selected_invoices'].queryset = Invoice.objects.none()
        except Exception as e: # Catch other potential errors
            # Log this error
            print(f"Error initializing SelectInvoicesForPaymentForm queryset: {e}")
            self.fields['selected_invoices'].queryset = Invoice.objects.none()

    def clean_selected_invoices(self):
        selected = self.cleaned_data.get('selected_invoices')
        if not selected:
            # This validation can also be done in the view more explicitly
            # raise forms.ValidationError(_("You must select at least one invoice to pay."))
            pass # View will handle if nothing is selected leading to zero total
        return selected
    
    
