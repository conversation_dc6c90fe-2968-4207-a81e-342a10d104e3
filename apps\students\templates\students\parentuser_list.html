{# D:\school_fees_saas_v2\apps\students\templates\students\parentuser_list.html #}
{% extends "tenant_base.html" %}

{% load static i18n humanize %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    {# Add any specific styles for this list if needed #}
{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="pagetitle d-flex justify-content-between align-items-center">
        <div>
            <h1>{{ view_title }}</h1>
            <nav>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">{% trans "Dashboard" %}</a></li>
                    <li class="breadcrumb-item active">{% trans "Parent Accounts" %}</li>
                </ol>
            </nav>
        </div>
        <div>
            {% if perms.students.add_parentuser %} {# Or your specific permission #}
            <a href="{% url 'students:parentuser_create' %}" class="btn btn-primary">
                <i class="bi bi-person-plus-fill me-1"></i> {% trans "Add New Parent" %}
            </a>
            {% endif %}
        </div>
    </div>

    {% include "partials/_messages.html" %}

    {# Optional: Filter Form #}
    {% if filter_form %}
        <div class="card shadow-sm mb-3">
            <div class="card-body">
                <form method="get" class="row g-3 align-items-end mt-1">
                    {% for field in filter_form %}
                        <div class="col-md-3 col-sm-6">
                            <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                            {{ field|add_class:"form-control form-control-sm" }}
                        </div>
                    {% endfor %}
                    <div class="col-md-3 col-sm-6">
                        <button type="submit" class="btn btn-sm btn-info w-100"><i class="bi bi-search"></i> {% trans "Filter" %}</button>
                    </div>
                    {% if request.GET %}
                    <div class="col-md-3 col-sm-6">
                        <a href="{% url 'students:parentuser_list' %}" class="btn btn-sm btn-outline-secondary w-100"><i class="bi bi-x-circle"></i> {% trans "Clear" %}</a>
                    </div>
                    {% endif %}
                </form>
            </div>
        </div>
    {% endif %}


    <div class="card shadow-sm">
        <div class="card-header">
            <h5 class="mb-0 py-1">{% trans "All Parent Accounts" %}</h5>
        </div>
        <div class="card-body p-0">
            {% if parents %}
                <div class="table-responsive">
                    <table class="table table-hover table-striped mb-0">
                        <thead>
                            <tr>
                                <th>{% trans "Full Name" %}</th>
                                <th>{% trans "Email" %}</th>
                                <th>{% trans "Phone" %}</th>
                                <th>{% trans "Linked Children" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for parent_obj in parents %} {# Renamed to parent_obj to avoid conflict with context 'parent' #}
                            <tr>
                                <td>
                                    {# Link to parent detail view if you create one #}
                                    {# <a href="{% url 'students:parentuser_detail' parent_obj.pk %}"> #}
                                        {{ parent_obj.get_full_name|default:parent_obj.email }}
                                    {# </a> #}
                                </td>
                                <td>{{ parent_obj.email }}</td>
                                <td>{{ parent_obj.phone_number|default:"N/A" }}</td>
                                <td>
                                    {% for child in parent_obj.children.all|slice:":2" %}
                                        <span class="badge bg-light text-dark border me-1">{{ child.get_full_name|truncatechars:15 }}</span>
                                    {% empty %}
                                        <span class="text-muted small">{% trans "None" %}</span>
                                    {% endfor %}
                                    {% if parent_obj.children.all.count > 2 %}
                                        <span class="text-muted small"> +{{ parent_obj.children.all.count|add:"-2" }} more</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if parent_obj.is_active %}
                                        <span class="badge bg-success">{% trans "Active" %}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                                    {% endif %}
                                </td>
                                <td class="text-nowrap">
                                    {# Link to parent detail view #}
                                    {# <a href="{% url 'students:parentuser_detail' parent_obj.pk %}" class="btn btn-sm btn-outline-info" title="View Details"><i class="bi bi-eye-fill"></i></a> #}
                                    {% if perms.students.change_parentuser %}
                                    <a href="{% url 'students:parentuser_update' parent_obj.pk %}" class="btn btn-sm btn-outline-secondary mx-1" title="Edit Parent"><i class="bi bi-pencil-square"></i></a>
                                    {% endif %}
                                    {# Add delete button if applicable, with confirmation #}
                                    {% if perms.students.delete_parentuser %}
                                    <a href="#" class="btn btn-sm btn-outline-danger" title="Delete Parent"><i class="bi bi-trash"></i></a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <p class="text-center text-muted p-4">{% trans "No parent accounts found." %} 
                    {% if perms.students.add_parentuser %}
                    <a href="{% url 'students:parentuser_create' %}">{% trans "Add the first one?" %}</a>
                    {% endif %}
                </p>
            {% endif %}
        </div>
        {% if is_paginated %}
        <div class="card-footer">
            {% include "partials/_pagination.html" with page_obj=page_obj filter_params=filter_params %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock tenant_specific_content %}

