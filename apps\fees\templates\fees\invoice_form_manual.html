{# D:\school_fees_saas_V2\templates\fees\invoice_form_manual.html #}
{% extends "tenant_base.html" %}
{% load core_tags humanize widget_tweaks %}

{% block title %}{{ view_title }}{% endblock %}

{% block page_specific_css %}
    {{ block.super }}
    <style>
        .formset-row:last-of-type {
            border-bottom: none !important;
        }
        .form-control-sm.text-end { text-align: right; }
        .delete-marker {
            padding-top: 1.75rem; /* Align delete checkbox with form fields */
        }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <h1>{{ view_title }}</h1>

    <form method="post" id="invoiceFormManual">
        {% csrf_token %}

        {# --- CARD 1: INVOICE HEADER (Main Form) --- #}
        <div class="card shadow-sm mb-4">
            <div class="card-header">
                Invoice Header
            </div>
            <div class="card-body">
                {# Display any non-field errors from the main form #}
                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}{{ error }}<br>{% endfor %}
                    </div>
                {% endif %}

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="{{ form.student.id_for_label }}" class="form-label">{{ form.student.label }}</label>
                        {% render_field form.student class+=" form-select form-select-sm" %}
                        {% if form.student.errors %}<div class="invalid-feedback d-block">{{ form.student.errors|join:", " }}</div>{% endif %}
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="{{ form.academic_year.id_for_label }}" class="form-label">{{ form.academic_year.label }}</label>
                        {% render_field form.academic_year class+=" form-select form-select-sm" %}
                        {% if form.academic_year.errors %}<div class="invalid-feedback d-block">{{ form.academic_year.errors|join:", " }}</div>{% endif %}
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="{{ form.term.id_for_label }}" class="form-label">{{ form.term.label }}</label>
                        {% render_field form.term class+=" form-select form-select-sm" %}
                        <small class="form-text text-muted">{{ form.term.help_text }}</small>
                        {% if form.term.errors %}<div class="invalid-feedback d-block">{{ form.term.errors|join:", " }}</div>{% endif %}
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="{{ form.fee_structure.id_for_label }}" class="form-label">{{ form.fee_structure.label }}</label>
                        {% render_field form.fee_structure class+=" form-select form-select-sm" %}
                        <small class="form-text text-muted">{{ form.fee_structure.help_text }}</small>
                        {% if form.fee_structure.errors %}<div class="invalid-feedback d-block">{{ form.fee_structure.errors|join:", " }}</div>{% endif %}
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="{{ form.issue_date.id_for_label }}" class="form-label">{{ form.issue_date.label }}</label>
                        {% render_field form.issue_date class+=" form-control form-control-sm" type="date" %}
                        {% if form.issue_date.errors %}<div class="invalid-feedback d-block">{{ form.issue_date.errors|join:", " }}</div>{% endif %}
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="{{ form.due_date.id_for_label }}" class="form-label">{{ form.due_date.label }}</label>
                        {% render_field form.due_date class+=" form-control form-control-sm" type="date" %}
                        {% if form.due_date.errors %}<div class="invalid-feedback d-block">{{ form.due_date.errors|join:", " }}</div>{% endif %}
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="{{ form.status.id_for_label }}" class="form-label">{{ form.status.label }}</label>
                        {% render_field form.status class+=" form-select form-select-sm" %}
                        {% if form.status.errors %}<div class="invalid-feedback d-block">{{ form.status.errors|join:", " }}</div>{% endif %}
                    </div>
                </div>
                <div class="mb-3">
                    <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                    {% render_field form.notes class+=" form-control form-control-sm" rows="3" %}
                    {% if form.notes.errors %}<div class="invalid-feedback d-block">{{ form.notes.errors|join:", " }}</div>{% endif %}
                </div>
                <div class="mb-3">
                    <label for="{{ form.internal_notes.id_for_label }}" class="form-label">{{ form.internal_notes.label }}</label>
                    {% render_field form.internal_notes class+=" form-control form-control-sm" rows="3" %}
                    {% if form.internal_notes.errors %}<div class="invalid-feedback d-block">{{ form.internal_notes.errors|join:", " }}</div>{% endif %}
                </div>
            </div>
        </div>

        {# --- CARD 2: INVOICE ITEMS (Formset) --- #}
        <div class="card shadow-sm mb-4">
            <div class="card-header">
                Invoice Items
            </div>
            <div class="card-body">
                {{ detail_formset.management_form }}
                {% if detail_formset.non_form_errors %}
                    <div class="alert alert-danger">
                        {% for error in detail_formset.non_form_errors %}{{ error }}<br>{% endfor %}
                    </div>
                {% endif %}

                <div id="invoice-item-formset">
                    {% for item_form in detail_formset %}
                        <div class="formset-row p-2 mb-2 border-bottom">
                            {{ item_form.id }}
                            {% if item_form.non_field_errors %}<div class="alert alert-danger p-2 mb-2">{% for error in item_form.non_field_errors %} {{ error }} {% endfor %}</div>{% endif %}
                            
                            <div class="row align-items-start g-2">
                                <div class="col-md-2">
                                    <label class="form-label small mb-0">{{ item_form.line_type.label }}</label>
                                    {% render_field item_form.line_type class+="form-select form-select-sm" %}
                                    {% if item_form.line_type.errors %}<div class="text-danger small mt-1">{{ item_form.line_type.errors|join:", " }}</div>{% endif %}
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label small mb-0">{{ item_form.fee_head.label }}</label>
                                    {% render_field item_form.fee_head class+="form-select form-select-sm" %}
                                    {% if item_form.fee_head.errors %}<div class="text-danger small mt-1">{{ item_form.fee_head.errors|join:", " }}</div>{% endif %}
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label small mb-0">{{ item_form.concession_type.label }}</label>
                                    {% render_field item_form.concession_type class+="form-select form-select-sm" %}
                                    {% if item_form.concession_type.errors %}<div class="text-danger small mt-1">{{ item_form.concession_type.errors|join:", " }}</div>{% endif %}
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label small mb-0">{{ item_form.description.label }}</label>
                                    {% render_field item_form.description class+="form-control form-control-sm" %}
                                    {% if item_form.description.errors %}<div class="text-danger small mt-1">{{ item_form.description.errors|join:", " }}</div>{% endif %}
                                </div>
                                <div class="col-md-1">
                                    <label class="form-label small mb-0">{{ item_form.quantity.label }}</label>
                                    {% render_field item_form.quantity class+="form-control form-control-sm text-end" %}
                                    {% if item_form.quantity.errors %}<div class="text-danger small mt-1">{{ item_form.quantity.errors|join:", " }}</div>{% endif %}
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label small mb-0">{{ item_form.unit_price.label }}</label>
                                    {% render_field item_form.unit_price class+="form-control form-control-sm text-end" %}
                                    {% if item_form.unit_price.errors %}<div class="text-danger small mt-1">{{ item_form.unit_price.errors|join:", " }}</div>{% endif %}
                                </div>
                                <div class="col-md-1 d-flex justify-content-center delete-marker">
                                     {% if detail_formset.can_delete and item_form.instance.pk %}
                                        <div class="form-check">
                                            {% render_field item_form.DELETE class+="form-check-input" %}
                                            <label for="{{ item_form.DELETE.id_for_label }}" class="form-check-label small text-danger">Del</label>
                                        </div>
                                     {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
                <button type="button" id="add-invoice-item" class="btn btn-outline-primary btn-sm mt-2">Add Another Item</button>
            </div>
        </div>

        <div class="mt-3">
            <button type="submit" class="btn btn-success">Save Invoice</button>
            <a href="{% url 'fees:invoice_list' %}" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
</div>

{# Hidden template for new formset rows (wrapped in a div for easier JS handling) #}
<div id="empty-form-template" style="display: none;">
    <div class="formset-row p-2 mb-2 border-bottom">
        {{ detail_formset.empty_form.id }}
        <div class="row align-items-start g-2">
            <div class="col-md-2">
                <label class="form-label small mb-0">{{ detail_formset.empty_form.line_type.label }}</label>
                {{ detail_formset.empty_form.line_type|add_class:"form-select form-select-sm" }}
            </div>
            <div class="col-md-2">
                <label class="form-label small mb-0">{{ detail_formset.empty_form.fee_head.label }}</label>
                {{ detail_formset.empty_form.fee_head|add_class:"form-select form-select-sm" }}
            </div>
            <div class="col-md-2">
                <label class="form-label small mb-0">{{ detail_formset.empty_form.concession_type.label }}</label>
                {{ detail_formset.empty_form.concession_type|add_class:"form-select form-select-sm" }}
            </div>
            <div class="col-md-2">
                <label class="form-label small mb-0">{{ detail_formset.empty_form.description.label }}</label>
                {{ detail_formset.empty_form.description|add_class:"form-control form-control-sm" }}
            </div>
            <div class="col-md-1">
                <label class="form-label small mb-0">{{ detail_formset.empty_form.quantity.label }}</label>
                {{ detail_formset.empty_form.quantity|add_class:"form-control form-control-sm text-end" }}
            </div>
            <div class="col-md-2">
                <label class="form-label small mb-0">{{ detail_formset.empty_form.unit_price.label }}</label>
                {{ detail_formset.empty_form.unit_price|add_class:"form-control form-control-sm text-end" }}
            </div>
            <div class="col-md-1 d-flex justify-content-center delete-marker"></div>
        </div>
    </div>
</div>
{% endblock %}

{# Add this to the bottom of fees/invoice_form_manual.html #}
{% block page_specific_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // --- JavaScript for "Add Another Item" button (from before) ---
    const addButton = document.getElementById('add-invoice-item');
    const formsetContainer = document.getElementById('invoice-item-formset');
    const totalFormsInput = document.querySelector('input[name="details-TOTAL_FORMS"]');
    const emptyFormTemplate = document.getElementById('empty-form-template').innerHTML;

    addButton.addEventListener('click', function() {
        let formNum = parseInt(totalFormsInput.value);
        let newFormHtml = emptyFormTemplate.replace(/__prefix__/g, formNum);
        let newFormDiv = document.createElement('div');
        newFormDiv.innerHTML = newFormHtml;
        formsetContainer.appendChild(newFormDiv.firstElementChild);
        totalFormsInput.value = formNum + 1;
        // After adding a new form, immediately run our toggle logic on it
        toggleLineItemFields(formsetContainer.lastElementChild);
    });

    // --- ENHANCED DYNAMIC FORM LOGIC ---
    function toggleLineItemFields(row) {
        const lineTypeSelect = row.querySelector('select[name$="-line_type"]');
        const feeHeadField = row.querySelector('select[name$="-fee_head"]').closest('.col-md-2');
        const concessionTypeField = row.querySelector('select[name$="-concession_type"]').closest('.col-md-2');
        const unitPriceInput = row.querySelector('input[name$="-unit_price"]'); // Get the input
        
        if (!lineTypeSelect) return;

        const selectedType = lineTypeSelect.value;

        if (selectedType === 'FEE_ITEM') {
            feeHeadField.style.display = 'block';
            concessionTypeField.style.display = 'none';
            concessionTypeField.querySelector('select').value = '';

            // --- ENHANCED: Set placeholder for a Fee Item ---
            unitPriceInput.placeholder = 'e.g., 500.00';

        } else if (selectedType === 'CONCESSION_ITEM') {
            feeHeadField.style.display = 'none';
            feeHeadField.querySelector('select').value = '';
            concessionTypeField.style.display = 'block';

            // --- ENHANCED: Set placeholder for a Concession ---
            unitPriceInput.placeholder = 'e.g., -50.00 (MUST BE NEGATIVE)';

        } else { // If 'Select a line type...' is chosen
            feeHeadField.style.display = 'none';
            concessionTypeField.style.display = 'none';
            unitPriceInput.placeholder = ''; // Clear placeholder
        }
    }

    // Run the logic for all existing forms when the page first loads
    document.querySelectorAll('.formset-row').forEach(row => {
        toggleLineItemFields(row);
    });

    // Add event listener to catch changes from any row
    formsetContainer.addEventListener('change', function(event) {
        if (event.target && event.target.matches('select[name$="-line_type"]')) {
            const row = event.target.closest('.formset-row');
            toggleLineItemFields(row);
        }
    });
});
</script>
{% endblock %}


















{% comment %} {# D:\school_fees_saas_V2\templates\fees\invoice_form_manual.html #}
{% extends "tenant_base.html" %}
{% load core_tags humanize widget_tweaks %}

{% block title %}{{ view_title }}{% endblock %}

{% block page_specific_css %}
    {{ block.super }}
    <style>
        .formset-row:last-child {
            border-bottom: none !important;
        }
        .form-control-sm.text-end { text-align: right; }
        .formset-row .delete-marker {
            padding-top: 2rem; /* Align delete checkbox with form fields */
        }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <h1>{{ view_title }}</h1>

    <form method="post" id="invoiceFormManual">
        {% csrf_token %}

        {# --- CARD 1: INVOICE HEADER (Main Form) --- #}
        <div class="card shadow-sm mb-4">
            <div class="card-header">
                Invoice Header
            </div>
            <div class="card-body">
                {# Display any non-field errors from the main form #}
                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}{{ error }}<br>{% endfor %}
                    </div>
                {% endif %}

                {# Your existing main form rendering is good, no changes needed here #}
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="{{ form.student.id_for_label }}" class="form-label">{{ form.student.label }}</label>
                        {% render_field form.student class+=" form-select form-select-sm" %}
                        {% if form.student.errors %}<div class="invalid-feedback d-block">{{ form.student.errors|join:", " }}</div>{% endif %}
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="{{ form.academic_year.id_for_label }}" class="form-label">{{ form.academic_year.label }}</label>
                        {% render_field form.academic_year class+=" form-select form-select-sm" %}
                        {% if form.academic_year.errors %}<div class="invalid-feedback d-block">{{ form.academic_year.errors|join:", " }}</div>{% endif %}
                    </div>
                </div>
                <!-- ... your other main form fields ... -->
            </div>
        </div>

        {# --- CARD 2: INVOICE ITEMS (Formset) --- #}
        <div class="card shadow-sm mb-4">
            <div class="card-header">
                Invoice Items
            </div>
            <div class="card-body">
                {{ detail_formset.management_form }}
                
                {# Display any errors that apply to the formset as a whole #}
                {% if detail_formset.non_form_errors %}
                    <div class="alert alert-danger">
                        {% for error in detail_formset.non_form_errors %}{{ error }}<br>{% endfor %}
                    </div>
                {% endif %}

                <div id="invoice-item-formset">
                    {% for item_form in detail_formset %}
                        <div class="formset-row p-2 mb-2 border-bottom">
                            {{ item_form.id }} {# Hidden field for existing item PKs #}
                            
                            {% if item_form.non_field_errors %}
                                <div class="alert alert-danger p-2 mb-2">{% for error in item_form.non_field_errors %} {{ error }} {% endfor %}</div>
                            {% endif %}
                            
                            <div class="row align-items-start g-2">
                                {# ========= THIS IS THE UPDATED & CORRECTED ROW STRUCTURE ========= #}
                                <div class="col-md-2">
                                    <label class="form-label small mb-0">{{ item_form.line_type.label }}</label>
                                    {% render_field item_form.line_type class+="form-select form-select-sm" %}
                                    {% if item_form.line_type.errors %}
                                        <div class="text-danger small mt-1">{{ item_form.line_type.errors|join:", " }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label small mb-0">{{ item_form.fee_head.label }}</label>
                                    {% render_field item_form.fee_head class+="form-select form-select-sm" %}
                                    {% if item_form.fee_head.errors %}<div class="text-danger small mt-1">{{ item_form.fee_head.errors|join:", " }}</div>{% endif %}
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label small mb-0">{{ item_form.concession_type.label }}</label>
                                    {% render_field item_form.concession_type class+="form-select form-select-sm" %}
                                    {% if item_form.concession_type.errors %}<div class="text-danger small mt-1">{{ item_form.concession_type.errors|join:", " }}</div>{% endif %}
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label small mb-0">{{ item_form.description.label }}</label>
                                    {% render_field item_form.description class+="form-control form-control-sm" %}
                                    {% if item_form.description.errors %}<div class="text-danger small mt-1">{{ item_form.description.errors|join:", " }}</div>{% endif %}
                                </div>
                                <div class="col-md-1">
                                    <label class="form-label small mb-0">{{ item_form.quantity.label }}</label>
                                    {% render_field item_form.quantity class+="form-control form-control-sm text-end" %}
                                    {% if item_form.quantity.errors %}<div class="text-danger small mt-1">{{ item_form.quantity.errors|join:", " }}</div>{% endif %}
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label small mb-0">{{ item_form.unit_price.label }}</label>
                                    {% render_field item_form.unit_price class+="form-control form-control-sm text-end" %}
                                    {% if item_form.unit_price.errors %}<div class="text-danger small mt-1">{{ item_form.unit_price.errors|join:", " }}</div>{% endif %}
                                </div>
                                {# Hidden 'applies_to_line' for JS to handle, or show it if needed #}
                                <div class="col-md-1 d-flex justify-content-center delete-marker">
                                    {% if detail_formset.can_delete and item_form.instance.pk %}
                                        <div class="form-check">
                                            {% render_field item_form.DELETE class+="form-check-input" %}
                                            <label for="{{ item_form.DELETE.id_for_label }}" class="form-check-label small text-danger">Del</label>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
                <button type="button" id="add-invoice-item" class="btn btn-outline-primary btn-sm mt-2">Add Another Item</button>
            </div>
        </div>

        <div class="mt-3">
            <button type="submit" class="btn btn-success">Save Invoice</button>
            <a href="{% url 'fees:invoice_list' %}" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
</div>

{# Hidden template for new formset rows (this is the robust way to do it) #}
<div id="empty-form-template" style="display: none;">
    <div class="formset-row p-2 mb-2 border-bottom">
        {{ detail_formset.empty_form.id }}
        <div class="row align-items-start g-2">
            <div class="col-md-2">
                <label class="form-label small mb-0">{{ detail_formset.empty_form.line_type.label }}</label>
                {{ detail_formset.empty_form.line_type|add_class:"form-select form-select-sm" }}
            </div>
            <div class="col-md-2">
                <label class="form-label small mb-0">{{ detail_formset.empty_form.fee_head.label }}</label>
                {{ detail_formset.empty_form.fee_head|add_class:"form-select form-select-sm" }}
            </div>
            <div class="col-md-2">
                <label class="form-label small mb-0">{{ detail_formset.empty_form.concession_type.label }}</label>
                {{ detail_formset.empty_form.concession_type|add_class:"form-select form-select-sm" }}
            </div>
            <div class="col-md-2">
                <label class="form-label small mb-0">{{ detail_formset.empty_form.description.label }}</label>
                {{ detail_formset.empty_form.description|add_class:"form-control form-control-sm" }}
            </div>
            <div class="col-md-1">
                <label class="form-label small mb-0">{{ detail_formset.empty_form.quantity.label }}</label>
                {{ detail_formset.empty_form.quantity|add_class:"form-control form-control-sm text-end" }}
            </div>
            <div class="col-md-2">
                <label class="form-label small mb-0">{{ detail_formset.empty_form.unit_price.label }}</label>
                {{ detail_formset.empty_form.unit_price|add_class:"form-control form-control-sm text-end" }}
            </div>
            <div class="col-md-1 d-flex justify-content-center delete-marker">
                {# New forms don't have a delete box initially #}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block page_specific_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const addButton = document.getElementById('add-invoice-item');
    const formsetContainer = document.getElementById('invoice-item-formset');
    const totalFormsInput = document.querySelector('input[name="details-TOTAL_FORMS"]');
    const emptyFormTemplate = document.getElementById('empty-form-template').innerHTML;

    addButton.addEventListener('click', function() {
        let formNum = parseInt(totalFormsInput.value);
        
        // Replace the '__prefix__' placeholder with the new form number
        let newFormHtml = emptyFormTemplate.replace(/__prefix__/g, formNum);
        
        // Create a new div and add the new form's HTML to it
        let newFormDiv = document.createElement('div');
        newFormDiv.innerHTML = newFormHtml;
        
        // Append the new form to the container
        formsetContainer.appendChild(newFormDiv.firstElementChild);
        
        // Increment the total forms count
        totalFormsInput.value = formNum + 1;
    });
});
</script>
{% endblock %} {% endcomment %}





















{% comment %} {# D:\school_fees_saas_V2\templates\fees\invoice_form_manual.html #}
{% extends "tenant_base.html" %}
{% load core_tags humanize widget_tweaks %} {# Load widget_tweaks if using it for formset #}

{% block title %}{{ view_title }}{% endblock %}

{% block page_specific_css %}
    {{ block.super }}
    <style>
        .formset-row {
            padding-bottom: 10px;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .formset-row:last-child {
            border-bottom: none;
        }
        .formset-item-field {
            margin-bottom: 0.5rem !important; /* Reduce bottom margin for inline items */
        }
        .form-control-sm.text-end {
            text-align: right;
        }
    </style>
{% endblock %}


{% block tenant_specific_content %}
<div class="container mt-4">
    <h1>{{ view_title }}</h1>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %}" role="alert">
                {{ message }}
            </div>
        {% endfor %}
    {% endif %}

    <form method="post" id="invoiceFormManual">
        {% csrf_token %}

        <div class="card shadow-sm mb-4">
            <div class="card-header">
                Invoice Header
            </div>
            <div class="card-body">
                {# Render main form fields - you can use as_p or render manually #}
                {# Example using manual rendering with Bootstrap structure #}
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="{{ form.student.id_for_label }}" class="form-label">{{ form.student.label }}</label>
                        {% render_field form.student class+=" form-select form-select-sm" %}
                        {% if form.student.errors %}<div class="invalid-feedback d-block">{{ form.student.errors|join:", " }}</div>{% endif %}
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="{{ form.academic_year.id_for_label }}" class="form-label">{{ form.academic_year.label }}</label>
                        {% render_field form.academic_year class+=" form-select form-select-sm" %}
                        {% if form.academic_year.errors %}<div class="invalid-feedback d-block">{{ form.academic_year.errors|join:", " }}</div>{% endif %}
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="{{ form.term.id_for_label }}" class="form-label">{{ form.term.label }}</label>
                        {% render_field form.term class+=" form-select form-select-sm" %}
                        <small class="form-text text-muted">{{ form.term.help_text }}</small>
                        {% if form.term.errors %}<div class="invalid-feedback d-block">{{ form.term.errors|join:", " }}</div>{% endif %}
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="{{ form.fee_structure.id_for_label }}" class="form-label">{{ form.fee_structure.label }}</label>
                        {% render_field form.fee_structure class+=" form-select form-select-sm" %}
                        <small class="form-text text-muted">{{ form.fee_structure.help_text }}</small>
                        {% if form.fee_structure.errors %}<div class="invalid-feedback d-block">{{ form.fee_structure.errors|join:", " }}</div>{% endif %}
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="{{ form.issue_date.id_for_label }}" class="form-label">{{ form.issue_date.label }}</label>
                        {% render_field form.issue_date class+=" form-control form-control-sm" type="date" %}
                        {% if form.issue_date.errors %}<div class="invalid-feedback d-block">{{ form.issue_date.errors|join:", " }}</div>{% endif %}
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="{{ form.due_date.id_for_label }}" class="form-label">{{ form.due_date.label }}</label>
                        {% render_field form.due_date class+=" form-control form-control-sm" type="date" %}
                        {% if form.due_date.errors %}<div class="invalid-feedback d-block">{{ form.due_date.errors|join:", " }}</div>{% endif %}
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="{{ form.status.id_for_label }}" class="form-label">{{ form.status.label }}</label>
                        {% render_field form.status class+=" form-select form-select-sm" %}
                        {% if form.status.errors %}<div class="invalid-feedback d-block">{{ form.status.errors|join:", " }}</div>{% endif %}
                    </div>
                </div>
                <div class="mb-3">
                    <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                    {% render_field form.notes class+=" form-control form-control-sm" rows="3" %}
                    {% if form.notes.errors %}<div class="invalid-feedback d-block">{{ form.notes.errors|join:", " }}</div>{% endif %}
                </div>
                <div class="mb-3">
                    <label for="{{ form.internal_notes.id_for_label }}" class="form-label">{{ form.internal_notes.label }}</label>
                    {% render_field form.internal_notes class+=" form-control form-control-sm" rows="3" %}
                    {% if form.internal_notes.errors %}<div class="invalid-feedback d-block">{{ form.internal_notes.errors|join:", " }}</div>{% endif %}
                </div>

                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}<br>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
        </div>

        <div class="card shadow-sm mb-4">
            <div class="card-header">
                Invoice Items
            </div>
            <div class="card-body">
                {{ detail_formset.management_form }}
                {% if detail_formset.non_form_errors %}
                    <div class="alert alert-danger">
                        {% for error in detail_formset.non_form_errors %}
                            {{ error }}<br>
                        {% endfor %}
                    </div>
                {% endif %}

                <div id="invoice-item-formset">
                    {% for item_form in detail_formset %}
                        <div class="formset-row p-2 mb-2 border rounded">
                            {% if item_form.non_field_errors %}
                                <div class="alert alert-danger mb-2 p-1">
                                {% for error in item_form.non_field_errors %} {{ error }} {% endfor %}
                                </div>
                            {% endif %}
                            <div class="row align-items-center g-2">
                                <div class="col-md-3 formset-item-field">
                                    <label for="{{ item_form.fee_head.id_for_label }}" class="form-label small mb-0">{{ item_form.fee_head.label }}</label>
                                    {% render_field item_form.fee_head class+="form-select form-select-sm" %}
                                    {% if item_form.fee_head.errors %}<div class="invalid-feedback d-block">{{ item_form.fee_head.errors|join:", " }}</div>{% endif %}
                                </div>
                                <div class="col-md-3 formset-item-field">
                                    <label for="{{ item_form.description.id_for_label }}" class="form-label small mb-0">{{ item_form.description.label }}</label>
                                    {% render_field item_form.description class+="form-control form-control-sm" %}
                                    {% if item_form.description.errors %}<div class="invalid-feedback d-block">{{ item_form.description.errors|join:", " }}</div>{% endif %}
                                </div>
                                <div class="col-md-1 formset-item-field">
                                    <label for="{{ item_form.quantity.id_for_label }}" class="form-label small mb-0">{{ item_form.quantity.label }}</label>
                                    {% render_field item_form.quantity class+="form-control form-control-sm text-end" %}
                                    {% if item_form.quantity.errors %}<div class="invalid-feedback d-block">{{ item_form.quantity.errors|join:", " }}</div>{% endif %}
                                </div>
                                <div class="col-md-2 formset-item-field">
                                    <label for="{{ item_form.unit_price.id_for_label }}" class="form-label small mb-0">{{ item_form.unit_price.label }}</label>
                                    {% render_field item_form.unit_price class+="form-control form-control-sm text-end" %}
                                    {% if item_form.unit_price.errors %}<div class="invalid-feedback d-block">{{ item_form.unit_price.errors|join:", " }}</div>{% endif %}
                                </div>
                                <div class="col-md-2 formset-item-field">
                                    <label for="{{ item_form.concession_type_applied.id_for_label }}" class="form-label small mb-0">{{ item_form.concession_type_applied.label }}</label>
                                    {% render_field item_form.concession_type_applied class+="form-select form-select-sm" %}
                                    {% if item_form.concession_type_applied.errors %}<div class="invalid-feedback d-block">{{ item_form.concession_type_applied.errors|join:", " }}</div>{% endif %}
                                </div>
                                <div class="col-md-1 pt-3 d-flex align-items-center">
                                    <div class="form-check me-2">
                                        {% render_field item_form.is_concession class+="form-check-input" %}
                                        <label for="{{ item_form.is_concession.id_for_label }}" class="form-check-label small">{{ item_form.is_concession.label }}</label>
                                    </div>
                                    {% if detail_formset.can_delete and item_form.instance.pk %}
                                    <div class="form-check">
                                        {% render_field item_form.DELETE class+="form-check-input" %}
                                        <label for="{{ item_form.DELETE.id_for_label }}" class="form-check-label small text-danger">Delete</label>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            {{ item_form.id }} {# Hidden field for item ID if updating #}
                        </div>
                    {% endfor %}
                </div>
                <button type="button" id="add-invoice-item" class="btn btn-outline-primary btn-sm mt-2">Add Another Item</button>
            </div>
        </div>

        <div class="mt-3">
            <button type="submit" class="btn btn-success">Save Invoice</button>
            <a href="{% url 'fees:invoice_list' %}" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
</div>

{# JavaScript for adding more formset forms #}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const formsetContainer = document.getElementById('invoice-item-formset');
    const addButton = document.getElementById('add-invoice-item');
    const totalFormsInput = document.querySelector('input[name="details-TOTAL_FORMS"]');
    const emptyFormHtml = `{{ detail_formset.empty_form.as_p|escapejs }}`; // Get empty form HTML
    // This is a basic way to get empty_form. A more robust way involves a script tag template.
    // For now, let's assume a simplified version or manually construct it if as_p doesn't work well.

    // More robust empty form:
    let emptyFormTemplate = null;
    const formsetRows = formsetContainer.querySelectorAll('.formset-row');
    if (formsetRows.length > 0) {
        // Try to get the first form's structure (might not be perfect if complex)
        // A hidden template form is better.
        // For simplicity, we'll assume as_p works for now or adjust.
    }


    addButton.addEventListener('click', function() {
        let formNum = parseInt(totalFormsInput.value);
        
        // Create a div for the new form row
        let newFormRow = document.createElement('div');
        newFormRow.classList.add('formset-row', 'p-2', 'mb-2', 'border', 'rounded');
        
        // Basic structure assuming simple fields for now
        // Replace __prefix__ with the new form number
        // This is very basic and needs to match your FeeStructureItemForm fields structure
        // Using as_p directly into JS is problematic. We need to replicate the HTML structure.
        // Example of HTML structure for a single field:
        // <div class="col-md-3 formset-item-field">
        //  <label for="id_details-__prefix__-fee_head" class="form-label small mb-0">Fee/Item:</label>
        //  <select name="details-__prefix__-fee_head" class="form-select form-select-sm" id="id_details-__prefix__-fee_head">...</select>
        // </div>
        // This becomes complex to generate fully in JS without a template.

        // --- Simplified for now: Alert user to save and re-edit for more items if JS is too complex ---
        // alert("Please save the current items. To add more, save and then edit the invoice.");
        // return;
        // --- OR if you want to try basic JS addition: ---

        if (!emptyFormTemplate) { // Try to find an empty form template if not already captured
             const emptyFormNode = document.getElementById('empty-form-template'); // You'd need to create this
            if (emptyFormNode) {
                emptyFormTemplate = emptyFormNode.innerHTML;
            }
        }

        if (emptyFormTemplate) {
            let newFormHtml = emptyFormTemplate.replace(/__prefix__/g, formNum);
            newFormRow.innerHTML = newFormHtml; // This assumes emptyFormTemplate is well-formed HTML for one row
            formsetContainer.appendChild(newFormRow);
            totalFormsInput.value = formNum + 1;
        } else {
            // Fallback if template not found - very basic, won't have dropdowns populated
            newFormRow.innerHTML = `
                <p>--- New Item ${formNum + 1} (Basic - Save and Edit for full options) ---</p>
                <input type="hidden" name="details-${formNum}-id" id="id_details-${formNum}-id">
                <p><label for="id_details-${formNum}-description">Description:</label> <input type="text" name="details-${formNum}-description" class="form-control form-control-sm" id="id_details-${formNum}-description"></p>
                <p><label for="id_details-${formNum}-quantity">Quantity:</label> <input type="number" name="details-${formNum}-quantity" value="1.00" step="0.01" class="form-control form-control-sm text-end" id="id_details-${formNum}-quantity"></p>
                <p><label for="id_details-${formNum}-unit_price">Unit price:</label> <input type="number" name="details-${formNum}-unit_price" step="0.01" class="form-control form-control-sm text-end" id="id_details-${formNum}-unit_price"></p>
                <p><input type="checkbox" name="details-${formNum}-DELETE" id="id_details-${formNum}-DELETE"> <label for="id_details-${formNum}-DELETE">Delete</label></p>
            `;
            formsetContainer.appendChild(newFormRow);
            totalFormsInput.value = formNum + 1;
            alert("Basic item added. Save and re-edit to select Fee Heads/Concessions for new items.");
        }
    });
});
</script>

{# Hidden template for new formset rows (better than empty_form.as_p in JS) #}
<template id="empty-form-template">
    <div class="formset-row p-2 mb-2 border rounded">
        <div class="row align-items-center g-2">
            <div class="col-md-3 formset-item-field">
                <label for="id_details-__prefix__-fee_head" class="form-label small mb-0">{{ detail_formset.empty_form.fee_head.label }}</label>
                {{ detail_formset.empty_form.fee_head|add_class:"form-select form-select-sm" }}
            </div>
            <div class="col-md-3 formset-item-field">
                <label for="id_details-__prefix__-description" class="form-label small mb-0">{{ detail_formset.empty_form.description.label }}</label>
                {{ detail_formset.empty_form.description|add_class:"form-control form-control-sm" }}
            </div>
            <div class="col-md-1 formset-item-field">
                <label for="id_details-__prefix__-quantity" class="form-label small mb-0">{{ detail_formset.empty_form.quantity.label }}</label>
                {{ detail_formset.empty_form.quantity|add_class:"form-control form-control-sm text-end" }}
            </div>
            <div class="col-md-2 formset-item-field">
                <label for="id_details-__prefix__-unit_price" class="form-label small mb-0">{{ detail_formset.empty_form.unit_price.label }}</label>
                {{ detail_formset.empty_form.unit_price|add_class:"form-control form-control-sm text-end" }}
            </div>
            <div class="col-md-2 formset-item-field">
                <label for="id_details-__prefix__-concession_type_applied" class="form-label small mb-0">{{ detail_formset.empty_form.concession_type_applied.label }}</label>
                {{ detail_formset.empty_form.concession_type_applied|add_class:"form-select form-select-sm" }}
            </div>
            <div class="col-md-1 pt-3 d-flex align-items-center">
                <div class="form-check me-2">
                    {{ detail_formset.empty_form.is_concession|add_class:"form-check-input" }}
                    <label for="id_details-__prefix__-is_concession" class="form-check-label small">{{ detail_formset.empty_form.is_concession.label }}</label>
                </div>
                {# DELETE checkbox will be handled by formset extra forms if can_delete_extra is True #}
                {# For existing forms, form.DELETE is rendered. For new, it's not needed unless can_delete_extra=True #}
            </div>
        </div>
        {{ detail_formset.empty_form.id }}
    </div>
</template>
{% endblock %} {% endcomment %}