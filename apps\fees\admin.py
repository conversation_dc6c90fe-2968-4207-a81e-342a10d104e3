# apps/fees/admin.py

from django.contrib import admin
from django.db.models import Sum
from .models import (
    FeeHead,
    FeeStructure,
    FeeStructureItem,
    StudentFeeAllocation,
    Invoice,
    InvoiceDetail,
    ConcessionType,
    StudentConcession,
)

@admin.register(FeeHead)
class FeeHeadAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'updated_at')
    search_fields = ('name',)

class FeeStructureItemInline(admin.TabularInline):
    model = FeeStructureItem
    extra = 1
    autocomplete_fields = ['fee_head']

@admin.register(FeeStructure)
class FeeStructureAdmin(admin.ModelAdmin):
    list_display = ('name', 'academic_year', 'term', 'get_total_amount', 'updated_at')
    list_filter = ('academic_year', 'term')
    search_fields = ('name', 'academic_year__name', 'term__name')
    list_select_related = ('academic_year', 'term')
    inlines = [FeeStructureItemInline]
    autocomplete_fields = ['academic_year', 'term']

    # FIXED: Added the missing method to calculate the total amount
    @admin.display(description='Total Amount')
    def get_total_amount(self, obj):
        # 'items' is the default related_name. If you set one, use that.
        total = obj.items.aggregate(total=Sum('amount'))['total']
        return total or 0.00

# ... (StudentFeeAllocationAdmin is fine) ...

@admin.register(StudentFeeAllocation)
class StudentFeeAllocationAdmin(admin.ModelAdmin):
    list_display = ('student', 'academic_year', 'fee_structure', 'created_at')
    list_filter = ('academic_year', 'fee_structure')
    autocomplete_fields = ['student', 'academic_year', 'fee_structure']
    list_select_related = ('student', 'academic_year', 'fee_structure')
    search_fields = ('student__first_name', 'student__last_name', 'student__admission_number', 'fee_structure__name', 'academic_year__name')


class InvoiceDetailInline(admin.TabularInline):
    model = InvoiceDetail
    extra = 0
    readonly_fields = ('fee_head', 'description', 'amount')
    can_delete = False

@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    # FIXED: list_display now uses the correct field/property names from your Invoice model
    list_display = ('invoice_number', 'student', 'issue_date', 'due_date', 'period_description', 'subtotal_amount', 'total_concession_amount', 'amount_paid', 'balance_due', 'status')
    list_filter = ('status', 'academic_year', 'term', 'issue_date', 'due_date')
    
    # FIXED: Removed 'period_description' from search_fields as it's not a database field
    search_fields = ('invoice_number', 'student__first_name', 'student__last_name', 'student__admission_number')
    
    # FIXED: Added calculated fields to readonly_fields to prevent direct editing
    readonly_fields = ('amount_paid', 'subtotal_amount', 'total_concession_amount', 'net_billable_amount', 'balance_due')
    
    # FIXED: Removed 'allocation' as the field is commented out in your model
    autocomplete_fields = ['student', 'academic_year', 'term']
    
    list_select_related = ('student', 'academic_year', 'term')
    inlines = [InvoiceDetailInline]

    # FIXED: Added the missing method for 'period_description'
    @admin.display(description='Period', ordering='academic_year')
    def period_description(self, obj):
        if obj.term:
            return f"{obj.term.name} ({obj.academic_year.name})"
        return obj.academic_year.name

    # The admin can directly access model properties, so we don't need to redefine them here.
    # The list_display items 'balance_due' and readonly_fields 'net_billable_amount'
    # will automatically call the @property methods on your Invoice model.

@admin.register(ConcessionType)
class ConcessionTypeAdmin(admin.ModelAdmin):
    # FIXED: This assumes your model has fields 'type' and 'value'.
    # Please check your ConcessionType model and adjust these names if they are different.
    list_display = ('name', 'type', 'value', 'is_active')
    list_filter = ('type', 'is_active')
    search_fields = ('name',)

# ... (StudentConcessionAdmin is fine) ...
@admin.register(StudentConcession)
class StudentConcessionAdmin(admin.ModelAdmin):
    list_display = ('student', 'academic_year', 'concession_type', 'granted_at')
    list_filter = ('academic_year', 'concession_type')
    autocomplete_fields = ['student', 'academic_year', 'concession_type']
    list_select_related = ('student', 'academic_year', 'concession_type')
    search_fields = ('student__first_name', 'student__last_name', 'student__admission_number', 'concession_type__name', 'academic_year__name')
    
    
    





